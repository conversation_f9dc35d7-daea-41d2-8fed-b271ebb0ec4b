# Network Graph Visualization

An interactive network graph visualization built with PixiJS featuring force-directed physics, circular layout, and comprehensive interaction controls.

## Features

- **Interactive Network Graph**: Nodes connected by edges with force-directed physics simulation
- **Circular Initial Layout**: Nodes are initially arranged in a circle for optimal viewing
- **Force-Directed Physics**: Realistic node movement with repulsion, attraction, and damping forces
- **Pan and Zoom**: 
  - <PERSON>ag to pan around the graph
  - Mouse wheel scroll to zoom in/out
  - Pinch-to-zoom support on touch devices
- **Node Interaction**: 
  - Drag individual nodes to reposition them
  - Hover effects with visual feedback
  - Color-coded node groups
- **Dark Mode**: Optimized dark theme for comfortable viewing
- **Multiple Data Sets**: Switch between different network types (Tech, Social, Biological)
- **Simulation Controls**: Start/stop physics simulation and center view

## Installation

1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm run dev
   ```
4. Open your browser to `http://localhost:5173`

## Usage

### Controls
- **Pan**: Click and drag on empty space to move around the graph
- **Zoom**: Use mouse wheel or pinch gesture to zoom in/out
- **Node Dragging**: Click and drag any node to reposition it
- **Data Sets**: Use the buttons in the control panel to switch between different network types
- **Simulation**: Start/stop the physics simulation or center the view

### Customization

The network graph can be customized by modifying the parameters in `NetworkGraph.js`:

- **Physics**: Adjust `damping`, `repulsion`, `attraction`, and `centerForce` values
- **Visual**: Change `nodeRadius`, `nodeColors`, `edgeColor`, and `backgroundColor`
- **Data**: Modify the dummy data generators in `dummyData.js`

## Technical Details

- **Framework**: Vanilla JavaScript with ES6 modules
- **Graphics**: PixiJS for high-performance 2D rendering
- **Build Tool**: Vite for fast development and building
- **Physics**: Custom force-directed algorithm with configurable parameters

## File Structure

```
├── index.html              # Main HTML file
├── package.json            # Project dependencies and scripts
├── src/
│   ├── main.js             # Application entry point
│   ├── NetworkGraph.js     # Main graph visualization class
│   └── dummyData.js        # Dummy data generators
└── README.md              # This file
```

## Browser Support

- Modern browsers with ES6 module support
- WebGL support recommended for optimal performance
- Touch devices supported for mobile interaction

## Future Enhancements (Phase 2)

- **Group Blobs**: Encircle node groups with blob shapes
- **Node Labels**: Display node names and additional information
- **Edge Weights**: Visual representation of connection strengths
- **Layout Algorithms**: Additional layout options (hierarchical, grid, etc.)
- **Export Features**: Save graph as image or data
- **Performance Optimization**: Level-of-detail rendering for large graphs
