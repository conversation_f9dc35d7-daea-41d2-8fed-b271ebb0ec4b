{"version": 3, "file": "AnimatedSprite.js", "sources": ["../src/AnimatedSprite.ts"], "sourcesContent": ["import { Texture, Ticker, UPDATE_PRIORITY } from '@pixi/core';\nimport { Sprite } from '@pixi/sprite';\n\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * An AnimatedSprite is a simple way to display an animation depicted by a list of textures.\n *\n * ```js\n * import { AnimatedSprite, Texture } from 'pixi.js';\n *\n * const alienImages = [\n *     'image_sequence_01.png',\n *     'image_sequence_02.png',\n *     'image_sequence_03.png',\n *     'image_sequence_04.png',\n * ];\n * const textureArray = [];\n *\n * for (let i = 0; i < 4; i++)\n * {\n *     const texture = Texture.from(alienImages[i]);\n *     textureArray.push(texture);\n * }\n *\n * const animatedSprite = new AnimatedSprite(textureArray);\n * ```\n *\n * The more efficient and simpler way to create an animated sprite is using a {@link PIXI.Spritesheet}\n * containing the animation definitions:\n * @example\n * import { AnimatedSprite, Assets } from 'pixi.js';\n *\n * const sheet = await Assets.load('assets/spritesheet.json');\n * animatedSprite = new AnimatedSprite(sheet.animations['image_sequence']);\n * @memberof PIXI\n */\nexport class AnimatedSprite extends Sprite\n{\n    /**\n     * The speed that the AnimatedSprite will play at. Higher is faster, lower is slower.\n     * @default 1\n     */\n    public animationSpeed: number;\n\n    /**\n     * Whether or not the animate sprite repeats after playing.\n     * @default true\n     */\n    public loop: boolean;\n\n    /**\n     * Update anchor to [Texture's defaultAnchor]{@link PIXI.Texture#defaultAnchor} when frame changes.\n     *\n     * Useful with [sprite sheet animations]{@link PIXI.Spritesheet#animations} created with tools.\n     * Changing anchor for each frame allows to pin sprite origin to certain moving feature\n     * of the frame (e.g. left foot).\n     *\n     * Note: Enabling this will override any previously set `anchor` on each frame change.\n     * @default false\n     */\n    public updateAnchor: boolean;\n\n    /**\n     * User-assigned function to call when an AnimatedSprite finishes playing.\n     * @example\n     * animation.onComplete = () => {\n     *     // Finished!\n     * };\n     */\n    public onComplete?: () => void;\n\n    /**\n     * User-assigned function to call when an AnimatedSprite changes which texture is being rendered.\n     * @example\n     * animation.onFrameChange = () => {\n     *     // Updated!\n     * };\n     */\n    public onFrameChange?: (currentFrame: number) => void;\n\n    /**\n     * User-assigned function to call when `loop` is true, and an AnimatedSprite is played and\n     * loops around to start again.\n     * @example\n     * animation.onLoop = () => {\n     *     // Looped!\n     * };\n     */\n    public onLoop?: () => void;\n\n    private _playing: boolean;\n    private _textures: Texture[];\n    private _durations: number[];\n\n    /**\n     * `true` uses PIXI.Ticker.shared to auto update animation time.\n     * @default true\n     */\n    private _autoUpdate: boolean;\n\n    /**\n     * `true` if the instance is currently connected to PIXI.Ticker.shared to auto update animation time.\n     * @default false\n     */\n    private _isConnectedToTicker: boolean;\n\n    /** Elapsed time since animation has been started, used internally to display current texture. */\n    private _currentTime: number;\n\n    /** The texture index that was displayed last time. */\n    private _previousFrame: number;\n\n    /**\n     * @param textures - An array of {@link PIXI.Texture} or frame\n     *  objects that make up the animation.\n     * @param {boolean} [autoUpdate=true] - Whether to use Ticker.shared to auto update animation time.\n     */\n    constructor(textures: Texture[] | FrameObject[], autoUpdate = true)\n    {\n        super(textures[0] instanceof Texture ? textures[0] : textures[0].texture);\n\n        this._textures = null;\n        this._durations = null;\n        this._autoUpdate = autoUpdate;\n        this._isConnectedToTicker = false;\n\n        this.animationSpeed = 1;\n        this.loop = true;\n        this.updateAnchor = false;\n        this.onComplete = null;\n        this.onFrameChange = null;\n        this.onLoop = null;\n\n        this._currentTime = 0;\n\n        this._playing = false;\n        this._previousFrame = null;\n\n        this.textures = textures;\n    }\n\n    /** Stops the AnimatedSprite. */\n    public stop(): void\n    {\n        if (!this._playing)\n        {\n            return;\n        }\n\n        this._playing = false;\n        if (this._autoUpdate && this._isConnectedToTicker)\n        {\n            Ticker.shared.remove(this.update, this);\n            this._isConnectedToTicker = false;\n        }\n    }\n\n    /** Plays the AnimatedSprite. */\n    public play(): void\n    {\n        if (this._playing)\n        {\n            return;\n        }\n\n        this._playing = true;\n        if (this._autoUpdate && !this._isConnectedToTicker)\n        {\n            Ticker.shared.add(this.update, this, UPDATE_PRIORITY.HIGH);\n            this._isConnectedToTicker = true;\n        }\n    }\n\n    /**\n     * Stops the AnimatedSprite and goes to a specific frame.\n     * @param frameNumber - Frame index to stop at.\n     */\n    public gotoAndStop(frameNumber: number): void\n    {\n        this.stop();\n        this.currentFrame = frameNumber;\n    }\n\n    /**\n     * Goes to a specific frame and begins playing the AnimatedSprite.\n     * @param frameNumber - Frame index to start at.\n     */\n    public gotoAndPlay(frameNumber: number): void\n    {\n        this.currentFrame = frameNumber;\n        this.play();\n    }\n\n    /**\n     * Updates the object transform for rendering.\n     * @param deltaTime - Time since last tick.\n     */\n    update(deltaTime: number): void\n    {\n        if (!this._playing)\n        {\n            return;\n        }\n\n        const elapsed = this.animationSpeed * deltaTime;\n        const previousFrame = this.currentFrame;\n\n        if (this._durations !== null)\n        {\n            let lag = this._currentTime % 1 * this._durations[this.currentFrame];\n\n            lag += elapsed / 60 * 1000;\n\n            while (lag < 0)\n            {\n                this._currentTime--;\n                lag += this._durations[this.currentFrame];\n            }\n\n            const sign = Math.sign(this.animationSpeed * deltaTime);\n\n            this._currentTime = Math.floor(this._currentTime);\n\n            while (lag >= this._durations[this.currentFrame])\n            {\n                lag -= this._durations[this.currentFrame] * sign;\n                this._currentTime += sign;\n            }\n\n            this._currentTime += lag / this._durations[this.currentFrame];\n        }\n        else\n        {\n            this._currentTime += elapsed;\n        }\n\n        if (this._currentTime < 0 && !this.loop)\n        {\n            this.gotoAndStop(0);\n\n            if (this.onComplete)\n            {\n                this.onComplete();\n            }\n        }\n        else if (this._currentTime >= this._textures.length && !this.loop)\n        {\n            this.gotoAndStop(this._textures.length - 1);\n\n            if (this.onComplete)\n            {\n                this.onComplete();\n            }\n        }\n        else if (previousFrame !== this.currentFrame)\n        {\n            if (this.loop && this.onLoop)\n            {\n                if ((this.animationSpeed > 0 && this.currentFrame < previousFrame)\n                    || (this.animationSpeed < 0 && this.currentFrame > previousFrame))\n                {\n                    this.onLoop();\n                }\n            }\n\n            this.updateTexture();\n        }\n    }\n\n    /** Updates the displayed texture to match the current frame index. */\n    private updateTexture(): void\n    {\n        const currentFrame = this.currentFrame;\n\n        if (this._previousFrame === currentFrame)\n        {\n            return;\n        }\n\n        this._previousFrame = currentFrame;\n\n        this._texture = this._textures[currentFrame];\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n        this._cachedTint = 0xFFFFFF;\n        this.uvs = this._texture._uvs.uvsFloat32;\n\n        if (this.updateAnchor)\n        {\n            this._anchor.copyFrom(this._texture.defaultAnchor);\n        }\n\n        if (this.onFrameChange)\n        {\n            this.onFrameChange(this.currentFrame);\n        }\n    }\n\n    /**\n     * Stops the AnimatedSprite and destroys it.\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value.\n     * @param {boolean} [options.children=false] - If set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well.\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well.\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.stop();\n        super.destroy(options);\n\n        this.onComplete = null;\n        this.onFrameChange = null;\n        this.onLoop = null;\n    }\n\n    /**\n     * A short hand way of creating an AnimatedSprite from an array of frame ids.\n     * @param frames - The array of frames ids the AnimatedSprite will use as its texture frames.\n     * @returns - The new animated sprite with the specified frames.\n     */\n    public static fromFrames(frames: string[]): AnimatedSprite\n    {\n        const textures = [];\n\n        for (let i = 0; i < frames.length; ++i)\n        {\n            textures.push(Texture.from(frames[i]));\n        }\n\n        return new AnimatedSprite(textures);\n    }\n\n    /**\n     * A short hand way of creating an AnimatedSprite from an array of image ids.\n     * @param images - The array of image urls the AnimatedSprite will use as its texture frames.\n     * @returns The new animate sprite with the specified images as frames.\n     */\n    public static fromImages(images: string[]): AnimatedSprite\n    {\n        const textures = [];\n\n        for (let i = 0; i < images.length; ++i)\n        {\n            textures.push(Texture.from(images[i]));\n        }\n\n        return new AnimatedSprite(textures);\n    }\n\n    /**\n     * The total number of frames in the AnimatedSprite. This is the same as number of textures\n     * assigned to the AnimatedSprite.\n     * @readonly\n     * @default 0\n     */\n    get totalFrames(): number\n    {\n        return this._textures.length;\n    }\n\n    /** The array of textures used for this AnimatedSprite. */\n    get textures(): Texture[] | FrameObject[]\n    {\n        return this._textures;\n    }\n\n    set textures(value: Texture[] | FrameObject[])\n    {\n        if (value[0] instanceof Texture)\n        {\n            this._textures = value as Texture[];\n            this._durations = null;\n        }\n        else\n        {\n            this._textures = [];\n            this._durations = [];\n\n            for (let i = 0; i < value.length; i++)\n            {\n                this._textures.push((value[i] as FrameObject).texture);\n                this._durations.push((value[i] as FrameObject).time);\n            }\n        }\n        this._previousFrame = null;\n        this.gotoAndStop(0);\n        this.updateTexture();\n    }\n\n    /** The AnimatedSprite's current frame index. */\n    get currentFrame(): number\n    {\n        let currentFrame = Math.floor(this._currentTime) % this._textures.length;\n\n        if (currentFrame < 0)\n        {\n            currentFrame += this._textures.length;\n        }\n\n        return currentFrame;\n    }\n\n    set currentFrame(value: number)\n    {\n        if (value < 0 || value > this.totalFrames - 1)\n        {\n            throw new Error(`[AnimatedSprite]: Invalid frame index value ${value}, `\n                + `expected to be between 0 and totalFrames ${this.totalFrames}.`);\n        }\n\n        const previousFrame = this.currentFrame;\n\n        this._currentTime = value;\n\n        if (previousFrame !== this.currentFrame)\n        {\n            this.updateTexture();\n        }\n    }\n\n    /**\n     * Indicates if the AnimatedSprite is currently playing.\n     * @readonly\n     */\n    get playing(): boolean\n    {\n        return this._playing;\n    }\n\n    /** Whether to use Ticker.shared to auto update animation time. */\n    get autoUpdate(): boolean\n    {\n        return this._autoUpdate;\n    }\n\n    set autoUpdate(value: boolean)\n    {\n        if (value !== this._autoUpdate)\n        {\n            this._autoUpdate = value;\n\n            if (!this._autoUpdate && this._isConnectedToTicker)\n            {\n                Ticker.shared.remove(this.update, this);\n                this._isConnectedToTicker = false;\n            }\n            else if (this._autoUpdate && !this._isConnectedToTicker && this._playing)\n            {\n                Ticker.shared.add(this.update, this);\n                this._isConnectedToTicker = true;\n            }\n        }\n    }\n}\n\n/** @memberof PIXI.AnimatedSprite */\nexport interface FrameObject\n{\n    /** The {@link PIXI.Texture} of the frame. */\n    texture: Texture;\n\n    /** The duration of the frame, in milliseconds. */\n    time: number;\n}\n"], "names": ["Sprite", "Texture", "Ticker", "UPDATE_PRIORITY"], "mappings": ";;AAqCO,MAAM,uBAAuBA,OAAAA,OACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgFI,YAAY,UAAqC,aAAa,IAC9D;AACU,UAAA,SAAS,CAAC,aAAaC,eAAU,SAAS,CAAC,IAAI,SAAS,CAAC,EAAE,OAAO,GAExE,KAAK,YAAY,MACjB,KAAK,aAAa,MAClB,KAAK,cAAc,YACnB,KAAK,uBAAuB,IAE5B,KAAK,iBAAiB,GACtB,KAAK,OAAO,IACZ,KAAK,eAAe,IACpB,KAAK,aAAa,MAClB,KAAK,gBAAgB,MACrB,KAAK,SAAS,MAEd,KAAK,eAAe,GAEpB,KAAK,WAAW,IAChB,KAAK,iBAAiB,MAEtB,KAAK,WAAW;AAAA,EACpB;AAAA;AAAA,EAGO,OACP;AACS,SAAK,aAKV,KAAK,WAAW,IACZ,KAAK,eAAe,KAAK,yBAEzBC,KAAAA,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAI,GACtC,KAAK,uBAAuB;AAAA,EAEpC;AAAA;AAAA,EAGO,OACP;AACQ,SAAK,aAKT,KAAK,WAAW,IACZ,KAAK,eAAe,CAAC,KAAK,yBAE1BA,YAAO,OAAO,IAAI,KAAK,QAAQ,MAAMC,qBAAgB,IAAI,GACzD,KAAK,uBAAuB;AAAA,EAEpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,YAAY,aACnB;AACS,SAAA,KACL,GAAA,KAAK,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,YAAY,aACnB;AACS,SAAA,eAAe,aACpB,KAAK,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,WACP;AACI,QAAI,CAAC,KAAK;AAEN;AAGJ,UAAM,UAAU,KAAK,iBAAiB,WAChC,gBAAgB,KAAK;AAEvB,QAAA,KAAK,eAAe,MACxB;AACI,UAAI,MAAM,KAAK,eAAe,IAAI,KAAK,WAAW,KAAK,YAAY;AAInE,WAFA,OAAO,UAAU,KAAK,KAEf,MAAM;AAET,aAAK,gBACL,OAAO,KAAK,WAAW,KAAK,YAAY;AAG5C,YAAM,OAAO,KAAK,KAAK,KAAK,iBAAiB,SAAS;AAEtD,WAAA,KAAK,eAAe,KAAK,MAAM,KAAK,YAAY,GAEzC,OAAO,KAAK,WAAW,KAAK,YAAY;AAE3C,eAAO,KAAK,WAAW,KAAK,YAAY,IAAI,MAC5C,KAAK,gBAAgB;AAGzB,WAAK,gBAAgB,MAAM,KAAK,WAAW,KAAK,YAAY;AAAA,IAChE;AAGI,WAAK,gBAAgB;AAGrB,SAAK,eAAe,KAAK,CAAC,KAAK,QAE/B,KAAK,YAAY,CAAC,GAEd,KAAK,cAEL,KAAK,WAAW,KAGf,KAAK,gBAAgB,KAAK,UAAU,UAAU,CAAC,KAAK,QAEzD,KAAK,YAAY,KAAK,UAAU,SAAS,CAAC,GAEtC,KAAK,cAEL,KAAK,WAAA,KAGJ,kBAAkB,KAAK,iBAExB,KAAK,QAAQ,KAAK,WAEb,KAAK,iBAAiB,KAAK,KAAK,eAAe,iBAC5C,KAAK,iBAAiB,KAAK,KAAK,eAAe,kBAEnD,KAAK,OAAA,GAIb,KAAK,cAAc;AAAA,EAE3B;AAAA;AAAA,EAGQ,gBACR;AACI,UAAM,eAAe,KAAK;AAEtB,SAAK,mBAAmB,iBAK5B,KAAK,iBAAiB,cAEtB,KAAK,WAAW,KAAK,UAAU,YAAY,GAC3C,KAAK,aAAa,IAClB,KAAK,oBAAoB,IACzB,KAAK,cAAc,UACnB,KAAK,MAAM,KAAK,SAAS,KAAK,YAE1B,KAAK,gBAEL,KAAK,QAAQ,SAAS,KAAK,SAAS,aAAa,GAGjD,KAAK,iBAEL,KAAK,cAAc,KAAK,YAAY;AAAA,EAE5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,QAAQ,SACf;AACI,SAAK,KAAK,GACV,MAAM,QAAQ,OAAO,GAErB,KAAK,aAAa,MAClB,KAAK,gBAAgB,MACrB,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAc,WAAW,QACzB;AACI,UAAM,WAAW,CAAA;AAEjB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE;AAEjC,eAAS,KAAKF,aAAQ,KAAK,OAAO,CAAC,CAAC,CAAC;AAGlC,WAAA,IAAI,eAAe,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAc,WAAW,QACzB;AACI,UAAM,WAAW,CAAA;AAEjB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE;AAEjC,eAAS,KAAKA,aAAQ,KAAK,OAAO,CAAC,CAAC,CAAC;AAGlC,WAAA,IAAI,eAAe,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,cACJ;AACI,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA;AAAA,EAGA,IAAI,WACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,SAAS,OACb;AACQ,QAAA,MAAM,CAAC,aAAaA,KAAA;AAEf,WAAA,YAAY,OACjB,KAAK,aAAa;AAAA,SAGtB;AACI,WAAK,YAAY,CAAA,GACjB,KAAK,aAAa,CAAA;AAElB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAE9B,aAAK,UAAU,KAAM,MAAM,CAAC,EAAkB,OAAO,GACrD,KAAK,WAAW,KAAM,MAAM,CAAC,EAAkB,IAAI;AAAA,IAE3D;AACA,SAAK,iBAAiB,MACtB,KAAK,YAAY,CAAC,GAClB,KAAK;EACT;AAAA;AAAA,EAGA,IAAI,eACJ;AACI,QAAI,eAAe,KAAK,MAAM,KAAK,YAAY,IAAI,KAAK,UAAU;AAElE,WAAI,eAAe,MAEf,gBAAgB,KAAK,UAAU,SAG5B;AAAA,EACX;AAAA,EAEA,IAAI,aAAa,OACjB;AACI,QAAI,QAAQ,KAAK,QAAQ,KAAK,cAAc;AAExC,YAAM,IAAI,MAAM,+CAA+C,KAAK,8CAClB,KAAK,WAAW,GAAG;AAGzE,UAAM,gBAAgB,KAAK;AAE3B,SAAK,eAAe,OAEhB,kBAAkB,KAAK,gBAEvB,KAAK;EAEb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAGA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,WAAW,OACf;AACQ,cAAU,KAAK,gBAEf,KAAK,cAAc,OAEf,CAAC,KAAK,eAAe,KAAK,wBAE1BC,KAAAA,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAI,GACtC,KAAK,uBAAuB,MAEvB,KAAK,eAAe,CAAC,KAAK,wBAAwB,KAAK,aAE5DA,YAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,GACnC,KAAK,uBAAuB;AAAA,EAGxC;AACJ;;"}