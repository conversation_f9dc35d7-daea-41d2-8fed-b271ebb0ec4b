{"name": "@pixi/sprite-animated", "version": "7.4.3", "main": "lib/index.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "description": "Sprite Animations as depicted by playing a series of Textures", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixijs/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixijs.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "*.d.ts"], "peerDependencies": {"@pixi/core": "7.4.3", "@pixi/sprite": "7.4.3"}}