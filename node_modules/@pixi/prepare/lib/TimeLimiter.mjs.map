{"version": 3, "file": "TimeLimiter.mjs", "sources": ["../src/TimeLimiter.ts"], "sourcesContent": ["/**\n * TimeLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of milliseconds per frame.\n * @memberof PIXI\n */\nexport class TimeLimiter\n{\n    /** The maximum milliseconds that can be spent preparing items each frame. */\n    public maxMilliseconds: number;\n\n    /**\n     * The start time of the current frame.\n     * @readonly\n     */\n    public frameStart: number;\n\n    /** @param maxMilliseconds - The maximum milliseconds that can be spent preparing items each frame. */\n    constructor(maxMilliseconds: number)\n    {\n        this.maxMilliseconds = maxMilliseconds;\n        this.frameStart = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.frameStart = Date.now();\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns - If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return Date.now() - this.frameStart < this.maxMilliseconds;\n    }\n}\n"], "names": [], "mappings": "AAKO,MAAM,YACb;AAAA;AAAA,EAWI,YAAY,iBACZ;AACS,SAAA,kBAAkB,iBACvB,KAAK,aAAa;AAAA,EACtB;AAAA;AAAA,EAGA,aACA;AACS,SAAA,aAAa,KAAK;EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBACA;AACI,WAAO,KAAK,IAAQ,IAAA,KAAK,aAAa,KAAK;AAAA,EAC/C;AACJ;"}