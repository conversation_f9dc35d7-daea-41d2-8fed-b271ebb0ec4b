{"version": 3, "file": "settings.mjs", "sources": ["../src/settings.ts"], "sourcesContent": ["import { settings, utils } from '@pixi/core';\nimport { BasePrepare } from './BasePrepare';\n\nObject.defineProperties(settings, {\n    /**\n     * Default number of uploads per frame using prepare plugin.\n     * @static\n     * @memberof PIXI.settings\n     * @name UPLOADS_PER_FRAME\n     * @deprecated since 7.1.0\n     * @see PIXI.BasePrepare.uploadsPerFrame\n     * @type {number}\n     */\n    UPLOADS_PER_FRAME:\n    {\n        get()\n        {\n            return BasePrepare.uploadsPerFrame;\n        },\n        set(value: number)\n        {\n            if (process.env.DEBUG)\n            {\n                // eslint-disable-next-line max-len\n                utils.deprecation('7.1.0', 'settings.UPLOADS_PER_FRAME is deprecated, use prepare.BasePrepare.uploadsPerFrame');\n            }\n            BasePrepare.uploadsPerFrame = value;\n        },\n    },\n});\n\nexport { settings };\n"], "names": [], "mappings": ";;;AAGA,OAAO,iBAAiB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9B,mBACA;AAAA,IACI,MACA;AACI,aAAO,YAAY;AAAA,IACvB;AAAA,IACA,IAAI,OACJ;AAIQ,YAAM,YAAY,SAAS,mFAAmF,GAElH,YAAY,kBAAkB;AAAA,IAClC;AAAA,EACJ;AACJ,CAAC;"}