{"version": 3, "file": "settings.js", "sources": ["../src/settings.ts"], "sourcesContent": ["import { settings, utils } from '@pixi/core';\nimport { BasePrepare } from './BasePrepare';\n\nObject.defineProperties(settings, {\n    /**\n     * Default number of uploads per frame using prepare plugin.\n     * @static\n     * @memberof PIXI.settings\n     * @name UPLOADS_PER_FRAME\n     * @deprecated since 7.1.0\n     * @see PIXI.BasePrepare.uploadsPerFrame\n     * @type {number}\n     */\n    UPLOADS_PER_FRAME:\n    {\n        get()\n        {\n            return BasePrepare.uploadsPerFrame;\n        },\n        set(value: number)\n        {\n            if (process.env.DEBUG)\n            {\n                // eslint-disable-next-line max-len\n                utils.deprecation('7.1.0', 'settings.UPLOADS_PER_FRAME is deprecated, use prepare.BasePrepare.uploadsPerFrame');\n            }\n            BasePrepare.uploadsPerFrame = value;\n        },\n    },\n});\n\nexport { settings };\n"], "names": ["settings", "BasePrepare", "utils"], "mappings": ";;AAGA,OAAO,iBAAiBA,KAAAA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9B,mBACA;AAAA,IACI,MACA;AACI,aAAOC,YAAAA,YAAY;AAAA,IACvB;AAAA,IACA,IAAI,OACJ;AAIQC,WAAA,MAAM,YAAY,SAAS,mFAAmF,GAElHD,wBAAY,kBAAkB;AAAA,IAClC;AAAA,EACJ;AACJ,CAAC;;;;;;;"}