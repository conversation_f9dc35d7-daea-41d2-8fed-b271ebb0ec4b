{"version": 3, "file": "Prepare.mjs", "sources": ["../src/Prepare.ts"], "sourcesContent": ["import { BaseTexture, extensions, ExtensionType } from '@pixi/core';\nimport { Graphics } from '@pixi/graphics';\nimport { BasePrepare } from './BasePrepare';\n\nimport type { ExtensionMetadata, IRenderer, ISystem, Renderer } from '@pixi/core';\nimport type { IDisplayObjectExtended } from './BasePrepare';\n\n/**\n * Built-in hook to upload PIXI.Texture objects to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadBaseTextures(renderer: IRenderer | BasePrepare, item: IDisplayObjectExtended | BaseTexture): boolean\n{\n    if (item instanceof BaseTexture)\n    {\n        // if the texture already has a GL texture, then the texture has been prepared or rendered\n        // before now. If the texture changed, then the changer should be calling texture.update() which\n        // reuploads the texture without need for preparing it again\n        if (!item._glTextures[(renderer as Renderer).CONTEXT_UID])\n        {\n            (renderer as Render<PERSON>).texture.bind(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to upload PIXI.Graphics to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadGraphics(renderer: IRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (!(item instanceof Graphics))\n    {\n        return false;\n    }\n\n    const { geometry } = item;\n\n    // update dirty graphics to get batches\n    item.finishPoly();\n    geometry.updateBatches();\n\n    const { batches } = geometry;\n\n    // upload all textures found in styles\n    for (let i = 0; i < batches.length; i++)\n    {\n        const { texture } = batches[i].style;\n\n        if (texture)\n        {\n            uploadBaseTextures(renderer, texture.baseTexture);\n        }\n    }\n\n    // if its not batchable - update vao for particular shader\n    if (!geometry.batchable)\n    {\n        (renderer as Renderer).geometry.bind(geometry, (item as any)._resolveDirectShader((renderer as Renderer)));\n    }\n\n    return true;\n}\n\n/**\n * Built-in hook to find graphics.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Graphics object was found.\n */\nfunction findGraphics(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Graphics)\n    {\n        queue.push(item);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare plugin provides renderer-specific plugins for pre-rendering DisplayObjects. These plugins are useful for\n * asynchronously preparing and uploading to the GPU assets, textures, graphics waiting to be displayed.\n *\n * Do not instantiate this plugin directly. It is available from the `renderer.prepare` property.\n * @example\n * import { Application, Graphics } from 'pixi.js';\n *\n * // Create a new application (prepare will be auto-added to renderer)\n * const app = new Application();\n * document.body.appendChild(app.view);\n *\n * // Don't start rendering right away\n * app.stop();\n *\n * // Create a display object\n * const rect = new Graphics()\n *     .beginFill(0x00ff00)\n *     .drawRect(40, 40, 200, 200);\n *\n * // Add to the stage\n * app.stage.addChild(rect);\n *\n * // Don't start rendering until the graphic is uploaded to the GPU\n * app.renderer.prepare.upload(app.stage, () => {\n *     app.start();\n * });\n * @memberof PIXI\n */\nexport class Prepare extends BasePrepare implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'prepare',\n        type: ExtensionType.RendererSystem,\n    };\n\n    /**\n     * @param {PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.uploadHookHelper = this.renderer;\n\n        // Add textures and graphics to upload\n        this.registerFindHook(findGraphics);\n        this.registerUploadHook(uploadBaseTextures);\n        this.registerUploadHook(uploadGraphics);\n    }\n}\n\nextensions.add(Prepare);\n"], "names": [], "mappings": ";;;AAcA,SAAS,mBAAmB,UAAmC,MAC/D;AACI,SAAI,gBAAgB,eAKX,KAAK,YAAa,SAAsB,WAAW,KAEnD,SAAsB,QAAQ,KAAK,IAAI,GAGrC,MAGJ;AACX;AASA,SAAS,eAAe,UAAmC,MAC3D;AACI,MAAI,EAAE,gBAAgB;AAEX,WAAA;AAGL,QAAA,EAAE,SAAa,IAAA;AAGhB,OAAA,WAAA,GACL,SAAS,cAAc;AAEjB,QAAA,EAAE,QAAY,IAAA;AAGpB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KACpC;AACI,UAAM,EAAE,QAAY,IAAA,QAAQ,CAAC,EAAE;AAE3B,eAEA,mBAAmB,UAAU,QAAQ,WAAW;AAAA,EAExD;AAGK,SAAA,SAAS,aAET,SAAsB,SAAS,KAAK,UAAW,KAAa,qBAAsB,QAAqB,CAAC,GAGtG;AACX;AASA,SAAS,aAAa,MAA8B,OACpD;AACI,SAAI,gBAAgB,YAEhB,MAAM,KAAK,IAAI,GAER,MAGJ;AACX;AA+BO,MAAM,gBAAgB,YAC7B;AAAA;AAAA;AAAA;AAAA,EAUI,YAAY,UACZ;AACI,UAAM,QAAQ,GAEd,KAAK,mBAAmB,KAAK,UAG7B,KAAK,iBAAiB,YAAY,GAClC,KAAK,mBAAmB,kBAAkB,GAC1C,KAAK,mBAAmB,cAAc;AAAA,EAC1C;AACJ;AAtBa,QAGF,YAA+B;AAAA,EAClC,MAAM;AAAA,EACN,MAAM,cAAc;AACxB;AAkBJ,WAAW,IAAI,OAAO;"}