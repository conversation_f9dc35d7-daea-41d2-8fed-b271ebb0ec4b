{"version": 3, "file": "CountLimiter.mjs", "sources": ["../src/CountLimiter.ts"], "sourcesContent": ["/**\n * CountLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of items per frame.\n * @memberof PIXI\n */\nexport class CountLimiter\n{\n    /** The maximum number of items that can be prepared each frame. */\n    public maxItemsPerFrame: number;\n\n    /** The number of items that can be prepared in the current frame. */\n    public itemsLeft: number;\n\n    /**\n     * @param maxItemsPerFrame - The maximum number of items that can be prepared each frame.\n     */\n    constructor(maxItemsPerFrame: number)\n    {\n        this.maxItemsPerFrame = maxItemsPerFrame;\n        this.itemsLeft = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.itemsLeft = this.maxItemsPerFrame;\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return this.itemsLeft-- > 0;\n    }\n}\n"], "names": [], "mappings": "AAKO,MAAM,aACb;AAAA;AAAA;AAAA;AAAA,EAUI,YAAY,kBACZ;AACS,SAAA,mBAAmB,kBACxB,KAAK,YAAY;AAAA,EACrB;AAAA;AAAA,EAGA,aACA;AACI,SAAK,YAAY,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBACA;AACI,WAAO,KAAK,cAAc;AAAA,EAC9B;AACJ;"}