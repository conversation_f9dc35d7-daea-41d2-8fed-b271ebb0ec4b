{"version": 3, "file": "BasePrepare.js", "sources": ["../src/BasePrepare.ts"], "sourcesContent": ["import { BaseTexture, Texture, Ticker, UPDATE_PRIORITY } from '@pixi/core';\nimport { Container } from '@pixi/display';\nimport { Text, TextMetrics, TextStyle } from '@pixi/text';\nimport { CountLimiter } from './CountLimiter';\n\nimport type { <PERSON><PERSON><PERSON><PERSON> } from '@pixi/core';\nimport type { DisplayObject } from '@pixi/display';\n\ninterface IArrowFunction\n{\n    (): void;\n}\ninterface IUploadHook\n{\n    (helper: I<PERSON>enderer | BasePrepare, item: IDisplayObjectExtended): boolean;\n}\n\ninterface IFindHook\n{\n    (item: any, queue: Array<any>): boolean;\n}\n\nexport interface IDisplayObjectExtended extends DisplayObject\n{\n    _textures?: Array<Texture>;\n    _texture?: Texture;\n    style?: TextStyle | Partial<TextStyle>;\n}\n\n/**\n * Built-in hook to find multiple textures from objects like AnimatedSprites.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findMultipleBaseTextures(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    let result = false;\n\n    // Objects with multiple textures\n    if (item?._textures?.length)\n    {\n        for (let i = 0; i < item._textures.length; i++)\n        {\n            if (item._textures[i] instanceof Texture)\n            {\n                const baseTexture = item._textures[i].baseTexture;\n\n                if (!queue.includes(baseTexture))\n                {\n                    queue.push(baseTexture);\n                    result = true;\n                }\n            }\n        }\n    }\n\n    return result;\n}\n\n/**\n * Built-in hook to find BaseTextures from Texture.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findBaseTexture(item: Texture, queue: Array<any>): boolean\n{\n    if (item.baseTexture instanceof BaseTexture)\n    {\n        const texture = item.baseTexture;\n\n        if (!queue.includes(texture))\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find textures from objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findTexture(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item._texture && item._texture instanceof Texture)\n    {\n        const texture = item._texture.baseTexture;\n\n        if (!queue.includes(texture))\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to draw PIXI.Text to its texture.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction drawText(_helper: IRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof Text)\n    {\n        // updating text will return early if it is not dirty\n        item.updateText(true);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to calculate a text style for a PIXI.Text object.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction calculateTextStyle(_helper: IRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        const font = item.toFontString();\n\n        TextMetrics.measureFont(font);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find Text objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Text object was found.\n */\nfunction findText(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Text)\n    {\n        // push the text style to prepare it - this can be really expensive\n        if (!queue.includes(item.style))\n        {\n            queue.push(item.style);\n        }\n        // also push the text object so that we can render it (to canvas/texture) if needed\n        if (!queue.includes(item))\n        {\n            queue.push(item);\n        }\n        // also push the Text's texture for upload to GPU\n        const texture = item._texture.baseTexture;\n\n        if (!queue.includes(texture))\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find TextStyle objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.TextStyle object was found.\n */\nfunction findTextStyle(item: TextStyle, queue: Array<any>): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        if (!queue.includes(item))\n        {\n            queue.push(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare manager provides functionality to upload content to the GPU.\n *\n * BasePrepare handles basic queuing functionality and is extended by\n * {@link PIXI.Prepare} and {@link PIXI.CanvasPrepare}\n * to provide preparation capabilities specific to their respective renderers.\n * @example\n * // Create a sprite\n * const sprite = PIXI.Sprite.from('something.png');\n *\n * // Load object into GPU\n * app.renderer.prepare.upload(sprite, () => {\n *     // Texture(s) has been uploaded to GPU\n *     app.stage.addChild(sprite);\n * });\n * @abstract\n * @memberof PIXI\n */\nexport class BasePrepare\n{\n    /**\n     * The default maximum uploads per frame.\n     * @static\n     */\n    public static uploadsPerFrame = 4;\n\n    /**\n     * The limiter to be used to control how quickly items are prepared.\n     * @type {PIXI.CountLimiter|PIXI.TimeLimiter}\n     */\n    private limiter: CountLimiter;\n\n    /** Reference to the renderer. */\n    protected renderer: IRenderer;\n\n    /**\n     * The only real difference between CanvasPrepare and Prepare is what they pass\n     * to upload hooks. That different parameter is stored here.\n     */\n    protected uploadHookHelper: any;\n\n    /** Collection of items to uploads at once. */\n    protected queue: Array<any>;\n\n    /**\n     * Collection of additional hooks for finding assets.\n     * @type {Array<Function>}\n     */\n    public addHooks: Array<any>;\n\n    /**\n     * Collection of additional hooks for processing assets.\n     * @type {Array<Function>}\n     */\n    public uploadHooks: Array<any>;\n\n    /**\n     * Callback to call after completed.\n     * @type {Array<Function>}\n     */\n    public completes: Array<any>;\n\n    /**\n     * If prepare is ticking (running).\n     * @type {boolean}\n     */\n    public ticking: boolean;\n\n    /**\n     * 'bound' call for prepareItems().\n     * @type {Function}\n     */\n    private delayedTick: IArrowFunction;\n\n    /**\n     * @param {PIXI.IRenderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: IRenderer)\n    {\n        this.limiter = new CountLimiter(BasePrepare.uploadsPerFrame);\n        this.renderer = renderer;\n        this.uploadHookHelper = null;\n        this.queue = [];\n        this.addHooks = [];\n        this.uploadHooks = [];\n        this.completes = [];\n        this.ticking = false;\n        this.delayedTick = (): void =>\n        {\n            // unlikely, but in case we were destroyed between tick() and delayedTick()\n            if (!this.queue)\n            {\n                return;\n            }\n            this.prepareItems();\n        };\n\n        // hooks to find the correct texture\n        this.registerFindHook(findText);\n        this.registerFindHook(findTextStyle);\n        this.registerFindHook(findMultipleBaseTextures);\n        this.registerFindHook(findBaseTexture);\n        this.registerFindHook(findTexture);\n\n        // upload hooks\n        this.registerUploadHook(drawText);\n        this.registerUploadHook(calculateTextStyle);\n    }\n\n    /**\n     * Upload all the textures and graphics to the GPU.\n     * @method PIXI.BasePrepare#upload\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} [item] -\n     *        Container or display object to search for items to upload or the items to upload themselves,\n     *        or optionally ommitted, if items have been added using {@link PIXI.BasePrepare#add `prepare.add`}.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture): Promise<void>\n    {\n        return new Promise((resolve) =>\n        {\n            // If a display object, search for items\n            // that we could upload\n            if (item)\n            {\n                this.add(item as IDisplayObjectExtended | Container | BaseTexture | Texture);\n            }\n\n            // Get the items for upload from the display\n            if (this.queue.length)\n            {\n                this.completes.push(resolve);\n\n                if (!this.ticking)\n                {\n                    this.ticking = true;\n                    Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n                }\n            }\n            else\n            {\n                resolve();\n            }\n        });\n    }\n\n    /**\n     * Handle tick update\n     * @private\n     */\n    tick(): void\n    {\n        setTimeout(this.delayedTick, 0);\n    }\n\n    /**\n     * Actually prepare items. This is handled outside of the tick because it will take a while\n     * and we do NOT want to block the current animation frame from rendering.\n     * @private\n     */\n    prepareItems(): void\n    {\n        this.limiter.beginFrame();\n        // Upload the graphics\n        while (this.queue.length && this.limiter.allowedToUpload())\n        {\n            const item = this.queue[0];\n            let uploaded = false;\n\n            if (item && !item._destroyed)\n            {\n                for (let i = 0, len = this.uploadHooks.length; i < len; i++)\n                {\n                    if (this.uploadHooks[i](this.uploadHookHelper, item))\n                    {\n                        this.queue.shift();\n                        uploaded = true;\n                        break;\n                    }\n                }\n            }\n\n            if (!uploaded)\n            {\n                this.queue.shift();\n            }\n        }\n\n        // We're finished\n        if (!this.queue.length)\n        {\n            this.ticking = false;\n\n            const completes = this.completes.slice(0);\n\n            this.completes.length = 0;\n\n            for (let i = 0, len = completes.length; i < len; i++)\n            {\n                completes[i]();\n            }\n        }\n        else\n        {\n            // if we are not finished, on the next rAF do this again\n            Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n        }\n    }\n\n    /**\n     * Adds hooks for finding items.\n     * @param {Function} addHook - Function call that takes two parameters: `item:*, queue:Array`\n     *          function must return `true` if it was able to add item to the queue.\n     * @returns Instance of plugin for chaining.\n     */\n    registerFindHook(addHook: IFindHook): this\n    {\n        if (addHook)\n        {\n            this.addHooks.push(addHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Adds hooks for uploading items.\n     * @param {Function} uploadHook - Function call that takes two parameters: `prepare:CanvasPrepare, item:*` and\n     *          function must return `true` if it was able to handle upload of item.\n     * @returns Instance of plugin for chaining.\n     */\n    registerUploadHook(uploadHook: IUploadHook): this\n    {\n        if (uploadHook)\n        {\n            this.uploadHooks.push(uploadHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Manually add an item to the uploading queue.\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text|*} item - Object to\n     *        add to the queue\n     * @returns Instance of plugin for chaining.\n     */\n    add(item: IDisplayObjectExtended | Container | BaseTexture | Texture): this\n    {\n        // Add additional hooks for finding elements on special\n        // types of objects that\n        for (let i = 0, len = this.addHooks.length; i < len; i++)\n        {\n            if (this.addHooks[i](item, this.queue))\n            {\n                break;\n            }\n        }\n\n        // Get children recursively\n        if (item instanceof Container)\n        {\n            for (let i = item.children.length - 1; i >= 0; i--)\n            {\n                this.add(item.children[i]);\n            }\n        }\n\n        return this;\n    }\n\n    /** Destroys the plugin, don't use after this. */\n    destroy(): void\n    {\n        if (this.ticking)\n        {\n            Ticker.system.remove(this.tick, this);\n        }\n        this.ticking = false;\n        this.addHooks = null;\n        this.uploadHooks = null;\n        this.renderer = null;\n        this.completes = null;\n        this.queue = null;\n        this.limiter = null;\n        this.uploadHookHelper = null;\n    }\n}\n"], "names": ["Texture", "BaseTexture", "Text", "TextStyle", "TextMetrics", "_BasePrepare", "CountLimiter", "Ticker", "UPDATE_PRIORITY", "Container"], "mappings": ";;AAoCA,SAAS,yBAAyB,MAA8B,OAChE;AACI,MAAI,SAAS;AAGb,MAAI,MAAM,WAAW;AAEjB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ;AAEvC,UAAI,KAAK,UAAU,CAAC,aAAaA,KAAAA,SACjC;AACI,cAAM,cAAc,KAAK,UAAU,CAAC,EAAE;AAEjC,cAAM,SAAS,WAAW,MAE3B,MAAM,KAAK,WAAW,GACtB,SAAS;AAAA,MAEjB;AAAA;AAID,SAAA;AACX;AASA,SAAS,gBAAgB,MAAe,OACxC;AACQ,MAAA,KAAK,uBAAuBC,kBAChC;AACI,UAAM,UAAU,KAAK;AAErB,WAAK,MAAM,SAAS,OAAO,KAEvB,MAAM,KAAK,OAAO,GAGf;AAAA,EACX;AAEO,SAAA;AACX;AASA,SAAS,YAAY,MAA8B,OACnD;AACI,MAAI,KAAK,YAAY,KAAK,oBAAoBD,KAAAA,SAC9C;AACU,UAAA,UAAU,KAAK,SAAS;AAE9B,WAAK,MAAM,SAAS,OAAO,KAEvB,MAAM,KAAK,OAAO,GAGf;AAAA,EACX;AAEO,SAAA;AACX;AASA,SAAS,SAAS,SAAkC,MACpD;AACI,SAAI,gBAAgBE,aAGhB,KAAK,WAAW,EAAI,GAEb,MAGJ;AACX;AASA,SAAS,mBAAmB,SAAkC,MAC9D;AACI,MAAI,gBAAgBC,KAAAA,WACpB;AACU,UAAA,OAAO,KAAK;AAEN,WAAAC,KAAA,YAAA,YAAY,IAAI,GAErB;AAAA,EACX;AAEO,SAAA;AACX;AASA,SAAS,SAAS,MAA8B,OAChD;AACI,MAAI,gBAAgBF,KAAAA,MACpB;AAES,UAAM,SAAS,KAAK,KAAK,KAE1B,MAAM,KAAK,KAAK,KAAK,GAGpB,MAAM,SAAS,IAAI,KAEpB,MAAM,KAAK,IAAI;AAGb,UAAA,UAAU,KAAK,SAAS;AAE9B,WAAK,MAAM,SAAS,OAAO,KAEvB,MAAM,KAAK,OAAO,GAGf;AAAA,EACX;AAEO,SAAA;AACX;AASA,SAAS,cAAc,MAAiB,OACxC;AACQ,SAAA,gBAAgBC,KAEX,aAAA,MAAM,SAAS,IAAI,KAEpB,MAAM,KAAK,IAAI,GAGZ,MAGJ;AACX;AAoBO,MAAM,eAAN,MAAME,cACb;AAAA;AAAA;AAAA;AAAA,EA0DI,YAAY,UACZ;AACI,SAAK,UAAU,IAAIC,aAAa,aAAAD,cAAY,eAAe,GAC3D,KAAK,WAAW,UAChB,KAAK,mBAAmB,MACxB,KAAK,QAAQ,CAAA,GACb,KAAK,WAAW,CAAA,GAChB,KAAK,cAAc,CAAA,GACnB,KAAK,YAAY,CAAA,GACjB,KAAK,UAAU,IACf,KAAK,cAAc,MACnB;AAES,WAAK,SAIV,KAAK;IACT,GAGA,KAAK,iBAAiB,QAAQ,GAC9B,KAAK,iBAAiB,aAAa,GACnC,KAAK,iBAAiB,wBAAwB,GAC9C,KAAK,iBAAiB,eAAe,GACrC,KAAK,iBAAiB,WAAW,GAGjC,KAAK,mBAAmB,QAAQ,GAChC,KAAK,mBAAmB,kBAAkB;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MACP;AACW,WAAA,IAAI,QAAQ,CAAC,YACpB;AAGQ,cAEA,KAAK,IAAI,IAAkE,GAI3E,KAAK,MAAM,UAEX,KAAK,UAAU,KAAK,OAAO,GAEtB,KAAK,YAEN,KAAK,UAAU,IACfE,KAAAA,OAAO,OAAO,QAAQ,KAAK,MAAM,MAAMC,KAAA,gBAAgB,OAAO,MAKlE,QAAQ;AAAA,IAAA,CAEf;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OACA;AACe,eAAA,KAAK,aAAa,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eACA;AACI,SAAA,KAAK,QAAQ,WAAA,GAEN,KAAK,MAAM,UAAU,KAAK,QAAQ,qBACzC;AACU,YAAA,OAAO,KAAK,MAAM,CAAC;AACzB,UAAI,WAAW;AAEX,UAAA,QAAQ,CAAC,KAAK;AAEd,iBAAS,IAAI,GAAG,MAAM,KAAK,YAAY,QAAQ,IAAI,KAAK;AAEpD,cAAI,KAAK,YAAY,CAAC,EAAE,KAAK,kBAAkB,IAAI,GACnD;AACS,iBAAA,MAAM,MAAM,GACjB,WAAW;AACX;AAAA,UACJ;AAAA;AAIH,kBAED,KAAK,MAAM;IAEnB;AAGA,QAAK,KAAK,MAAM;AAgBZD,kBAAO,OAAO,QAAQ,KAAK,MAAM,MAAMC,KAAAA,gBAAgB,OAAO;AAAA,SAflE;AACI,WAAK,UAAU;AAEf,YAAM,YAAY,KAAK,UAAU,MAAM,CAAC;AAExC,WAAK,UAAU,SAAS;AAExB,eAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK;AAE7C,kBAAU,CAAC;IAEnB;AAAA,EAMJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,SACjB;AACI,WAAI,WAEA,KAAK,SAAS,KAAK,OAAO,GAGvB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,YACnB;AACI,WAAI,cAEA,KAAK,YAAY,KAAK,UAAU,GAG7B;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MACJ;AAGI,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,OAExC,CAAA,KAAK,SAAS,CAAC,EAAE,MAAM,KAAK,KAAK,GAFY;AAEjD;AAOJ,QAAI,gBAAgBC,QAAA;AAEhB,eAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG;AAE3C,aAAK,IAAI,KAAK,SAAS,CAAC,CAAC;AAI1B,WAAA;AAAA,EACX;AAAA;AAAA,EAGA,UACA;AACQ,SAAK,WAELF,KAAO,OAAA,OAAO,OAAO,KAAK,MAAM,IAAI,GAExC,KAAK,UAAU,IACf,KAAK,WAAW,MAChB,KAAK,cAAc,MACnB,KAAK,WAAW,MAChB,KAAK,YAAY,MACjB,KAAK,QAAQ,MACb,KAAK,UAAU,MACf,KAAK,mBAAmB;AAAA,EAC5B;AACJ;AA5Qa,aAMK,kBAAkB;AAN7B,IAAM,cAAN;;"}