{"version": 3, "file": "HTMLTextStyle.mjs", "sources": ["../src/HTMLTextStyle.ts"], "sourcesContent": ["import { settings, utils } from '@pixi/core';\nimport { TextStyle } from '@pixi/text';\n\nimport type {\n    ITextStyle,\n    TextStyleFontStyle,\n    TextStyleFontWeight,\n    TextStyleLineJoin,\n    TextStyleTextBaseline\n} from '@pixi/text';\n\n/**\n * HTMLText support more white-space options.\n * @memberof PIXI\n * @since 7.2.0\n * @see PIXI.IHTMLTextStyle\n */\nexport type HTMLTextStyleWhiteSpace = 'normal' | 'pre' | 'pre-line' | 'nowrap' | 'pre-wrap';\n\n/**\n * FontFace display options.\n * @memberof PIXI\n * @since 7.3.0\n */\nexport type FontDisplay = 'auto' | 'block' | 'swap' | 'fallback' | 'optional';\n\n// Subset of ITextStyle\ntype ITextStyleIgnore = 'whiteSpace'\n| 'fillGradientStops'\n| 'fillGradientType'\n| 'miterLimit'\n| 'textBaseline'\n| 'trim'\n| 'leading'\n| 'lineJoin';\n\n/**\n * Modifed versions from ITextStyle.\n * @memberof PIXI\n * @extends PIXI.ITextStyle\n * @since 7.2.0\n */\nexport interface IHTMLTextStyle extends Omit<ITextStyle, ITextStyleIgnore>\n{\n    /** White-space with expanded options. */\n    whiteSpace: HTMLTextStyleWhiteSpace;\n}\n\nexport interface IHTMLTextFontOptions extends Pick<IHTMLFont, 'weight' | 'style' | 'family'>\n{\n    /** font-display property */\n    display: FontDisplay;\n}\n\n/**\n * Font information for HTMLText\n * @memberof PIXI\n * @since 7.2.0\n */\nexport interface IHTMLFont\n{\n    /** User-supplied URL request */\n    originalUrl: string;\n    /** Base64 string for font */\n    dataSrc: string;\n    /** FontFace installed in the document */\n    fontFace: FontFace | null;\n    /** Blob-based URL for font */\n    src: string;\n    /** Family name of font */\n    family: string;\n    /** Weight of the font */\n    weight: TextStyleFontWeight;\n    /** Style of the font */\n    style: TextStyleFontStyle;\n    /** Display property of the font */\n    display: FontDisplay;\n    /** Reference counter */\n    refs: number;\n}\n\n/**\n * Used internally to restrict text style usage and convert easily to CSS.\n * @class\n * @memberof PIXI\n * @param {PIXI.ITextStyle|PIXI.IHTMLTextStyle} [style] - Style to copy.\n * @since 7.2.0\n */\nexport class HTMLTextStyle extends TextStyle\n{\n    /** The collection of installed fonts */\n    public static availableFonts: Record<string, IHTMLFont> = {};\n\n    /**\n     * List of default options, these are largely the same as TextStyle,\n     * with the exception of whiteSpace, which is set to 'normal' by default.\n     */\n    public static readonly defaultOptions: IHTMLTextStyle = {\n        /** Align */\n        align: 'left',\n        /** Break words */\n        breakWords: false,\n        /** Drop shadow */\n        dropShadow: false,\n        /** Drop shadow alpha */\n        dropShadowAlpha: 1,\n        /**\n         * Drop shadow angle\n         * @type {number}\n         * @default Math.PI / 6\n         */\n        dropShadowAngle: Math.PI / 6,\n        /** Drop shadow blur */\n        dropShadowBlur: 0,\n        /** Drop shadow color */\n        dropShadowColor: 'black',\n        /** Drop shadow distance */\n        dropShadowDistance: 5,\n        /** Fill */\n        fill: 'black',\n        /** Font family */\n        fontFamily: 'Arial',\n        /** Font size */\n        fontSize: 26,\n        /** Font style */\n        fontStyle: 'normal',\n        /** Font variant */\n        fontVariant: 'normal',\n        /** Font weight */\n        fontWeight: 'normal',\n        /** Letter spacing */\n        letterSpacing: 0,\n        /** Line height */\n        lineHeight: 0,\n        /** Padding */\n        padding: 0,\n        /** Stroke */\n        stroke: 'black',\n        /** Stroke thickness */\n        strokeThickness: 0,\n        /** White space */\n        whiteSpace: 'normal',\n        /** Word wrap */\n        wordWrap: false,\n        /** Word wrap width */\n        wordWrapWidth: 100,\n    };\n\n    /** For using custom fonts */\n    private _fonts: IHTMLFont[] = [];\n\n    /** List of internal style rules */\n    private _overrides: string[] = [];\n\n    /** Global rules or stylesheet, useful for creating rules for rendering */\n    private _stylesheet = '';\n\n    /** Track font changes internally */\n    private fontsDirty = false;\n\n    /**\n     * Convert a TextStyle to HTMLTextStyle\n     * @param originalStyle\n     * @example\n     * import {TextStyle } from 'pixi.js';\n     * import {HTMLTextStyle} from '@pixi/text-html';\n     * const style = new TextStyle();\n     * const htmlStyle = HTMLTextStyle.from(style);\n     */\n    static from(originalStyle: TextStyle | Partial<IHTMLTextStyle>): HTMLTextStyle\n    {\n        return new HTMLTextStyle(Object.keys(HTMLTextStyle.defaultOptions)\n            .reduce((obj, prop) => ({ ...obj, [prop]: originalStyle[prop as keyof IHTMLTextStyle] }), {})\n        );\n    }\n\n    /** Clear the current font */\n    public cleanFonts(): void\n    {\n        if (this._fonts.length > 0)\n        {\n            this._fonts.forEach((font) =>\n            {\n                URL.revokeObjectURL(font.src);\n                font.refs--;\n                if (font.refs === 0)\n                {\n                    if (font.fontFace)\n                    {\n                        document.fonts.delete(font.fontFace);\n                    }\n                    delete HTMLTextStyle.availableFonts[font.originalUrl];\n                }\n            });\n            this.fontFamily = 'Arial';\n            this._fonts.length = 0;\n            this.styleID++;\n            this.fontsDirty = true;\n        }\n    }\n\n    /**\n     * Because of how HTMLText renders, fonts need to be imported\n     * @param url\n     * @param options\n     */\n    public loadFont(url: string, options: Partial<IHTMLTextFontOptions> = {}): Promise<void>\n    {\n        const { availableFonts } = HTMLTextStyle;\n\n        // Font is already installed\n        if (availableFonts[url])\n        {\n            const font = availableFonts[url];\n\n            this._fonts.push(font);\n            font.refs++;\n            this.styleID++;\n            this.fontsDirty = true;\n\n            return Promise.resolve();\n        }\n\n        return settings.ADAPTER.fetch(url)\n            .then((response) => response.blob())\n            .then(async (blob) => new Promise<[string, string]>((resolve, reject) =>\n            {\n                const src = URL.createObjectURL(blob);\n                const reader = new FileReader();\n\n                reader.onload = () => resolve([src, reader.result as string]);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            }))\n            .then(async ([src, dataSrc]) =>\n            {\n                const font: IHTMLFont = Object.assign({\n                    family: utils.path.basename(url, utils.path.extname(url)),\n                    weight: 'normal',\n                    style: 'normal',\n                    display: 'auto',\n                    src,\n                    dataSrc,\n                    refs: 1,\n                    originalUrl: url,\n                    fontFace: null,\n                }, options);\n\n                availableFonts[url] = font;\n                this._fonts.push(font);\n                this.styleID++;\n\n                // Load it into the current DOM so we can properly measure it!\n                const fontFace = new FontFace(font.family, `url(${font.src})`, {\n                    weight: font.weight,\n                    style: font.style,\n                    display: font.display,\n                });\n\n                // Keep this reference so we can remove it later from document\n                font.fontFace = fontFace;\n\n                await fontFace.load();\n                document.fonts.add(fontFace);\n                await document.fonts.ready;\n\n                this.styleID++;\n                this.fontsDirty = true;\n            });\n    }\n\n    /**\n     * Add a style override, this can be any CSS property\n     * it will override any built-in style. This is the\n     * property and the value as a string (e.g., `color: red`).\n     * This will override any other internal style.\n     * @param {string} value - CSS style(s) to add.\n     * @example\n     * style.addOverride('background-color: red');\n     */\n    public addOverride(...value: string[]): void\n    {\n        const toAdd = value.filter((v) => !this._overrides.includes(v));\n\n        if (toAdd.length > 0)\n        {\n            this._overrides.push(...toAdd);\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Remove any overrides that match the value.\n     * @param {string} value - CSS style to remove.\n     * @example\n     * style.removeOverride('background-color: red');\n     */\n    public removeOverride(...value: string[]): void\n    {\n        const toRemove = value.filter((v) => this._overrides.includes(v));\n\n        if (toRemove.length > 0)\n        {\n            this._overrides = this._overrides.filter((v) => !toRemove.includes(v));\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Internally converts all of the style properties into CSS equivalents.\n     * @param scale\n     * @returns The CSS style string, for setting `style` property of root HTMLElement.\n     */\n    public toCSS(scale: number): string\n    {\n        return [\n            `transform: scale(${scale})`,\n            `transform-origin: top left`,\n            'display: inline-block',\n            `color: ${this.normalizeColor(this.fill)}`,\n            `font-size: ${(this.fontSize as number)}px`,\n            `font-family: ${this.fontFamily}`,\n            `font-weight: ${this.fontWeight}`,\n            `font-style: ${this.fontStyle}`,\n            `font-variant: ${this.fontVariant}`,\n            `letter-spacing: ${this.letterSpacing}px`,\n            `text-align: ${this.align}`,\n            `padding: ${this.padding}px`,\n            `white-space: ${this.whiteSpace}`,\n            ...this.lineHeight ? [`line-height: ${this.lineHeight}px`] : [],\n            ...this.wordWrap ? [\n                `word-wrap: ${this.breakWords ? 'break-all' : 'break-word'}`,\n                `max-width: ${this.wordWrapWidth}px`\n            ] : [],\n            ...this.strokeThickness ? [\n                `-webkit-text-stroke-width: ${this.strokeThickness}px`,\n                `-webkit-text-stroke-color: ${this.normalizeColor(this.stroke)}`,\n                `text-stroke-width: ${this.strokeThickness}px`,\n                `text-stroke-color: ${this.normalizeColor(this.stroke)}`,\n                'paint-order: stroke',\n            ] : [],\n            ...this.dropShadow ? [this.dropShadowToCSS()] : [],\n            ...this._overrides,\n        ].join(';');\n    }\n\n    /** Get the font CSS styles from the loaded font, If available. */\n    public toGlobalCSS(): string\n    {\n        return this._fonts.reduce((result, font) => (\n            `${result}\n            @font-face {\n                font-family: \"${font.family}\";\n                src: url('${font.dataSrc}');\n                font-weight: ${font.weight};\n                font-style: ${font.style};\n                font-display: ${font.display};\n            }`\n        ), this._stylesheet);\n    }\n\n    /** Internal stylesheet contents, useful for creating rules for rendering */\n    public get stylesheet(): string\n    {\n        return this._stylesheet;\n    }\n    public set stylesheet(value: string)\n    {\n        if (this._stylesheet !== value)\n        {\n            this._stylesheet = value;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Convert numerical colors into hex-strings\n     * @param color\n     */\n    private normalizeColor(color: any): string\n    {\n        if (Array.isArray(color))\n        {\n            color = utils.rgb2hex(color);\n        }\n\n        if (typeof color === 'number')\n        {\n            return utils.hex2string(color);\n        }\n\n        return color;\n    }\n\n    /** Convert the internal drop-shadow settings to CSS text-shadow */\n    private dropShadowToCSS(): string\n    {\n        let color = this.normalizeColor(this.dropShadowColor);\n        const alpha = this.dropShadowAlpha;\n        const x = Math.round(Math.cos(this.dropShadowAngle) * this.dropShadowDistance);\n        const y = Math.round(Math.sin(this.dropShadowAngle) * this.dropShadowDistance);\n\n        // Append alpha to color\n        if (color.startsWith('#') && alpha < 1)\n        {\n            color += (alpha * 255 | 0).toString(16).padStart(2, '0');\n        }\n\n        const position = `${x}px ${y}px`;\n\n        if (this.dropShadowBlur > 0)\n        {\n            return `text-shadow: ${position} ${this.dropShadowBlur}px ${color}`;\n        }\n\n        return `text-shadow: ${position} ${color}`;\n    }\n\n    /** Resets all properties to the defaults specified in TextStyle.prototype._default */\n    public reset(): void\n    {\n        Object.assign(this, HTMLTextStyle.defaultOptions);\n    }\n\n    /**\n     * Called after the image is loaded but before drawing to the canvas.\n     * Mostly used to handle Safari's font loading bug.\n     * @ignore\n     */\n    public onBeforeDraw()\n    {\n        const { fontsDirty: prevFontsDirty } = this;\n\n        this.fontsDirty = false;\n\n        // Safari has a known bug where embedded fonts are not available\n        // immediately after the image loads, to compensate we wait an\n        // arbitrary amount of time\n        // @see https://bugs.webkit.org/show_bug.cgi?id=219770\n        if (this.isSafari && this._fonts.length > 0 && prevFontsDirty)\n        {\n            return new Promise<void>((resolve) => setTimeout(resolve, 100));\n        }\n\n        return Promise.resolve();\n    }\n\n    /**\n     * Proving that Safari is the new IE\n     * @ignore\n     */\n    private get isSafari(): boolean\n    {\n        const { userAgent } = settings.ADAPTER.getNavigator();\n\n        return (/^((?!chrome|android).)*safari/i).test(userAgent);\n    }\n\n    override set fillGradientStops(_value: number[])\n    {\n        console.warn('[HTMLTextStyle] fillGradientStops is not supported by HTMLText');\n    }\n    override get fillGradientStops()\n    {\n        return super.fillGradientStops;\n    }\n\n    override set fillGradientType(_value: number)\n    {\n        console.warn('[HTMLTextStyle] fillGradientType is not supported by HTMLText');\n    }\n    override get fillGradientType()\n    {\n        return super.fillGradientType;\n    }\n\n    override set miterLimit(_value: number)\n    {\n        console.warn('[HTMLTextStyle] miterLimit is not supported by HTMLText');\n    }\n    override get miterLimit()\n    {\n        return super.miterLimit;\n    }\n\n    override set trim(_value: boolean)\n    {\n        console.warn('[HTMLTextStyle] trim is not supported by HTMLText');\n    }\n    override get trim()\n    {\n        return super.trim;\n    }\n\n    override set textBaseline(_value: TextStyleTextBaseline)\n    {\n        console.warn('[HTMLTextStyle] textBaseline is not supported by HTMLText');\n    }\n    override get textBaseline()\n    {\n        return super.textBaseline;\n    }\n\n    override set leading(_value: number)\n    {\n        console.warn('[HTMLTextStyle] leading is not supported by HTMLText');\n    }\n    override get leading()\n    {\n        return super.leading;\n    }\n\n    override set lineJoin(_value: TextStyleLineJoin)\n    {\n        console.warn('[HTMLTextStyle] lineJoin is not supported by HTMLText');\n    }\n    override get lineJoin()\n    {\n        return super.lineJoin;\n    }\n}\n"], "names": ["_HTMLTextStyle"], "mappings": ";;AAwFO,MAAM,iBAAN,MAAMA,wBAAsB,UACnC;AAAA,EADO,cAAA;AAAA,UAAA,GAAA,SAAA,GA6DH,KAAQ,SAAsB,IAG9B,KAAQ,aAAuB,IAG/B,KAAQ,cAAc,IAGtB,KAAQ,aAAa;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWrB,OAAO,KAAK,eACZ;AACI,WAAO,IAAIA;AAAA,MAAc,OAAO,KAAKA,gBAAc,cAAc,EAC5D,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,KAAK,CAAC,IAAI,GAAG,cAAc,IAA4B,EAAE,IAAI,EAAE;AAAA,IAAA;AAAA,EAEpG;AAAA;AAAA,EAGO,aACP;AACQ,SAAK,OAAO,SAAS,MAErB,KAAK,OAAO,QAAQ,CAAC,SACrB;AACQ,UAAA,gBAAgB,KAAK,GAAG,GAC5B,KAAK,QACD,KAAK,SAAS,MAEV,KAAK,YAEL,SAAS,MAAM,OAAO,KAAK,QAAQ,GAEvC,OAAOA,gBAAc,eAAe,KAAK,WAAW;AAAA,IAAA,CAE3D,GACD,KAAK,aAAa,SAClB,KAAK,OAAO,SAAS,GACrB,KAAK,WACL,KAAK,aAAa;AAAA,EAE1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,KAAa,UAAyC,IACtE;AACU,UAAA,EAAE,eAAmB,IAAAA;AAGvB,QAAA,eAAe,GAAG,GACtB;AACU,YAAA,OAAO,eAAe,GAAG;AAE/B,aAAA,KAAK,OAAO,KAAK,IAAI,GACrB,KAAK,QACL,KAAK,WACL,KAAK,aAAa,IAEX,QAAQ;IACnB;AAEA,WAAO,SAAS,QAAQ,MAAM,GAAG,EAC5B,KAAK,CAAC,aAAa,SAAS,KAAM,CAAA,EAClC,KAAK,OAAO,SAAS,IAAI,QAA0B,CAAC,SAAS,WAC9D;AACI,YAAM,MAAM,IAAI,gBAAgB,IAAI,GAC9B,SAAS,IAAI;AAEnB,aAAO,SAAS,MAAM,QAAQ,CAAC,KAAK,OAAO,MAAgB,CAAC,GAC5D,OAAO,UAAU,QACjB,OAAO,cAAc,IAAI;AAAA,IAC5B,CAAA,CAAC,EACD,KAAK,OAAO,CAAC,KAAK,OAAO,MAC1B;AACU,YAAA,OAAkB,OAAO,OAAO;AAAA,QAClC,QAAQ,MAAM,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,QACxD,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,SACX,OAAO;AAEK,qBAAA,GAAG,IAAI,MACtB,KAAK,OAAO,KAAK,IAAI,GACrB,KAAK;AAGC,YAAA,WAAW,IAAI,SAAS,KAAK,QAAQ,OAAO,KAAK,GAAG,KAAK;AAAA,QAC3D,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,MAAA,CACjB;AAGD,WAAK,WAAW,UAEhB,MAAM,SAAS,QACf,SAAS,MAAM,IAAI,QAAQ,GAC3B,MAAM,SAAS,MAAM,OAErB,KAAK,WACL,KAAK,aAAa;AAAA,IAAA,CACrB;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,eAAe,OACtB;AACU,UAAA,QAAQ,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,WAAW,SAAS,CAAC,CAAC;AAE1D,UAAM,SAAS,MAEf,KAAK,WAAW,KAAK,GAAG,KAAK,GAC7B,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,kBAAkB,OACzB;AACU,UAAA,WAAW,MAAM,OAAO,CAAC,MAAM,KAAK,WAAW,SAAS,CAAC,CAAC;AAE5D,aAAS,SAAS,MAElB,KAAK,aAAa,KAAK,WAAW,OAAO,CAAC,MAAM,CAAC,SAAS,SAAS,CAAC,CAAC,GACrE,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,MAAM,OACb;AACW,WAAA;AAAA,MACH,oBAAoB,KAAK;AAAA,MACzB;AAAA,MACA;AAAA,MACA,UAAU,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,MACxC,cAAe,KAAK,QAAmB;AAAA,MACvC,gBAAgB,KAAK,UAAU;AAAA,MAC/B,gBAAgB,KAAK,UAAU;AAAA,MAC/B,eAAe,KAAK,SAAS;AAAA,MAC7B,iBAAiB,KAAK,WAAW;AAAA,MACjC,mBAAmB,KAAK,aAAa;AAAA,MACrC,eAAe,KAAK,KAAK;AAAA,MACzB,YAAY,KAAK,OAAO;AAAA,MACxB,gBAAgB,KAAK,UAAU;AAAA,MAC/B,GAAG,KAAK,aAAa,CAAC,gBAAgB,KAAK,UAAU,IAAI,IAAI,CAAC;AAAA,MAC9D,GAAG,KAAK,WAAW;AAAA,QACf,cAAc,KAAK,aAAa,cAAc,YAAY;AAAA,QAC1D,cAAc,KAAK,aAAa;AAAA,MAAA,IAChC,CAAC;AAAA,MACL,GAAG,KAAK,kBAAkB;AAAA,QACtB,8BAA8B,KAAK,eAAe;AAAA,QAClD,8BAA8B,KAAK,eAAe,KAAK,MAAM,CAAC;AAAA,QAC9D,sBAAsB,KAAK,eAAe;AAAA,QAC1C,sBAAsB,KAAK,eAAe,KAAK,MAAM,CAAC;AAAA,QACtD;AAAA,MAAA,IACA,CAAC;AAAA,MACL,GAAG,KAAK,aAAa,CAAC,KAAK,gBAAgB,CAAC,IAAI,CAAC;AAAA,MACjD,GAAG,KAAK;AAAA,IAAA,EACV,KAAK,GAAG;AAAA,EACd;AAAA;AAAA,EAGO,cACP;AACI,WAAO,KAAK,OAAO,OAAO,CAAC,QAAQ,SAC/B,GAAG,MAAM;AAAA;AAAA,gCAEW,KAAK,MAAM;AAAA,4BACf,KAAK,OAAO;AAAA,+BACT,KAAK,MAAM;AAAA,8BACZ,KAAK,KAAK;AAAA,gCACR,KAAK,OAAO;AAAA,gBAEjC,KAAK,WAAW;AAAA,EACvB;AAAA;AAAA,EAGA,IAAW,aACX;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAW,WAAW,OACtB;AACQ,SAAK,gBAAgB,UAErB,KAAK,cAAc,OACnB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,eAAe,OACvB;AAMI,WALI,MAAM,QAAQ,KAAK,MAEnB,QAAQ,MAAM,QAAQ,KAAK,IAG3B,OAAO,SAAU,WAEV,MAAM,WAAW,KAAK,IAG1B;AAAA,EACX;AAAA;AAAA,EAGQ,kBACR;AACI,QAAI,QAAQ,KAAK,eAAe,KAAK,eAAe;AAC9C,UAAA,QAAQ,KAAK,iBACb,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,eAAe,IAAI,KAAK,kBAAkB,GACvE,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,eAAe,IAAI,KAAK,kBAAkB;AAGzE,UAAM,WAAW,GAAG,KAAK,QAAQ,MAEjC,UAAU,QAAQ,MAAM,GAAG,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAG3D,UAAM,WAAW,GAAG,CAAC,MAAM,CAAC;AAE5B,WAAI,KAAK,iBAAiB,IAEf,gBAAgB,QAAQ,IAAI,KAAK,cAAc,MAAM,KAAK,KAG9D,gBAAgB,QAAQ,IAAI,KAAK;AAAA,EAC5C;AAAA;AAAA,EAGO,QACP;AACW,WAAA,OAAO,MAAMA,gBAAc,cAAc;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eACP;AACU,UAAA,EAAE,YAAY,eAAmB,IAAA;AAEvC,WAAA,KAAK,aAAa,IAMd,KAAK,YAAY,KAAK,OAAO,SAAS,KAAK,iBAEpC,IAAI,QAAc,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC,IAG3D,QAAQ;EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAY,WACZ;AACI,UAAM,EAAE,UAAc,IAAA,SAAS,QAAQ,aAAa;AAE5C,WAAA,iCAAkC,KAAK,SAAS;AAAA,EAC5D;AAAA,EAEA,IAAa,kBAAkB,QAC/B;AACI,YAAQ,KAAK,gEAAgE;AAAA,EACjF;AAAA,EACA,IAAa,oBACb;AACI,WAAO,MAAM;AAAA,EACjB;AAAA,EAEA,IAAa,iBAAiB,QAC9B;AACI,YAAQ,KAAK,+DAA+D;AAAA,EAChF;AAAA,EACA,IAAa,mBACb;AACI,WAAO,MAAM;AAAA,EACjB;AAAA,EAEA,IAAa,WAAW,QACxB;AACI,YAAQ,KAAK,yDAAyD;AAAA,EAC1E;AAAA,EACA,IAAa,aACb;AACI,WAAO,MAAM;AAAA,EACjB;AAAA,EAEA,IAAa,KAAK,QAClB;AACI,YAAQ,KAAK,mDAAmD;AAAA,EACpE;AAAA,EACA,IAAa,OACb;AACI,WAAO,MAAM;AAAA,EACjB;AAAA,EAEA,IAAa,aAAa,QAC1B;AACI,YAAQ,KAAK,2DAA2D;AAAA,EAC5E;AAAA,EACA,IAAa,eACb;AACI,WAAO,MAAM;AAAA,EACjB;AAAA,EAEA,IAAa,QAAQ,QACrB;AACI,YAAQ,KAAK,sDAAsD;AAAA,EACvE;AAAA,EACA,IAAa,UACb;AACI,WAAO,MAAM;AAAA,EACjB;AAAA,EAEA,IAAa,SAAS,QACtB;AACI,YAAQ,KAAK,uDAAuD;AAAA,EACxE;AAAA,EACA,IAAa,WACb;AACI,WAAO,MAAM;AAAA,EACjB;AACJ;AAhba,eAGK,iBAA4C,CAAC;AAAA;AAAA;AAAA;AAHlD,eASc,iBAAiC;AAAA;AAAA,EAEpD,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA;AAAA,EAEZ,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,iBAAiB,KAAK,KAAK;AAAA;AAAA,EAE3B,gBAAgB;AAAA;AAAA,EAEhB,iBAAiB;AAAA;AAAA,EAEjB,oBAAoB;AAAA;AAAA,EAEpB,MAAM;AAAA;AAAA,EAEN,YAAY;AAAA;AAAA,EAEZ,UAAU;AAAA;AAAA,EAEV,WAAW;AAAA;AAAA,EAEX,aAAa;AAAA;AAAA,EAEb,YAAY;AAAA;AAAA,EAEZ,eAAe;AAAA;AAAA,EAEf,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA;AAAA,EAET,QAAQ;AAAA;AAAA,EAER,iBAAiB;AAAA;AAAA,EAEjB,YAAY;AAAA;AAAA,EAEZ,UAAU;AAAA;AAAA,EAEV,eAAe;AACnB;AA1DG,IAAM,gBAAN;"}