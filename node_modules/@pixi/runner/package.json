{"name": "@pixi/runner", "version": "7.4.3", "main": "lib/index.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "description": "A simple alternative to events and signals with an emphasis on performance.", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixijs/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixijs.git"}, "keywords": ["runner", "signals", "event", "messaging", "publish", "subscribe", "observer", "pub/sub", "fast"], "publishConfig": {"access": "public"}, "scripts": {"test": "floss --path test", "benchmark": "cd benchmark && npm start"}, "files": ["lib", "*.d.ts"]}