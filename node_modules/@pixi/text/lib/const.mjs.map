{"version": 3, "file": "const.mjs", "sources": ["../src/const.ts"], "sourcesContent": ["/**\n * Constants that define the type of gradient on text.\n * @static\n * @memberof PIXI\n * @type {object}\n */\nexport enum TEXT_GRADIENT\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * Vertical gradient\n     * @default 0\n     */\n    LINEAR_VERTICAL = 0,\n    /**\n     * Linear gradient\n     * @default 1\n     */\n    LINEAR_HORIZONTAL = 1\n}\n"], "names": ["TEXT_GRADIENT"], "mappings": "AAMY,IAAA,gBAAAA,kBAAAA,oBAORA,eAAA,eAAA,kBAAkB,CAAlB,IAAA,mBAKAA,eAAA,eAAA,oBAAoB,CAApB,IAAA,qBAZQA,iBAAA,iBAAA,CAAA,CAAA;"}