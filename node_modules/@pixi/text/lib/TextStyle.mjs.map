{"version": 3, "file": "TextStyle.mjs", "sources": ["../src/TextStyle.ts"], "sourcesContent": ["// disabling eslint for now, going to rewrite this in v5\n/* eslint-disable */\n\nimport { TEXT_GRADIENT } from './const';\nimport { Color } from '@pixi/core';\n\nexport type TextStyleAlign = 'left'|'center'|'right'|'justify';\nexport type TextStyleFill = string|string[]|number|number[]|CanvasGradient|CanvasPattern;\nexport type TextStyleFontStyle = 'normal'|'italic'|'oblique';\nexport type TextStyleFontVariant = 'normal'|'small-caps';\nexport type TextStyleFontWeight = 'normal'|'bold'|'bolder'|'lighter'|'100'|'200'|'300'|'400'|'500'|'600'|'700'|'800'|'900';\nexport type TextStyleLineJoin = 'miter'|'round'|'bevel';\nexport type TextStyleTextBaseline = 'alphabetic'|'top'|'hanging'|'middle'|'ideographic'|'bottom';\nexport type TextStyleWhiteSpace = 'normal'|'pre'|'pre-line';\n\n/**\n * Generic interface for TextStyle options.\n * @memberof PIXI\n */\nexport interface ITextStyle {\n    /**\n     * Alignment for multiline text, does not affect single line text\n     * @type {'left'|'center'|'right'|'justify'}\n     */\n    align: TextStyleAlign;\n    /** Indicates if lines can be wrapped within words, it needs wordWrap to be set to true */\n    breakWords: boolean;\n    /** Set a drop shadow for the text */\n    dropShadow: boolean;\n    /** Set alpha for the drop shadow */\n    dropShadowAlpha: number;\n    /** Set a angle of the drop shadow */\n    dropShadowAngle: number;\n    /** Set a shadow blur radius */\n    dropShadowBlur: number;\n    /** A fill style to be used on the dropshadow e.g., 'red', '#00FF00' */\n    dropShadowColor: string|number;\n    /** Set a distance of the drop shadow */\n    dropShadowDistance: number;\n    /**\n     * A canvas fillstyle that will be used on the text e.g., 'red', '#00FF00'.\n     * Can be an array to create a gradient, e.g., `['#000000','#FFFFFF']`\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle|MDN}\n     * @type {string|string[]|number|number[]|CanvasGradient|CanvasPattern}\n     */\n    fill: TextStyleFill;\n    /**\n     * If fill is an array of colours to create a gradient, this can change the\n     * type/direction of the gradient. See {@link PIXI.TEXT_GRADIENT}\n     * @type {PIXI.TEXT_GRADIENT}\n     */\n    fillGradientType: TEXT_GRADIENT;\n    /**\n     * If fill is an array of colours to create a gradient, this array can set\n     * the stop points (numbers between 0 and 1) for the color, overriding the\n     * default behaviour of evenly spacing them.\n     */\n    fillGradientStops: number[];\n    /**\n     * The font family, can be a single font name, or a list of names where the first\n     * is the preferred font.\n     */\n    fontFamily: string | string[];\n    /**\n     * The font size (as a number it converts to px, but as a string,\n     * equivalents are '26px','20pt','160%' or '1.6em')\n     */\n    fontSize: number | string;\n    /**\n     * The font style.\n     * @type {'normal'|'italic'|'oblique'}\n     */\n    fontStyle: TextStyleFontStyle;\n    /**\n     * The font variant.\n     * @type {'normal'|'small-caps'}\n     */\n    fontVariant: TextStyleFontVariant;\n    /**\n     * The font weight.\n     * @type {'normal'|'bold'|'bolder'|'lighter'|'100'|'200'|'300'|'400'|'500'|'600'|'700'|'800'|'900'}\n     */\n    fontWeight: TextStyleFontWeight;\n    /** The height of the line, a number that represents the vertical space that a letter uses. */\n    leading: number;\n    /** The amount of spacing between letters, default is 0 */\n    letterSpacing: number;\n    /** The line height, a number that represents the vertical space that a letter uses */\n    lineHeight: number;\n    /**\n     * The lineJoin property sets the type of corner created, it can resolve\n     * spiked text issues. Possible values \"miter\" (creates a sharp corner),\n     * \"round\" (creates a round corner) or \"bevel\" (creates a squared corner).\n     * @type {'miter'|'round'|'bevel'}\n     */\n    lineJoin: TextStyleLineJoin;\n    /**\n     * The miter limit to use when using the 'miter' lineJoin mode. This can reduce\n     * or increase the spikiness of rendered text.\n     */\n    miterLimit: number;\n    /**\n     * Occasionally some fonts are cropped. Adding some padding will prevent this from\n     * happening by adding padding to all sides of the text.\n     */\n    padding: number;\n    /** A canvas fillstyle that will be used on the text stroke, e.g., 'blue', '#FCFF00' */\n    stroke: string|number;\n    /** A number that represents the thickness of the stroke. A value of 0 will disable stroke. */\n    strokeThickness: number;\n    /**\n     * The baseline of the text that is rendered.\n     * @type {'alphabetic'|'top'|'hanging'|'middle'|'ideographic'|'bottom'}\n     */\n    textBaseline: TextStyleTextBaseline;\n    /** Trim transparent borders */\n    trim: boolean;\n    /**\n     * Determines whether newlines & spaces are collapsed or preserved \"normal\"\n     * (collapse, collapse), \"pre\" (preserve, preserve) | \"pre-line\" (preserve,\n     * collapse). It needs wordWrap to be set to true.\n     * @type {'normal'|'pre'|'pre-line'}\n     */\n    whiteSpace: TextStyleWhiteSpace;\n    /** Indicates if word wrap should be used */\n    wordWrap: boolean;\n    /** The width at which text will wrap, it needs wordWrap to be set to true */\n    wordWrapWidth: number;\n}\n\nconst genericFontFamilies = [\n    'serif',\n    'sans-serif',\n    'monospace',\n    'cursive',\n    'fantasy',\n    'system-ui',\n];\n\n/**\n * A TextStyle Object contains information to decorate a Text objects.\n *\n * An instance can be shared between multiple Text objects; then changing the style will update all text objects using it.\n *\n * A tool can be used to generate a text style [here](https://pixijs.io/pixi-text-style).\n *\n * @memberof PIXI\n * @example\n * import { TextStyle } from 'pixi.js';\n * const style = new TextStyle({\n *   fontFamily: ['Helvetica', 'Arial', 'sans-serif'],\n *   fontSize: 36,\n * });\n */\nexport class TextStyle implements ITextStyle\n{\n    /**\n     * Default style options used for all TextStyle instances.\n     * @type {PIXI.ITextStyle}\n     */\n    public static defaultStyle: ITextStyle = {\n        /**\n         * See {@link PIXI.TextStyle.align}\n         * @type {'left'|'center'|'right'|'justify'}\n         */\n        align: 'left',\n        /** See {@link PIXI.TextStyle.breakWords} */\n        breakWords: false,\n        /** See {@link PIXI.TextStyle.dropShadow} */\n        dropShadow: false,\n        /** See {@link PIXI.TextStyle.dropShadowAlpha} */\n        dropShadowAlpha: 1,\n        /**\n         * See {@link PIXI.TextStyle.dropShadowAngle}\n         * @type {number}\n         * @default Math.PI / 6\n         */\n        dropShadowAngle: Math.PI / 6,\n        /** See {@link PIXI.TextStyle.dropShadowBlur} */\n        dropShadowBlur: 0,\n        /**\n         * See {@link PIXI.TextStyle.dropShadowColor}\n         * @type {string|number}\n         */\n        dropShadowColor: 'black',\n        /** See {@link PIXI.TextStyle.dropShadowDistance} */\n        dropShadowDistance: 5,\n        /**\n         * See {@link PIXI.TextStyle.fill}\n         * @type {string|string[]|number|number[]|CanvasGradient|CanvasPattern}\n         */\n        fill: 'black',\n        /**\n         * See {@link PIXI.TextStyle.fillGradientType}\n         * @type {PIXI.TEXT_GRADIENT}\n         * @default PIXI.TEXT_GRADIENT.LINEAR_VERTICAL\n         */\n        fillGradientType: TEXT_GRADIENT.LINEAR_VERTICAL,\n        /**\n         * See {@link PIXI.TextStyle.fillGradientStops}\n         * @type {number[]}\n         * @default []\n         */\n        fillGradientStops: [],\n        /**\n         * See {@link PIXI.TextStyle.fontFamily}\n         * @type {string|string[]}\n         */\n        fontFamily: 'Arial',\n        /**\n         * See {@link PIXI.TextStyle.fontSize}\n         * @type {number|string} \n         */\n        fontSize: 26,\n        /**\n         * See {@link PIXI.TextStyle.fontStyle}\n         * @type {'normal'|'italic'|'oblique'}\n         */\n        fontStyle: 'normal',\n        /**\n         * See {@link PIXI.TextStyle.fontVariant}\n         * @type {'normal'|'small-caps'}\n         */\n        fontVariant: 'normal',\n        /**\n         * See {@link PIXI.TextStyle.fontWeight}\n         * @type {'normal'|'bold'|'bolder'|'lighter'|'100'|'200'|'300'|'400'|'500'|'600'|'700'|'800'|'900'}\n         */\n        fontWeight: 'normal',\n        /** See {@link PIXI.TextStyle.leading} */\n        leading: 0,\n        /** See {@link PIXI.TextStyle.letterSpacing} */\n        letterSpacing: 0,\n        /** See {@link PIXI.TextStyle.lineHeight} */\n        lineHeight: 0,\n        /**\n         * See {@link PIXI.TextStyle.lineJoin}\n         * @type {'miter'|'round'|'bevel'}\n         */\n        lineJoin: 'miter',\n        /** See {@link PIXI.TextStyle.miterLimit} */\n        miterLimit: 10,\n        /** See {@link PIXI.TextStyle.padding} */\n        padding: 0,\n        /**\n         * See {@link PIXI.TextStyle.stroke}\n         * @type {string|number}\n         */\n        stroke: 'black',\n        /** See {@link PIXI.TextStyle.strokeThickness} */\n        strokeThickness: 0,\n        /**\n         * See {@link PIXI.TextStyle.textBaseline} \n         * @type {'alphabetic'|'top'|'hanging'|'middle'|'ideographic'|'bottom'}\n         */\n        textBaseline: 'alphabetic',\n        /** See {@link PIXI.TextStyle.trim} */\n        trim: false,\n        /**\n         * See {@link PIXI.TextStyle.whiteSpace}\n         * @type {'normal'|'pre'|'pre-line'}\n         */\n        whiteSpace: 'pre',\n        /** See {@link PIXI.TextStyle.wordWrap} */\n        wordWrap: false,\n        /** See {@link PIXI.TextStyle.wordWrapWidth} */\n        wordWrapWidth: 100,\n    };\n\n    public styleID: number;\n\n    protected _align: TextStyleAlign;\n    protected _breakWords: boolean;\n    protected _dropShadow: boolean;\n    protected _dropShadowAlpha: number;\n    protected _dropShadowAngle: number;\n    protected _dropShadowBlur: number;\n    protected _dropShadowColor: string|number;\n    protected _dropShadowDistance: number;\n    protected _fill: TextStyleFill;\n    protected _fillGradientType: TEXT_GRADIENT;\n    protected _fillGradientStops: number[];\n    protected _fontFamily: string|string[];\n    protected _fontSize: number|string;\n    protected _fontStyle: TextStyleFontStyle;\n    protected _fontVariant: TextStyleFontVariant;\n    protected _fontWeight: TextStyleFontWeight;\n    protected _letterSpacing: number;\n    protected _lineHeight: number;\n    protected _lineJoin: TextStyleLineJoin;\n    protected _miterLimit: number;\n    protected _padding: number;\n    protected _stroke: string|number;\n    protected _strokeThickness: number;\n    protected _textBaseline: TextStyleTextBaseline;\n    protected _trim: boolean;\n    protected _whiteSpace: TextStyleWhiteSpace;\n    protected _wordWrap: boolean;\n    protected _wordWrapWidth: number;\n    protected _leading: number;\n\n    /**\n     * @param style - TextStyle properties to be set on the text. See {@link PIXI.TextStyle.defaultStyle}\n     *       for the default values.\n     */\n    constructor(style?: Partial<ITextStyle>)\n    {\n        this.styleID = 0;\n\n        this.reset();\n\n        deepCopyProperties(this, style, style);\n    }\n\n    /**\n     * Creates a new TextStyle object with the same values as this one.\n     * Note that the only the properties of the object are cloned.\n     *\n     * @return New cloned TextStyle object\n     */\n    public clone(): TextStyle\n    {\n        const clonedProperties: Partial<ITextStyle> = {};\n\n        deepCopyProperties(clonedProperties, this, TextStyle.defaultStyle);\n\n        return new TextStyle(clonedProperties);\n    }\n\n    /** Resets all properties to the defaults specified in TextStyle.prototype._default */\n    public reset(): void\n    {\n        deepCopyProperties(this, TextStyle.defaultStyle, TextStyle.defaultStyle);\n    }\n\n    /**\n     * Alignment for multiline text, does not affect single line text.\n     *\n     * @member {'left'|'center'|'right'|'justify'}\n     */\n    get align(): TextStyleAlign\n    {\n        return this._align;\n    }\n    set align(align: TextStyleAlign)\n    {\n        if (this._align !== align)\n        {\n            this._align = align;\n            this.styleID++;\n        }\n    }\n\n    /** Indicates if lines can be wrapped within words, it needs wordWrap to be set to true. */\n    get breakWords(): boolean\n    {\n        return this._breakWords;\n    }\n    set breakWords(breakWords: boolean)\n    {\n        if (this._breakWords !== breakWords)\n        {\n            this._breakWords = breakWords;\n            this.styleID++;\n        }\n    }\n\n    /** Set a drop shadow for the text. */\n    get dropShadow(): boolean\n    {\n        return this._dropShadow;\n    }\n    set dropShadow(dropShadow: boolean)\n    {\n        if (this._dropShadow !== dropShadow)\n        {\n            this._dropShadow = dropShadow;\n            this.styleID++;\n        }\n    }\n\n    /** Set alpha for the drop shadow. */\n    get dropShadowAlpha(): number\n    {\n        return this._dropShadowAlpha;\n    }\n    set dropShadowAlpha(dropShadowAlpha: number)\n    {\n        if (this._dropShadowAlpha !== dropShadowAlpha)\n        {\n            this._dropShadowAlpha = dropShadowAlpha;\n            this.styleID++;\n        }\n    }\n\n    /** Set a angle of the drop shadow. */\n    get dropShadowAngle(): number\n    {\n        return this._dropShadowAngle;\n    }\n    set dropShadowAngle(dropShadowAngle: number)\n    {\n        if (this._dropShadowAngle !== dropShadowAngle)\n        {\n            this._dropShadowAngle = dropShadowAngle;\n            this.styleID++;\n        }\n    }\n\n    /** Set a shadow blur radius. */\n    get dropShadowBlur(): number\n    {\n        return this._dropShadowBlur;\n    }\n    set dropShadowBlur(dropShadowBlur: number)\n    {\n        if (this._dropShadowBlur !== dropShadowBlur)\n        {\n            this._dropShadowBlur = dropShadowBlur;\n            this.styleID++;\n        }\n    }\n\n    /** A fill style to be used on the dropshadow e.g., 'red', '#00FF00'. */\n    get dropShadowColor(): number | string\n    {\n        return this._dropShadowColor;\n    }\n    set dropShadowColor(dropShadowColor: number | string)\n    {\n        const outputColor = getColor(dropShadowColor);\n        if (this._dropShadowColor !== outputColor)\n        {\n            this._dropShadowColor = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /** Set a distance of the drop shadow. */\n    get dropShadowDistance(): number\n    {\n        return this._dropShadowDistance;\n    }\n    set dropShadowDistance(dropShadowDistance: number)\n    {\n        if (this._dropShadowDistance !== dropShadowDistance)\n        {\n            this._dropShadowDistance = dropShadowDistance;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A canvas fillstyle that will be used on the text e.g., 'red', '#00FF00'.\n     *\n     * Can be an array to create a gradient e.g., `['#000000','#FFFFFF']`\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle|MDN}\n     *\n     * @member {string|string[]|number|number[]|CanvasGradient|CanvasPattern}\n     */\n    get fill(): TextStyleFill\n    {\n        return this._fill;\n    }\n    set fill(fill: TextStyleFill)\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        // TODO: Not sure if getColor works properly with CanvasGradient and/or CanvasPattern, can't pass in\n        //       without casting here.\n        const outputColor = getColor(fill as any);\n        if (this._fill !== outputColor)\n        {\n            this._fill = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * If fill is an array of colours to create a gradient, this can change the type/direction of the gradient.\n     *\n     * @type {PIXI.TEXT_GRADIENT}\n     */\n    get fillGradientType(): TEXT_GRADIENT\n    {\n        return this._fillGradientType;\n    }\n    set fillGradientType(fillGradientType: TEXT_GRADIENT)\n    {\n        if (this._fillGradientType !== fillGradientType)\n        {\n            this._fillGradientType = fillGradientType;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * If fill is an array of colours to create a gradient, this array can set the stop points\n     * (numbers between 0 and 1) for the color, overriding the default behaviour of evenly spacing them.\n     */\n    get fillGradientStops(): number[]\n    {\n        return this._fillGradientStops;\n    }\n    set fillGradientStops(fillGradientStops: number[])\n    {\n        if (!areArraysEqual(this._fillGradientStops,fillGradientStops))\n        {\n            this._fillGradientStops = fillGradientStops;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font family, can be a single font name, or a list of names where the first\n     * is the preferred font.\n     */\n    get fontFamily(): string | string[]\n    {\n        return this._fontFamily;\n    }\n    set fontFamily(fontFamily: string | string[])\n    {\n        if (this.fontFamily !== fontFamily)\n        {\n            this._fontFamily = fontFamily;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font size\n     * (as a number it converts to px, but as a string, equivalents are '26px','20pt','160%' or '1.6em')\n     */\n    get fontSize(): number | string\n    {\n        return this._fontSize;\n    }\n    set fontSize(fontSize: number | string)\n    {\n        if (this._fontSize !== fontSize)\n        {\n            this._fontSize = fontSize;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font style.\n     *\n     * @member {'normal'|'italic'|'oblique'}\n     */\n    get fontStyle(): TextStyleFontStyle\n    {\n        return this._fontStyle;\n    }\n    set fontStyle(fontStyle: TextStyleFontStyle)\n    {\n        if (this._fontStyle !== fontStyle)\n        {\n            this._fontStyle = fontStyle;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font variant.\n     *\n     * @member {'normal'|'small-caps'}\n     */\n    get fontVariant(): TextStyleFontVariant\n    {\n        return this._fontVariant;\n    }\n    set fontVariant(fontVariant: TextStyleFontVariant)\n    {\n        if (this._fontVariant !== fontVariant)\n        {\n            this._fontVariant = fontVariant;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The font weight.\n     *\n     * @member {'normal'|'bold'|'bolder'|'lighter'|'100'|'200'|'300'|'400'|'500'|'600'|'700'|'800'|'900'}\n     */\n    get fontWeight(): TextStyleFontWeight\n    {\n        return this._fontWeight;\n    }\n    set fontWeight(fontWeight: TextStyleFontWeight)\n    {\n        if (this._fontWeight !== fontWeight)\n        {\n            this._fontWeight = fontWeight;\n            this.styleID++;\n        }\n    }\n\n    /** The amount of spacing between letters, default is 0. */\n    get letterSpacing(): number\n    {\n        return this._letterSpacing;\n    }\n    set letterSpacing(letterSpacing: number)\n    {\n        if (this._letterSpacing !== letterSpacing)\n        {\n            this._letterSpacing = letterSpacing;\n            this.styleID++;\n        }\n    }\n\n    /** The line height, a number that represents the vertical space that a letter uses. */\n    get lineHeight(): number\n    {\n        return this._lineHeight;\n    }\n    set lineHeight(lineHeight: number)\n    {\n        if (this._lineHeight !== lineHeight)\n        {\n            this._lineHeight = lineHeight;\n            this.styleID++;\n        }\n    }\n\n    /** The space between lines. */\n    get leading(): number\n    {\n        return this._leading;\n    }\n    set leading(leading: number)\n    {\n        if (this._leading !== leading)\n        {\n            this._leading = leading;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The lineJoin property sets the type of corner created, it can resolve spiked text issues.\n     * Default is 'miter' (creates a sharp corner).\n     *\n     * @member {'miter'|'round'|'bevel'}\n     */\n    get lineJoin(): TextStyleLineJoin\n    {\n        return this._lineJoin;\n    }\n    set lineJoin(lineJoin: TextStyleLineJoin)\n    {\n        if (this._lineJoin !== lineJoin)\n        {\n            this._lineJoin = lineJoin;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The miter limit to use when using the 'miter' lineJoin mode.\n     *\n     * This can reduce or increase the spikiness of rendered text.\n     */\n    get miterLimit(): number\n    {\n        return this._miterLimit;\n    }\n    set miterLimit(miterLimit: number)\n    {\n        if (this._miterLimit !== miterLimit)\n        {\n            this._miterLimit = miterLimit;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Occasionally some fonts are cropped. Adding some padding will prevent this from happening\n     * by adding padding to all sides of the text.\n     */\n    get padding(): number\n    {\n        return this._padding;\n    }\n    set padding(padding: number)\n    {\n        if (this._padding !== padding)\n        {\n            this._padding = padding;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A canvas fillstyle that will be used on the text stroke, e.g., 'blue', '#FCFF00'\n     */\n    get stroke(): string | number\n    {\n        return this._stroke;\n    }\n    set stroke(stroke: string | number)\n    {\n        // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n        //       the setter converts to string. See this thread for more details:\n        //       https://github.com/microsoft/TypeScript/issues/2521\n        const outputColor = getColor(stroke);\n        if (this._stroke !== outputColor)\n        {\n            this._stroke = outputColor;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * A number that represents the thickness of the stroke.\n     *\n     * @default 0\n     */\n    get strokeThickness(): number\n    {\n        return this._strokeThickness;\n    }\n    set strokeThickness(strokeThickness: number)\n    {\n        if (this._strokeThickness !== strokeThickness)\n        {\n            this._strokeThickness = strokeThickness;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * The baseline of the text that is rendered.\n     *\n     * @member {'alphabetic'|'top'|'hanging'|'middle'|'ideographic'|'bottom'}\n     */\n    get textBaseline(): TextStyleTextBaseline\n    {\n        return this._textBaseline;\n    }\n    set textBaseline(textBaseline: TextStyleTextBaseline)\n    {\n        if (this._textBaseline !== textBaseline)\n        {\n            this._textBaseline = textBaseline;\n            this.styleID++;\n        }\n    }\n\n    /** Trim transparent borders. */\n    get trim(): boolean\n    {\n        return this._trim;\n    }\n    set trim(trim: boolean)\n    {\n        if (this._trim !== trim)\n        {\n            this._trim = trim;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * How newlines and spaces should be handled.\n     * Default is 'pre' (preserve, preserve).\n     *\n     *  value       | New lines     |   Spaces\n     *  ---         | ---           |   ---\n     * 'normal'     | Collapse      |   Collapse\n     * 'pre'        | Preserve      |   Preserve\n     * 'pre-line'   | Preserve      |   Collapse\n     *\n     * @member {'normal'|'pre'|'pre-line'}\n     */\n    get whiteSpace(): TextStyleWhiteSpace\n    {\n        return this._whiteSpace;\n    }\n    set whiteSpace(whiteSpace: TextStyleWhiteSpace)\n    {\n        if (this._whiteSpace !== whiteSpace)\n        {\n            this._whiteSpace = whiteSpace;\n            this.styleID++;\n        }\n    }\n\n    /** Indicates if word wrap should be used. */\n    get wordWrap(): boolean\n    {\n        return this._wordWrap;\n    }\n    set wordWrap(wordWrap: boolean)\n    {\n        if (this._wordWrap !== wordWrap)\n        {\n            this._wordWrap = wordWrap;\n            this.styleID++;\n        }\n    }\n\n    /** The width at which text will wrap, it needs wordWrap to be set to true. */\n    get wordWrapWidth(): number\n    {\n        return this._wordWrapWidth;\n    }\n    set wordWrapWidth(wordWrapWidth: number)\n    {\n        if (this._wordWrapWidth !== wordWrapWidth)\n        {\n            this._wordWrapWidth = wordWrapWidth;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Generates a font style string to use for `TextMetrics.measureFont()`.\n     *\n     * @return Font style string, for passing to `TextMetrics.measureFont()`\n     */\n    public toFontString(): string\n    {\n        // build canvas api font setting from individual components. Convert a numeric this.fontSize to px\n        const fontSizeString = (typeof this.fontSize === 'number') ? `${this.fontSize}px` : this.fontSize;\n\n        // Clean-up fontFamily property by quoting each font name\n        // this will support font names with spaces\n        let fontFamilies: string|string[] = this.fontFamily;\n\n        if (!Array.isArray(this.fontFamily))\n        {\n            fontFamilies = this.fontFamily.split(',');\n        }\n\n        for (let i = fontFamilies.length - 1; i >= 0; i--)\n        {\n            // Trim any extra white-space\n            let fontFamily = fontFamilies[i].trim();\n\n            // Check if font already contains strings\n            if (!(/([\\\"\\'])[^\\'\\\"]+\\1/).test(fontFamily) && !genericFontFamilies.includes(fontFamily))\n            {\n                fontFamily = `\"${fontFamily}\"`;\n            }\n            (fontFamilies as string[])[i] = fontFamily;\n        }\n\n        return `${this.fontStyle} ${this.fontVariant} ${this.fontWeight} ${fontSizeString} ${(fontFamilies as string[]).join(',')}`;\n    }\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * This version can also convert array of colors\n * @private\n * @param color\n * @return The color as a string.\n */\nfunction getColor(color: (string|number)[]): string[];\nfunction getColor(color: string|number): string;\nfunction getColor(color: string|number|(string|number)[]): string|string[]\n{\n    const temp = Color.shared;\n\n    const format = (color: string | number) => {\n        const res = temp.setValue(color);\n        return res.alpha === 1 ? res.toHex() : res.toRgbaString();\n    }\n\n    if (!Array.isArray(color))\n    {\n        return format(color);\n    }\n    else\n    {\n        return color.map(format);\n    }\n}\n\n/**\n * Utility function to convert hexadecimal colors to strings, and simply return the color if it's a string.\n * This version can also convert array of colors\n * @private\n * @param array1 - First array to compare\n * @param array2 - Second array to compare\n * @return Do the arrays contain the same values in the same order\n */\nfunction areArraysEqual<T>(array1: T[], array2: T[]): boolean\n{\n    if (!Array.isArray(array1) || !Array.isArray(array2))\n    {\n        return false;\n    }\n\n    if (array1.length !== array2.length)\n    {\n        return false;\n    }\n\n    for (let i = 0; i < array1.length; ++i)\n    {\n        if (array1[i] !== array2[i])\n        {\n            return false;\n        }\n    }\n\n    return true;\n}\n\n/**\n * Utility function to ensure that object properties are copied by value, and not by reference\n * @private\n * @param target - Target object to copy properties into\n * @param source - Source object for the properties to copy\n * @param propertyObj - Object containing properties names we want to loop over\n */\nfunction deepCopyProperties(target: Record<string, any>, source: Record<string, any>, propertyObj: Record<string, any>): void {\n    for (const prop in propertyObj) {\n        if (Array.isArray(source[prop])) {\n            target[prop] = source[prop].slice();\n        } else {\n            target[prop] = source[prop];\n        }\n    }\n}\n"], "names": ["_TextStyle", "color"], "mappings": ";;AAkIA,MAAM,sBAAsB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,GAiBa,aAAN,MAAMA,YACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAsJI,YAAY,OACZ;AACS,SAAA,UAAU,GAEf,KAAK,SAEL,mBAAmB,MAAM,OAAO,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,QACP;AACI,UAAM,mBAAwC,CAAA;AAE9C,WAAA,mBAAmB,kBAAkB,MAAMA,YAAU,YAAY,GAE1D,IAAIA,YAAU,gBAAgB;AAAA,EACzC;AAAA;AAAA,EAGO,QACP;AACI,uBAAmB,MAAMA,YAAU,cAAcA,YAAU,YAAY;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,MAAM,OACV;AACQ,SAAK,WAAW,UAEhB,KAAK,SAAS,OACd,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,gBAAgB,eAErB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,gBAAgB,eAErB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,kBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB,iBACpB;AACQ,SAAK,qBAAqB,oBAE1B,KAAK,mBAAmB,iBACxB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,kBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB,iBACpB;AACQ,SAAK,qBAAqB,oBAE1B,KAAK,mBAAmB,iBACxB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,iBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,eAAe,gBACnB;AACQ,SAAK,oBAAoB,mBAEzB,KAAK,kBAAkB,gBACvB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,kBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB,iBACpB;AACU,UAAA,cAAc,SAAS,eAAe;AACxC,SAAK,qBAAqB,gBAE1B,KAAK,mBAAmB,aACxB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,qBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,mBAAmB,oBACvB;AACQ,SAAK,wBAAwB,uBAE7B,KAAK,sBAAsB,oBAC3B,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,OACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,KAAK,MACT;AAMU,UAAA,cAAc,SAAS,IAAW;AACpC,SAAK,UAAU,gBAEf,KAAK,QAAQ,aACb,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,mBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,iBAAiB,kBACrB;AACQ,SAAK,sBAAsB,qBAE3B,KAAK,oBAAoB,kBACzB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,oBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,kBAAkB,mBACtB;AACS,mBAAe,KAAK,oBAAmB,iBAAiB,MAEzD,KAAK,qBAAqB,mBAC1B,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,eAAe,eAEpB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,UACb;AACQ,SAAK,cAAc,aAEnB,KAAK,YAAY,UACjB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,WACd;AACQ,SAAK,eAAe,cAEpB,KAAK,aAAa,WAClB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,cACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,aAChB;AACQ,SAAK,iBAAiB,gBAEtB,KAAK,eAAe,aACpB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,gBAAgB,eAErB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,gBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,cAAc,eAClB;AACQ,SAAK,mBAAmB,kBAExB,KAAK,iBAAiB,eACtB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,gBAAgB,eAErB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,UACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ,SACZ;AACQ,SAAK,aAAa,YAElB,KAAK,WAAW,SAChB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,UACb;AACQ,SAAK,cAAc,aAEnB,KAAK,YAAY,UACjB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,gBAAgB,eAErB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ,SACZ;AACQ,SAAK,aAAa,YAElB,KAAK,WAAW,SAChB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,OAAO,QACX;AAIU,UAAA,cAAc,SAAS,MAAM;AAC/B,SAAK,YAAY,gBAEjB,KAAK,UAAU,aACf,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,kBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB,iBACpB;AACQ,SAAK,qBAAqB,oBAE1B,KAAK,mBAAmB,iBACxB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,aAAa,cACjB;AACQ,SAAK,kBAAkB,iBAEvB,KAAK,gBAAgB,cACrB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,OACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,KAAK,MACT;AACQ,SAAK,UAAU,SAEf,KAAK,QAAQ,MACb,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,YACf;AACQ,SAAK,gBAAgB,eAErB,KAAK,cAAc,YACnB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,WACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,UACb;AACQ,SAAK,cAAc,aAEnB,KAAK,YAAY,UACjB,KAAK;AAAA,EAEb;AAAA;AAAA,EAGA,IAAI,gBACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,cAAc,eAClB;AACQ,SAAK,mBAAmB,kBAExB,KAAK,iBAAiB,eACtB,KAAK;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eACP;AAEU,UAAA,iBAAkB,OAAO,KAAK,YAAa,WAAY,GAAG,KAAK,QAAQ,OAAO,KAAK;AAIzF,QAAI,eAAgC,KAAK;AAEpC,UAAM,QAAQ,KAAK,UAAU,MAE9B,eAAe,KAAK,WAAW,MAAM,GAAG;AAG5C,aAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAC9C;AAEI,UAAI,aAAa,aAAa,CAAC,EAAE,KAAK;AAGlC,OAAE,qBAAsB,KAAK,UAAU,KAAK,CAAC,oBAAoB,SAAS,UAAU,MAEpF,aAAa,IAAI,UAAU,MAE9B,aAA0B,CAAC,IAAI;AAAA,IACpC;AAEA,WAAO,GAAG,KAAK,SAAS,IAAI,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,cAAc,IAAK,aAA0B,KAAK,GAAG,CAAC;AAAA,EAC7H;AACJ;AA7rBa,WAMK,eAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrC,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA;AAAA,EAEZ,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,iBAAiB,KAAK,KAAK;AAAA;AAAA,EAE3B,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,iBAAiB;AAAA;AAAA,EAEjB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMN,kBAAkB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA;AAAA,EAET,eAAe;AAAA;AAAA,EAEf,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,UAAU;AAAA;AAAA,EAEV,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,QAAQ;AAAA;AAAA,EAER,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,cAAc;AAAA;AAAA,EAEd,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,YAAY;AAAA;AAAA,EAEZ,UAAU;AAAA;AAAA,EAEV,eAAe;AACnB;AAjHG,IAAM,YAAN;AAwsBP,SAAS,SAAS,OAClB;AACI,QAAM,OAAO,MAAM,QAEb,SAAS,CAACC,WAA2B;AACjC,UAAA,MAAM,KAAK,SAASA,MAAK;AAC/B,WAAO,IAAI,UAAU,IAAI,IAAI,UAAU,IAAI;EAAa;AAGvD,SAAA,MAAM,QAAQ,KAAK,IAMb,MAAM,IAAI,MAAM,IAJhB,OAAO,KAAK;AAM3B;AAUA,SAAS,eAAkB,QAAa,QACxC;AAMI,MALI,CAAC,MAAM,QAAQ,MAAM,KAAK,CAAC,MAAM,QAAQ,MAAM,KAK/C,OAAO,WAAW,OAAO;AAElB,WAAA;AAGX,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE;AAEjC,QAAI,OAAO,CAAC,MAAM,OAAO,CAAC;AAEf,aAAA;AAIR,SAAA;AACX;AASA,SAAS,mBAAmB,QAA6B,QAA6B,aAAwC;AAC1H,aAAW,QAAQ;AACX,UAAM,QAAQ,OAAO,IAAI,CAAC,IAC1B,OAAO,IAAI,IAAI,OAAO,IAAI,EAAE,MAAM,IAElC,OAAO,IAAI,IAAI,OAAO,IAAI;AAGtC;"}