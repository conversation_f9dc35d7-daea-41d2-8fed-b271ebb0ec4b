{"version": 3, "file": "LineStyle.js", "sources": ["../../src/styles/LineStyle.ts"], "sourcesContent": ["import { LINE_CAP, LINE_JOIN } from '../const';\nimport { FillStyle } from './FillStyle';\n\n/**\n * Represents the line style for Graphics.\n * @memberof PIXI\n */\nexport class LineStyle extends FillStyle\n{\n    /** The width (thickness) of any lines drawn. */\n    public width = 0;\n\n    /** The alignment of any lines drawn (0.5 = middle, 1 = outer, 0 = inner). WebGL only. */\n    public alignment = 0.5;\n\n    /** If true the lines will be draw using LINES instead of TRIANGLE_STRIP. */\n    public native = false;\n\n    /**\n     * Line cap style.\n     * @member {PIXI.LINE_CAP}\n     * @default PIXI.LINE_CAP.BUTT\n     */\n    public cap = LINE_CAP.BUTT;\n\n    /**\n     * Line join style.\n     * @member {PIXI.LINE_JOIN}\n     * @default PIXI.LINE_JOIN.MITER\n     */\n    public join = LINE_JOIN.MITER;\n\n    /** Miter limit. */\n    public miterLimit = 10;\n\n    /** Clones the object. */\n    public clone(): LineStyle\n    {\n        const obj = new LineStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n        obj.width = this.width;\n        obj.alignment = this.alignment;\n        obj.native = this.native;\n        obj.cap = this.cap;\n        obj.join = this.join;\n        obj.miterLimit = this.miterLimit;\n\n        return obj;\n    }\n\n    /** Reset the line style to default. */\n    public reset(): void\n    {\n        super.reset();\n\n        // Override default line style color\n        this.color = 0x0;\n\n        this.alignment = 0.5;\n        this.width = 0;\n        this.native = false;\n        this.cap = LINE_CAP.BUTT;\n        this.join = LINE_JOIN.MITER;\n        this.miterLimit = 10;\n    }\n}\n"], "names": ["FillStyle", "LINE_CAP", "LINE_JOIN"], "mappings": ";;AAOO,MAAM,kBAAkBA,UAAAA,UAC/B;AAAA,EADO,cAAA;AAAA,UAAA,GAAA,SAAA,GAGH,KAAO,QAAQ,GAGf,KAAO,YAAY,KAGnB,KAAO,SAAS,IAOhB,KAAO,MAAMC,OAAS,SAAA,MAOtB,KAAO,OAAOC,OAAU,UAAA,OAGxB,KAAO,aAAa;AAAA,EAAA;AAAA;AAAA,EAGb,QACP;AACU,UAAA,MAAM,IAAI;AAEhB,WAAA,IAAI,QAAQ,KAAK,OACjB,IAAI,QAAQ,KAAK,OACjB,IAAI,UAAU,KAAK,SACnB,IAAI,SAAS,KAAK,QAClB,IAAI,UAAU,KAAK,SACnB,IAAI,QAAQ,KAAK,OACjB,IAAI,YAAY,KAAK,WACrB,IAAI,SAAS,KAAK,QAClB,IAAI,MAAM,KAAK,KACf,IAAI,OAAO,KAAK,MAChB,IAAI,aAAa,KAAK,YAEf;AAAA,EACX;AAAA;AAAA,EAGO,QACP;AACU,UAAA,MAAA,GAGN,KAAK,QAAQ,GAEb,KAAK,YAAY,KACjB,KAAK,QAAQ,GACb,KAAK,SAAS,IACd,KAAK,MAAMD,OAAAA,SAAS,MACpB,KAAK,OAAOC,OAAA,UAAU,OACtB,KAAK,aAAa;AAAA,EACtB;AACJ;;"}