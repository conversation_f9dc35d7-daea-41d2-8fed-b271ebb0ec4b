{"version": 3, "file": "FillStyle.js", "sources": ["../../src/styles/FillStyle.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\n\nimport type { Matrix } from '@pixi/core';\n\n/**\n * Fill style object for Graphics.\n * @memberof PIXI\n */\nexport class FillStyle\n{\n    /**\n     * The hex color value used when coloring the Graphics object.\n     * @default 0xFFFFFF\n     */\n    public color = 0xFFFFFF;\n\n    /** The alpha value used when filling the Graphics object. */\n    public alpha = 1.0;\n\n    /**\n     * The texture to be used for the fill.\n     * @default 0\n     */\n    public texture: Texture = Texture.WHITE;\n\n    /**\n     * The transform applied to the texture.\n     * @default null\n     */\n    public matrix: Matrix = null;\n\n    /** If the current fill is visible. */\n    public visible = false;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /** Clones the object */\n    public clone(): FillStyle\n    {\n        const obj = new FillStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n\n        return obj;\n    }\n\n    /** Reset */\n    public reset(): void\n    {\n        this.color = 0xFFFFFF;\n        this.alpha = 1;\n        this.texture = Texture.WHITE;\n        this.matrix = null;\n        this.visible = false;\n    }\n\n    /** Destroy and don't use after this. */\n    public destroy(): void\n    {\n        this.texture = null;\n        this.matrix = null;\n    }\n}\n"], "names": ["Texture"], "mappings": ";;AAQO,MAAM,UACb;AAAA,EAyBI,cACA;AArBA,SAAO,QAAQ,UAGf,KAAO,QAAQ,GAMf,KAAO,UAAmBA,KAAQ,QAAA,OAMlC,KAAO,SAAiB,MAGxB,KAAO,UAAU,IAIb,KAAK,MAAM;AAAA,EACf;AAAA;AAAA,EAGO,QACP;AACU,UAAA,MAAM,IAAI;AAEhB,WAAA,IAAI,QAAQ,KAAK,OACjB,IAAI,QAAQ,KAAK,OACjB,IAAI,UAAU,KAAK,SACnB,IAAI,SAAS,KAAK,QAClB,IAAI,UAAU,KAAK,SAEZ;AAAA,EACX;AAAA;AAAA,EAGO,QACP;AACI,SAAK,QAAQ,UACb,KAAK,QAAQ,GACb,KAAK,UAAUA,KAAA,QAAQ,OACvB,KAAK,SAAS,MACd,KAAK,UAAU;AAAA,EACnB;AAAA;AAAA,EAGO,UACP;AACS,SAAA,UAAU,MACf,KAAK,SAAS;AAAA,EAClB;AACJ;;"}