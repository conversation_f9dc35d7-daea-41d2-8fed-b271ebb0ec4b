{"version": 3, "file": "GraphicsData.mjs", "sources": ["../src/GraphicsData.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>, <PERSON>, SHAPES } from '@pixi/core';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/**\n * A class to contain data useful for Graphics objects\n * @memberof PIXI\n */\nexport class GraphicsData\n{\n    /**\n     * The shape object to draw.\n     * @member {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle}\n     */\n    shape: IShape;\n\n    /** The style of the line. */\n    lineStyle: LineStyle;\n\n    /** The style of the fill. */\n    fillStyle: FillStyle;\n\n    /** The transform matrix. */\n    matrix: Matrix;\n\n    /** The type of the shape, see the Const.Shapes file for all the existing types, */\n    type: SHAPES;\n\n    /** The collection of points. */\n    points: number[] = [];\n\n    /** The collection of holes. */\n\n    holes: Array<GraphicsData> = [];\n\n    /**\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - the width of the line to draw\n     * @param lineStyle - the color of the line to draw\n     * @param matrix - Transform matrix\n     */\n    constructor(shape: IShape, fillStyle: FillStyle = null, lineStyle: LineStyle = null, matrix: Matrix = null)\n    {\n        this.shape = shape;\n        this.lineStyle = lineStyle;\n        this.fillStyle = fillStyle;\n        this.matrix = matrix;\n        this.type = shape.type;\n    }\n\n    /**\n     * Creates a new GraphicsData object with the same values as this one.\n     * @returns - Cloned GraphicsData object\n     */\n    public clone(): GraphicsData\n    {\n        return new GraphicsData(\n            this.shape,\n            this.fillStyle,\n            this.lineStyle,\n            this.matrix\n        );\n    }\n\n    /** Destroys the Graphics data. */\n    public destroy(): void\n    {\n        this.shape = null;\n        this.holes.length = 0;\n        this.holes = null;\n        this.points.length = 0;\n        this.points = null;\n        this.lineStyle = null;\n        this.fillStyle = null;\n    }\n}\n"], "names": [], "mappings": "AAQO,MAAM,aACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCI,YAAY,OAAe,YAAuB,MAAM,YAAuB,MAAM,SAAiB,MACtG;AAbA,SAAA,SAAmB,IAInB,KAAA,QAA6B,IAUzB,KAAK,QAAQ,OACb,KAAK,YAAY,WACjB,KAAK,YAAY,WACjB,KAAK,SAAS,QACd,KAAK,OAAO,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,QACP;AACI,WAAO,IAAI;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IAAA;AAAA,EAEb;AAAA;AAAA,EAGO,UACP;AACS,SAAA,QAAQ,MACb,KAAK,MAAM,SAAS,GACpB,KAAK,QAAQ,MACb,KAAK,OAAO,SAAS,GACrB,KAAK,SAAS,MACd,KAAK,YAAY,MACjB,KAAK,YAAY;AAAA,EACrB;AACJ;"}