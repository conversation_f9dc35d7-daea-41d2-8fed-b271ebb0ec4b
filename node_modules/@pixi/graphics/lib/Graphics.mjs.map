{"version": 3, "file": "Graphics.mjs", "sources": ["../src/Graphics.ts"], "sourcesContent": ["import {\n    BLEND_MODES,\n    Circle,\n    Color,\n    Ellipse,\n    Matrix,\n    PI_2,\n    Point,\n    Polygon,\n    Rectangle,\n    RoundedRectangle,\n    Shader,\n    SHAPES,\n    State,\n    Texture,\n    UniformGroup,\n} from '@pixi/core';\nimport { Container } from '@pixi/display';\nimport { curves, LINE_CAP, LINE_JOIN } from './const';\nimport { GraphicsGeometry } from './GraphicsGeometry';\nimport { FillStyle } from './styles/FillStyle';\nimport { LineStyle } from './styles/LineStyle';\nimport { ArcUtils, BezierUtils, QuadraticUtils } from './utils';\n\nimport type { BatchDrawCall, ColorSource, IPointData, IShape, Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * Batch element computed from Graphics geometry.\n * @memberof PIXI\n */\nexport interface IGraphicsBatchElement\n{\n    vertexData: Float32Array;\n    blendMode: BLEND_MODES;\n    indices: Uint16Array | Uint32Array;\n    uvs: Float32Array;\n    alpha: number;\n    worldAlpha: number;\n    _batchRGB: number[];\n    _tintRGB: number;\n    _texture: Texture;\n}\n\nexport interface IFillStyleOptions\n{\n    color?: ColorSource;\n    alpha?: number;\n    texture?: Texture;\n    matrix?: Matrix;\n}\n\nexport interface ILineStyleOptions extends IFillStyleOptions\n{\n    width?: number;\n    alignment?: number;\n    native?: boolean;\n    cap?: LINE_CAP;\n    join?: LINE_JOIN;\n    miterLimit?: number;\n}\n\n// a default shaders map used by graphics..\nconst DEFAULT_SHADERS: {[key: string]: Shader} = {};\n\nexport interface Graphics extends GlobalMixins.Graphics, Container {}\n\n/**\n * The Graphics class is primarily used to render primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.  However, you can also use a Graphics\n * object to build a list of primitives to use as a mask, or as a complex hitArea.\n *\n * Please note that due to legacy naming conventions, the behavior of some functions in this class\n * can be confusing.  Each call to `drawRect()`, `drawPolygon()`, etc. actually stores that primitive\n * in the Geometry class's GraphicsGeometry object for later use in rendering or hit testing - the\n * functions do not directly draw anything to the screen.  Similarly, the `clear()` function doesn't\n * change the screen, it simply resets the list of primitives, which can be useful if you want to\n * rebuild the contents of an existing Graphics object.\n *\n * Once a GraphicsGeometry list is built, you can re-use it in other Geometry objects as\n * an optimization, by passing it into a new Geometry object's constructor.  Because of this\n * ability, it's important to call `destroy()` on Geometry objects once you are done with them, to\n * properly dereference each GraphicsGeometry and prevent memory leaks.\n * @memberof PIXI\n */\nexport class Graphics extends Container\n{\n    /**\n     * Graphics curves resolution settings. If `adaptive` flag is set to `true`,\n     * the resolution is calculated based on the curve's length to ensure better visual quality.\n     * Adaptive draw works with `bezierCurveTo` and `quadraticCurveTo`.\n     * @static\n     * @property {boolean} [adaptive=true] - flag indicating if the resolution should be adaptive\n     * @property {number} [maxLength=10] - maximal length of a single segment of the curve (if adaptive = false, ignored)\n     * @property {number} [minSegments=8] - minimal number of segments in the curve (if adaptive = false, ignored)\n     * @property {number} [maxSegments=2048] - maximal number of segments in the curve (if adaptive = false, ignored)\n     * @property {number} [epsilon=0.0001] - precision of the curve fitting\n     */\n    public static readonly curves = curves;\n\n    /**\n     * Temporary point to use for containsPoint.\n     * @private\n     */\n    static _TEMP_POINT = new Point();\n\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Graphics objects.\n     */\n    public shader: Shader = null;\n\n    /** Renderer plugin for batching */\n    public pluginName = 'batch';\n\n    /**\n     * Current path\n     * @readonly\n     */\n    public currentPath: Polygon = null;\n\n    /** A collections of batches! These can be drawn by the renderer batch system. */\n    protected batches: Array<IGraphicsBatchElement> = [];\n\n    /** Update dirty for limiting calculating tints for batches. */\n    protected batchTint = -1;\n\n    /** Update dirty for limiting calculating batches.*/\n    protected batchDirty = -1;\n\n    /** Copy of the object vertex data. */\n    protected vertexData: Float32Array = null;\n\n    /** Current fill style. */\n    protected _fillStyle: FillStyle = new FillStyle();\n\n    /** Current line style. */\n    protected _lineStyle: LineStyle = new LineStyle();\n\n    /** Current shape transform matrix. */\n    protected _matrix: Matrix = null;\n\n    /** Current hole mode is enabled. */\n    protected _holeMode = false;\n    protected _transformID: number;\n    protected _tintColor: Color;\n\n    /**\n     * Represents the WebGL state the Graphics required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    private state: State = State.for2d();\n    private _geometry: GraphicsGeometry;\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh or Graphics objects.\n     * @readonly\n     */\n    public get geometry(): GraphicsGeometry\n    {\n        return this._geometry;\n    }\n\n    /**\n     * @param geometry - Geometry to use, if omitted will create a new GraphicsGeometry instance.\n     */\n    constructor(geometry: GraphicsGeometry = null)\n    {\n        super();\n\n        this._geometry = geometry || new GraphicsGeometry();\n        this._geometry.refCount++;\n\n        /**\n         * When cacheAsBitmap is set to true the graphics object will be rendered as if it was a sprite.\n         * This is useful if your graphics element does not change often, as it will speed up the rendering\n         * of the object in exchange for taking up texture memory. It is also useful if you need the graphics\n         * object to be anti-aliased, because it will be rendered using canvas. This is not recommended if\n         * you are constantly redrawing the graphics element.\n         * @name cacheAsBitmap\n         * @member {boolean}\n         * @memberof PIXI.Graphics#\n         * @default false\n         */\n\n        this._transformID = -1;\n\n        // Set default\n        this._tintColor = new Color(0xFFFFFF);\n        this.blendMode = BLEND_MODES.NORMAL;\n    }\n\n    /**\n     * Creates a new Graphics object with the same values as this one.\n     * Note that only the geometry of the object is cloned, not its transform (position,scale,etc)\n     * @returns - A clone of the graphics object\n     */\n    public clone(): Graphics\n    {\n        this.finishPoly();\n\n        return new Graphics(this._geometry);\n    }\n\n    /**\n     * The blend mode to be applied to the graphic shape. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.  Note that, since each\n     * primitive in the GraphicsGeometry list is rendered sequentially, modes\n     * such as `PIXI.BLEND_MODES.ADD` and `PIXI.BLEND_MODES.MULTIPLY` will\n     * be applied per-primitive.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    public get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * The tint applied to each graphic shape. This is a hex value. A value of\n     * 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    public get tint(): ColorSource\n    {\n        return this._tintColor.value;\n    }\n\n    public set tint(value: ColorSource)\n    {\n        this._tintColor.setValue(value);\n    }\n\n    /**\n     * The current fill style.\n     * @readonly\n     */\n    public get fill(): FillStyle\n    {\n        return this._fillStyle;\n    }\n\n    /**\n     * The current line style.\n     * @readonly\n     */\n    public get line(): LineStyle\n    {\n        return this._lineStyle;\n    }\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param [width=0] - width of the line to draw, will update the objects stored style\n     * @param [color=0x0] - color of the line to draw, will update the objects stored style\n     * @param [alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param [alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param [native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(width: number, color?: ColorSource, alpha?: number, alignment?: number, native?: boolean): this;\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param options - Line style options\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.ColorSource} [options.color=0x0] - color of the line to draw, will update the objects stored style\n     * @param {number} [options.alpha] - alpha of the line to draw, will update the objects stored style\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(options?: ILineStyleOptions): this;\n\n    public lineStyle(options: ILineStyleOptions | number = null,\n        color: ColorSource = 0x0, alpha?: number, alignment = 0.5, native = false): this\n    {\n        // Support non-object params: (width, color, alpha, alignment, native)\n        if (typeof options === 'number')\n        {\n            options = { width: options, color, alpha, alignment, native } as ILineStyleOptions;\n        }\n\n        return this.lineTextureStyle(options);\n    }\n\n    /**\n     * Like line style but support texture for line fill.\n     * @param [options] - Collection of options for setting line style.\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to use\n     * @param {PIXI.ColorSource} [options.color=0x0] - color of the line to draw, will update the objects stored style.\n     *  Default 0xFFFFFF if texture present.\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {PIXI.Matrix} [options.matrix=null] - Texture matrix to transform texture\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineTextureStyle(options?: ILineStyleOptions): this\n    {\n        // Apply defaults\n        const defaultLineStyleOptions: ILineStyleOptions = {\n            width: 0,\n            texture: Texture.WHITE,\n            color: options?.texture ? 0xFFFFFF : 0x0,\n            matrix: null,\n            alignment: 0.5,\n            native: false,\n            cap: LINE_CAP.BUTT,\n            join: LINE_JOIN.MITER,\n            miterLimit: 10,\n        };\n\n        options = Object.assign(defaultLineStyleOptions, options);\n\n        this.normalizeColor(options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.width > 0 && options.alpha > 0;\n\n        if (!visible)\n        {\n            this._lineStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._lineStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Start a polygon object internally.\n     * @protected\n     */\n    protected startPoly(): void\n    {\n        if (this.currentPath)\n        {\n            const points = this.currentPath.points;\n            const len = this.currentPath.points.length;\n\n            if (len > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = new Polygon();\n                this.currentPath.closeStroke = false;\n                this.currentPath.points.push(points[len - 2], points[len - 1]);\n            }\n        }\n        else\n        {\n            this.currentPath = new Polygon();\n            this.currentPath.closeStroke = false;\n        }\n    }\n\n    /**\n     * Finish the polygon object.\n     * @protected\n     */\n    finishPoly(): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = null;\n            }\n            else\n            {\n                this.currentPath.points.length = 0;\n            }\n        }\n    }\n\n    /**\n     * Moves the current drawing position to x, y.\n     * @param x - the X coordinate to move to\n     * @param y - the Y coordinate to move to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public moveTo(x: number, y: number): this\n    {\n        this.startPoly();\n        this.currentPath.points[0] = x;\n        this.currentPath.points[1] = y;\n\n        return this;\n    }\n\n    /**\n     * Draws a line using the current line style from the current drawing position to (x, y);\n     * The current drawing position is then set to (x, y).\n     * @param x - the X coordinate to draw to\n     * @param y - the Y coordinate to draw to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineTo(x: number, y: number): this\n    {\n        if (!this.currentPath)\n        {\n            this.moveTo(0, 0);\n        }\n\n        // remove duplicates..\n        const points = this.currentPath.points;\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        if (fromX !== x || fromY !== y)\n        {\n            points.push(x, y);\n        }\n\n        return this;\n    }\n\n    /**\n     * Initialize the curve\n     * @param x\n     * @param y\n     */\n    protected _initCurve(x = 0, y = 0): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length === 0)\n            {\n                this.currentPath.points = [x, y];\n            }\n        }\n        else\n        {\n            this.moveTo(x, y);\n        }\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public quadraticCurveTo(cpX: number, cpY: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        const points = this.currentPath.points;\n\n        if (points.length === 0)\n        {\n            this.moveTo(0, 0);\n        }\n\n        QuadraticUtils.curveTo(cpX, cpY, toX, toY, points);\n\n        return this;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns This Graphics object. Good for chaining method calls\n     */\n    public bezierCurveTo(cpX: number, cpY: number, cpX2: number, cpY2: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        BezierUtils.curveTo(cpX, cpY, cpX2, cpY2, toX, toY, this.currentPath.points);\n\n        return this;\n    }\n\n    /**\n     * The `arcTo` method creates an arc/curve between two tangents on the canvas.\n     * The first tangent is from the start point to the first control point,\n     * and the second tangent is from the first control point to the second control point.\n     * Note that the second control point is not necessarily the end point of the arc.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @param x1 - The x-coordinate of the first control point of the arc\n     * @param y1 - The y-coordinate of the first control point of the arc\n     * @param x2 - The x-coordinate of the second control point of the arc\n     * @param y2 - The y-coordinate of the second control point of the arc\n     * @param radius - The radius of the arc\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this\n    {\n        this._initCurve(x1, y1);\n\n        const points = this.currentPath.points;\n\n        const result = ArcUtils.curveTo(x1, y1, x2, y2, radius, points);\n\n        if (result)\n        {\n            const { cx, cy, radius, startAngle, endAngle, anticlockwise } = result;\n\n            this.arc(cx, cy, radius, startAngle, endAngle, anticlockwise);\n        }\n\n        return this;\n    }\n\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arc(cx: number, cy: number, radius: number, startAngle: number, endAngle: number, anticlockwise = false): this\n    {\n        if (startAngle === endAngle)\n        {\n            return this;\n        }\n\n        if (!anticlockwise && endAngle <= startAngle)\n        {\n            endAngle += PI_2;\n        }\n        else if (anticlockwise && startAngle <= endAngle)\n        {\n            startAngle += PI_2;\n        }\n\n        const sweep = endAngle - startAngle;\n\n        if (sweep === 0)\n        {\n            return this;\n        }\n\n        const startX = cx + (Math.cos(startAngle) * radius);\n        const startY = cy + (Math.sin(startAngle) * radius);\n        const eps = this._geometry.closePointEps;\n\n        // If the currentPath exists, take its points. Otherwise call `moveTo` to start a path.\n        let points = this.currentPath ? this.currentPath.points : null;\n\n        if (points)\n        {\n            // TODO: make a better fix.\n\n            // We check how far our start is from the last existing point\n            const xDiff = Math.abs(points[points.length - 2] - startX);\n            const yDiff = Math.abs(points[points.length - 1] - startY);\n\n            if (xDiff < eps && yDiff < eps)\n            {\n                // If the point is very close, we don't add it, since this would lead to artifacts\n                // during tessellation due to floating point imprecision.\n            }\n            else\n            {\n                points.push(startX, startY);\n            }\n        }\n        else\n        {\n            this.moveTo(startX, startY);\n            points = this.currentPath.points;\n        }\n\n        ArcUtils.arc(startX, startY, cx, cy, radius, startAngle, endAngle, anticlockwise, points);\n\n        return this;\n    }\n\n    /**\n     * Specifies a simple one-color fill that subsequent calls to other Graphics methods\n     * (such as lineTo() or drawCircle()) use when drawing.\n     * @param {PIXI.ColorSource} color - the color of the fill\n     * @param alpha - the alpha of the fill, will override the color's alpha\n     * @returns - This Graphics object. Suitable for chaining method calls\n     */\n    public beginFill(color: ColorSource = 0, alpha?: number): this\n    {\n        return this.beginTextureFill({ texture: Texture.WHITE, color, alpha });\n    }\n\n    /**\n     * Normalize the color input from options for line style or fill\n     * @param {PIXI.IFillStyleOptions} options - Fill style object.\n     */\n    private normalizeColor(options: Pick<IFillStyleOptions, 'color' | 'alpha'>): void\n    {\n        const temp = Color.shared.setValue(options.color ?? 0);\n\n        options.color = temp.toNumber();\n        options.alpha ??= temp.alpha;\n    }\n\n    /**\n     * Begin the texture fill.\n     * Note: The wrap mode of the texture is forced to REPEAT on render.\n     * @param options - Fill style object.\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to fill\n     * @param {PIXI.ColorSource} [options.color=0xffffff] - Background to fill behind texture\n     * @param {number} [options.alpha] - Alpha of fill, overrides the color's alpha\n     * @param {PIXI.Matrix} [options.matrix=null] - Transform matrix\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    beginTextureFill(options?: IFillStyleOptions): this\n    {\n        // Apply defaults\n        const defaultOptions: IFillStyleOptions = {\n            texture: Texture.WHITE,\n            color: 0xFFFFFF,\n            matrix: null,\n        };\n\n        options = Object.assign(defaultOptions, options);\n\n        this.normalizeColor(options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.alpha > 0;\n\n        if (!visible)\n        {\n            this._fillStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._fillStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Applies a fill to the lines and shapes that were added since the last call to the beginFill() method.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public endFill(): this\n    {\n        this.finishPoly();\n\n        this._fillStyle.reset();\n\n        return this;\n    }\n\n    /**\n     * Draws a rectangle shape.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRect(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Rectangle(x, y, width, height));\n    }\n\n    /**\n     * Draw a rectangle shape with rounded/beveled corners.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @param radius - Radius of the rectangle corners\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): this\n    {\n        return this.drawShape(new RoundedRectangle(x, y, width, height, radius));\n    }\n\n    /**\n     * Draws a circle.\n     * @param x - The X coordinate of the center of the circle\n     * @param y - The Y coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawCircle(x: number, y: number, radius: number): this\n    {\n        return this.drawShape(new Circle(x, y, radius));\n    }\n\n    /**\n     * Draws an ellipse.\n     * @param x - The X coordinate of the center of the ellipse\n     * @param y - The Y coordinate of the center of the ellipse\n     * @param width - The half width of the ellipse\n     * @param height - The half height of the ellipse\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawEllipse(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Ellipse(x, y, width, height));\n    }\n\n    public drawPolygon(...path: Array<number> | Array<IPointData>): this;\n    public drawPolygon(path: Array<number> | Array<IPointData> | Polygon): this;\n\n    /**\n     * Draws a polygon using the given path.\n     * @param {number[]|PIXI.IPointData[]|PIXI.Polygon} path - The path data used to construct the polygon.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawPolygon(...path: any[]): this\n    {\n        let points: Array<number> | Array<IPointData>;\n        let closeStroke = true;// !!this._fillStyle;\n\n        const poly = path[0] as Polygon;\n\n        // check if data has points..\n        if (poly.points)\n        {\n            closeStroke = poly.closeStroke;\n            points = poly.points;\n        }\n        else\n            if (Array.isArray(path[0]))\n            {\n                points = path[0];\n            }\n            else\n            {\n                points = path;\n            }\n\n        const shape = new Polygon(points);\n\n        shape.closeStroke = closeStroke;\n\n        this.drawShape(shape);\n\n        return this;\n    }\n\n    /**\n     * Draw any shape.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - Shape to draw\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawShape(shape: IShape): this\n    {\n        if (!this._holeMode)\n        {\n            this._geometry.drawShape(\n                shape,\n                this._fillStyle.clone(),\n                this._lineStyle.clone(),\n                this._matrix\n            );\n        }\n        else\n        {\n            this._geometry.drawHole(shape, this._matrix);\n        }\n\n        return this;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public clear(): this\n    {\n        this._geometry.clear();\n        this._lineStyle.reset();\n        this._fillStyle.reset();\n\n        this._boundsID++;\n        this._matrix = null;\n        this._holeMode = false;\n        this.currentPath = null;\n\n        return this;\n    }\n\n    /**\n     * True if graphics consists of one rectangle, and thus, can be drawn like a Sprite and\n     * masked with gl.scissor.\n     * @returns - True if only 1 rect.\n     */\n    public isFastRect(): boolean\n    {\n        const data = this._geometry.graphicsData;\n\n        return data.length === 1\n            && data[0].shape.type === SHAPES.RECT\n            && !data[0].matrix\n            && !data[0].holes.length\n            && !(data[0].lineStyle.visible && data[0].lineStyle.width);\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n        // batch part..\n        // batch it!\n\n        geometry.updateBatches();\n\n        if (geometry.batchable)\n        {\n            if (this.batchDirty !== geometry.batchDirty)\n            {\n                this._populateBatches();\n            }\n\n            this._renderBatched(renderer);\n        }\n        else\n        {\n            // no batching...\n            renderer.batch.flush();\n\n            this._renderDirect(renderer);\n        }\n    }\n\n    /** Populating batches for rendering. */\n    protected _populateBatches(): void\n    {\n        const geometry = this._geometry;\n        const blendMode = this.blendMode;\n        const len = geometry.batches.length;\n\n        this.batchTint = -1;\n        this._transformID = -1;\n        this.batchDirty = geometry.batchDirty;\n        this.batches.length = len;\n\n        this.vertexData = new Float32Array(geometry.points);\n\n        for (let i = 0; i < len; i++)\n        {\n            const gI = geometry.batches[i];\n            const color = gI.style.color;\n            const vertexData = new Float32Array(this.vertexData.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const uvs = new Float32Array(geometry.uvsFloat32.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const indices = new Uint16Array(geometry.indicesUint16.buffer,\n                gI.start * 2,\n                gI.size);\n\n            const batch = {\n                vertexData,\n                blendMode,\n                indices,\n                uvs,\n                _batchRGB: Color.shared.setValue(color).toRgbArray(),\n                _tintRGB: color,\n                _texture: gI.style.texture,\n                alpha: gI.style.alpha,\n                worldAlpha: 1\n            };\n\n            this.batches[i] = batch;\n        }\n    }\n\n    /**\n     * Renders the batches using the BathedRenderer plugin\n     * @param renderer - The renderer\n     */\n    protected _renderBatched(renderer: Renderer): void\n    {\n        if (!this.batches.length)\n        {\n            return;\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n\n        this.calculateVertices();\n        this.calculateTints();\n\n        for (let i = 0, l = this.batches.length; i < l; i++)\n        {\n            const batch = this.batches[i];\n\n            batch.worldAlpha = this.worldAlpha * batch.alpha;\n\n            renderer.plugins[this.pluginName].render(batch);\n        }\n    }\n\n    /**\n     * Renders the graphics direct\n     * @param renderer - The renderer\n     */\n    protected _renderDirect(renderer: Renderer): void\n    {\n        const shader = this._resolveDirectShader(renderer);\n\n        const geometry = this._geometry;\n        const worldAlpha = this.worldAlpha;\n        const uniforms = shader.uniforms;\n        const drawCalls = geometry.drawCalls;\n\n        // lets set the transfomr\n        uniforms.translationMatrix = this.transform.worldTransform;\n\n        // and then lets set the tint..\n        Color.shared.setValue(this._tintColor)\n            .premultiply(worldAlpha)\n            .toArray(uniforms.tint);\n\n        // the first draw call, we can set the uniforms of the shader directly here.\n\n        // this means that we can tack advantage of the sync function of pixi!\n        // bind and sync uniforms..\n        // there is a way to optimise this..\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(geometry, shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // then render the rest of them...\n        for (let i = 0, l = drawCalls.length; i < l; i++)\n        {\n            this._renderDrawCallDirect(renderer, geometry.drawCalls[i]);\n        }\n    }\n\n    /**\n     * Renders specific DrawCall\n     * @param renderer\n     * @param drawCall\n     */\n    protected _renderDrawCallDirect(renderer: Renderer, drawCall: BatchDrawCall): void\n    {\n        const { texArray, type, size, start } = drawCall;\n        const groupTextureCount = texArray.count;\n\n        for (let j = 0; j < groupTextureCount; j++)\n        {\n            renderer.texture.bind(texArray.elements[j], j);\n        }\n\n        renderer.geometry.draw(type, size, start);\n    }\n\n    /**\n     * Resolves shader for direct rendering\n     * @param renderer - The renderer\n     */\n    protected _resolveDirectShader(renderer: Renderer): Shader\n    {\n        let shader = this.shader;\n\n        const pluginName = this.pluginName;\n\n        if (!shader)\n        {\n            // if there is no shader here, we can use the default shader.\n            // and that only gets created if we actually need it..\n            // but may be more than one plugins for graphics\n            if (!DEFAULT_SHADERS[pluginName])\n            {\n                const { maxTextures } = renderer.plugins[pluginName];\n                const sampleValues = new Int32Array(maxTextures);\n\n                for (let i = 0; i < maxTextures; i++)\n                {\n                    sampleValues[i] = i;\n                }\n\n                const uniforms = {\n                    tint: new Float32Array([1, 1, 1, 1]),\n                    translationMatrix: new Matrix(),\n                    default: UniformGroup.from({ uSamplers: sampleValues }, true),\n                };\n\n                const program = renderer.plugins[pluginName]._shader.program;\n\n                DEFAULT_SHADERS[pluginName] = new Shader(program, uniforms);\n            }\n\n            shader = DEFAULT_SHADERS[pluginName];\n        }\n\n        return shader;\n    }\n\n    /**\n     * Retrieves the bounds of the graphic shape as a rectangle object.\n     * @see PIXI.GraphicsGeometry#bounds\n     */\n    protected _calculateBounds(): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n\n        // skipping when graphics is empty, like a container\n        if (!geometry.graphicsData.length)\n        {\n            return;\n        }\n\n        const { minX, minY, maxX, maxY } = geometry.bounds;\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Tests if a point is inside this graphics object\n     * @param point - the point to test\n     * @returns - the result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, Graphics._TEMP_POINT);\n\n        return this._geometry.containsPoint(Graphics._TEMP_POINT);\n    }\n\n    /** Recalculate the tint by applying tint to batches using Graphics tint. */\n    protected calculateTints(): void\n    {\n        if (this.batchTint !== this.tint)\n        {\n            this.batchTint = this._tintColor.toNumber();\n\n            for (let i = 0; i < this.batches.length; i++)\n            {\n                const batch = this.batches[i];\n\n                batch._tintRGB = Color.shared\n                    .setValue(this._tintColor)\n                    .multiply(batch._batchRGB)\n                    .toLittleEndianNumber();\n            }\n        }\n    }\n\n    /** If there's a transform update or a change to the shape of the geometry, recalculate the vertices. */\n    protected calculateVertices(): void\n    {\n        const wtID = this.transform._worldID;\n\n        if (this._transformID === wtID)\n        {\n            return;\n        }\n\n        this._transformID = wtID;\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const data = this._geometry.points;// batch.vertexDataOriginal;\n        const vertexData = this.vertexData;\n\n        let count = 0;\n\n        for (let i = 0; i < data.length; i += 2)\n        {\n            const x = data[i];\n            const y = data[i + 1];\n\n            vertexData[count++] = (a * x) + (c * y) + tx;\n            vertexData[count++] = (d * y) + (b * x) + ty;\n        }\n    }\n\n    /**\n     * Closes the current path.\n     * @returns - Returns itself.\n     */\n    public closePath(): this\n    {\n        const currentPath = this.currentPath;\n\n        if (currentPath)\n        {\n            // we don't need to add extra point in the end because buildLine will take care of that\n            currentPath.closeStroke = true;\n            // ensure that the polygon is completed, and is available for hit detection\n            // (even if the graphics is not rendered yet)\n            this.finishPoly();\n        }\n\n        return this;\n    }\n\n    /**\n     * Apply a matrix to the positional data.\n     * @param matrix - Matrix to use for transform current shape.\n     * @returns - Returns itself.\n     */\n    public setMatrix(matrix: Matrix): this\n    {\n        this._matrix = matrix;\n\n        return this;\n    }\n\n    /**\n     * Begin adding holes to the last draw shape\n     * IMPORTANT: holes must be fully inside a shape to work\n     * Also weirdness ensues if holes overlap!\n     * Ellipses, Circles, Rectangles and Rounded Rectangles cannot be holes or host for holes in CanvasRenderer,\n     * please use `moveTo` `lineTo`, `quadraticCurveTo` if you rely on pixi-legacy bundle.\n     * @returns - Returns itself.\n     */\n    public beginHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = true;\n\n        return this;\n    }\n\n    /**\n     * End adding holes to the last draw shape.\n     * @returns - Returns itself.\n     */\n    public endHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = false;\n\n        return this;\n    }\n\n    /**\n     * Destroys the Graphics object.\n     * @param options - Options parameter. A boolean will act as if all\n     *  options have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have\n     *  their destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this._geometry.refCount--;\n        if (this._geometry.refCount === 0)\n        {\n            this._geometry.dispose();\n        }\n\n        this._matrix = null;\n        this.currentPath = null;\n        this._lineStyle.destroy();\n        this._lineStyle = null;\n        this._fillStyle.destroy();\n        this._fillStyle = null;\n        this._geometry = null;\n        this.shader = null;\n        this.vertexData = null;\n        this.batches.length = 0;\n        this.batches = null;\n\n        super.destroy(options);\n    }\n}\n"], "names": ["_Graphics", "radius"], "mappings": ";;;;;;;;;;AA+DA,MAAM,kBAA2C,CAAA,GAsBpC,YAAN,MAAMA,mBAAiB,UAC9B;AAAA;AAAA;AAAA;AAAA,EAkFI,YAAY,WAA6B,MACzC;AACU,aA5DV,KAAO,SAAiB,MAGxB,KAAO,aAAa,SAMpB,KAAO,cAAuB,MAG9B,KAAU,UAAwC,IAGlD,KAAU,YAAY,IAGtB,KAAU,aAAa,IAGvB,KAAU,aAA2B,MAG3B,KAAA,aAAwB,IAAI,aAG5B,KAAA,aAAwB,IAAI,aAGtC,KAAU,UAAkB,MAG5B,KAAU,YAAY,IAQd,KAAA,QAAe,MAAM,SAqBzB,KAAK,YAAY,YAAY,IAAI,oBACjC,KAAK,UAAU,YAcf,KAAK,eAAe,IAGpB,KAAK,aAAa,IAAI,MAAM,QAAQ,GACpC,KAAK,YAAY,YAAY;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAhCA,IAAW,WACX;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCO,QACP;AACI,WAAA,KAAK,WAAW,GAET,IAAIA,WAAS,KAAK,SAAS;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAW,UAAU,OACrB;AACI,SAAK,MAAM,YAAY;AAAA,EAC3B;AAAA,EAEA,IAAW,YACX;AACI,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAW,OACX;AACI,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA,EAEA,IAAW,KAAK,OAChB;AACS,SAAA,WAAW,SAAS,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,OACX;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,OACX;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAgCO,UAAU,UAAsC,MACnD,QAAqB,GAAK,OAAgB,YAAY,KAAK,SAAS,IACxE;AAEI,WAAI,OAAO,WAAY,aAEnB,UAAU,EAAE,OAAO,SAAS,OAAO,OAAO,WAAW,OAGlD,IAAA,KAAK,iBAAiB,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBO,iBAAiB,SACxB;AAEI,UAAM,0BAA6C;AAAA,MAC/C,OAAO;AAAA,MACP,SAAS,QAAQ;AAAA,MACjB,OAAO,SAAS,UAAU,WAAW;AAAA,MACrC,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,KAAK,SAAS;AAAA,MACd,MAAM,UAAU;AAAA,MAChB,YAAY;AAAA,IAAA;AAGhB,cAAU,OAAO,OAAO,yBAAyB,OAAO,GAExD,KAAK,eAAe,OAAO,GAEvB,KAAK,eAEL,KAAK,UAAU;AAGnB,UAAM,UAAU,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;AAEhD,WAAA,WAMG,QAAQ,WAER,QAAQ,SAAS,QAAQ,OAAO,MAAA,GAChC,QAAQ,OAAO,OAAA,IAGnB,OAAO,OAAO,KAAK,YAAY,EAAE,QAAA,GAAW,OAAO,KAVnD,KAAK,WAAW,MAAA,GAab;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,YACV;AACI,QAAI,KAAK,aACT;AACI,YAAM,SAAS,KAAK,YAAY,QAC1B,MAAM,KAAK,YAAY,OAAO;AAEhC,YAAM,MAEN,KAAK,UAAU,KAAK,WAAW,GAC/B,KAAK,cAAc,IAAI,WACvB,KAAK,YAAY,cAAc,IAC/B,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC;AAAA,IAErE;AAGI,WAAK,cAAc,IAAI,QACvB,GAAA,KAAK,YAAY,cAAc;AAAA,EAEvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aACA;AACQ,SAAK,gBAED,KAAK,YAAY,OAAO,SAAS,KAEjC,KAAK,UAAU,KAAK,WAAW,GAC/B,KAAK,cAAc,QAInB,KAAK,YAAY,OAAO,SAAS;AAAA,EAG7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,OAAO,GAAW,GACzB;AACI,WAAA,KAAK,UAAU,GACf,KAAK,YAAY,OAAO,CAAC,IAAI,GAC7B,KAAK,YAAY,OAAO,CAAC,IAAI,GAEtB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,OAAO,GAAW,GACzB;AACS,SAAK,eAEN,KAAK,OAAO,GAAG,CAAC;AAIpB,UAAM,SAAS,KAAK,YAAY,QAC1B,QAAQ,OAAO,OAAO,SAAS,CAAC,GAChC,QAAQ,OAAO,OAAO,SAAS,CAAC;AAElC,YAAA,UAAU,KAAK,UAAU,MAEzB,OAAO,KAAK,GAAG,CAAC,GAGb;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,WAAW,IAAI,GAAG,IAAI,GAChC;AACQ,SAAK,cAED,KAAK,YAAY,OAAO,WAAW,MAEnC,KAAK,YAAY,SAAS,CAAC,GAAG,CAAC,KAKnC,KAAK,OAAO,GAAG,CAAC;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,iBAAiB,KAAa,KAAa,KAAa,KAC/D;AACI,SAAK,WAAW;AAEV,UAAA,SAAS,KAAK,YAAY;AAEhC,WAAI,OAAO,WAAW,KAElB,KAAK,OAAO,GAAG,CAAC,GAGpB,eAAe,QAAQ,KAAK,KAAK,KAAK,KAAK,MAAM,GAE1C;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,cAAc,KAAa,KAAa,MAAc,MAAc,KAAa,KACxF;AACI,WAAA,KAAK,WAAW,GAEhB,YAAY,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,YAAY,MAAM,GAEpE;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBO,MAAM,IAAY,IAAY,IAAY,IAAY,QAC7D;AACS,SAAA,WAAW,IAAI,EAAE;AAEtB,UAAM,SAAS,KAAK,YAAY,QAE1B,SAAS,SAAS,QAAQ,IAAI,IAAI,IAAI,IAAI,QAAQ,MAAM;AAE9D,QAAI,QACJ;AACU,YAAA,EAAE,IAAI,IAAI,QAAAC,SAAQ,YAAY,UAAU,cAAkB,IAAA;AAEhE,WAAK,IAAI,IAAI,IAAIA,SAAQ,YAAY,UAAU,aAAa;AAAA,IAChE;AAEO,WAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeO,IAAI,IAAY,IAAY,QAAgB,YAAoB,UAAkB,gBAAgB,IACzG;AACI,QAAI,eAAe;AAER,aAAA;AAcX,QAXI,CAAC,iBAAiB,YAAY,aAE9B,YAAY,OAEP,iBAAiB,cAAc,aAEpC,cAAc,OAGJ,WAAW,eAEX;AAEH,aAAA;AAGX,UAAM,SAAS,KAAM,KAAK,IAAI,UAAU,IAAI,QACtC,SAAS,KAAM,KAAK,IAAI,UAAU,IAAI,QACtC,MAAM,KAAK,UAAU;AAG3B,QAAI,SAAS,KAAK,cAAc,KAAK,YAAY,SAAS;AAE1D,QAAI,QACJ;AAII,YAAM,QAAQ,KAAK,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI,MAAM,GACnD,QAAQ,KAAK,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI,MAAM;AAErD,cAAQ,OAAO,QAAQ,OAOvB,OAAO,KAAK,QAAQ,MAAM;AAAA,IAElC;AAGI,WAAK,OAAO,QAAQ,MAAM,GAC1B,SAAS,KAAK,YAAY;AAGrB,WAAA,SAAA,IAAI,QAAQ,QAAQ,IAAI,IAAI,QAAQ,YAAY,UAAU,eAAe,MAAM,GAEjF;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,UAAU,QAAqB,GAAG,OACzC;AACW,WAAA,KAAK,iBAAiB,EAAE,SAAS,QAAQ,OAAO,OAAO,OAAO;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,eAAe,SACvB;AACI,UAAM,OAAO,MAAM,OAAO,SAAS,QAAQ,SAAS,CAAC;AAE7C,YAAA,QAAQ,KAAK,YACrB,QAAQ,UAAR,QAAQ,QAAU,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,iBAAiB,SACjB;AAEI,UAAM,iBAAoC;AAAA,MACtC,SAAS,QAAQ;AAAA,MACjB,OAAO;AAAA,MACP,QAAQ;AAAA,IAAA;AAGZ,cAAU,OAAO,OAAO,gBAAgB,OAAO,GAE/C,KAAK,eAAe,OAAO,GAEvB,KAAK,eAEL,KAAK,UAAU;AAGb,UAAA,UAAU,QAAQ,QAAQ;AAE3B,WAAA,WAMG,QAAQ,WAER,QAAQ,SAAS,QAAQ,OAAO,MAAA,GAChC,QAAQ,OAAO,OAAA,IAGnB,OAAO,OAAO,KAAK,YAAY,EAAE,QAAA,GAAW,OAAO,KAVnD,KAAK,WAAW,MAAA,GAab;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,UACP;AACI,WAAA,KAAK,cAEL,KAAK,WAAW,MAET,GAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,SAAS,GAAW,GAAW,OAAe,QACrD;AACW,WAAA,KAAK,UAAU,IAAI,UAAU,GAAG,GAAG,OAAO,MAAM,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,gBAAgB,GAAW,GAAW,OAAe,QAAgB,QAC5E;AACW,WAAA,KAAK,UAAU,IAAI,iBAAiB,GAAG,GAAG,OAAO,QAAQ,MAAM,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,WAAW,GAAW,GAAW,QACxC;AACI,WAAO,KAAK,UAAU,IAAI,OAAO,GAAG,GAAG,MAAM,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,YAAY,GAAW,GAAW,OAAe,QACxD;AACW,WAAA,KAAK,UAAU,IAAI,QAAQ,GAAG,GAAG,OAAO,MAAM,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,eAAe,MACtB;AACI,QAAI,QACA,cAAc;AAEZ,UAAA,OAAO,KAAK,CAAC;AAGf,SAAK,UAEL,cAAc,KAAK,aACnB,SAAS,KAAK,UAGV,MAAM,QAAQ,KAAK,CAAC,CAAC,IAErB,SAAS,KAAK,CAAC,IAIf,SAAS;AAGX,UAAA,QAAQ,IAAI,QAAQ,MAAM;AAEhC,WAAA,MAAM,cAAc,aAEpB,KAAK,UAAU,KAAK,GAEb;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,UAAU,OACjB;AACS,WAAA,KAAK,YAWN,KAAK,UAAU,SAAS,OAAO,KAAK,OAAO,IAT3C,KAAK,UAAU;AAAA,MACX;AAAA,MACA,KAAK,WAAW,MAAM;AAAA,MACtB,KAAK,WAAW,MAAM;AAAA,MACtB,KAAK;AAAA,IAQN,GAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,QACP;AACS,WAAA,KAAA,UAAU,SACf,KAAK,WAAW,SAChB,KAAK,WAAW,MAAA,GAEhB,KAAK,aACL,KAAK,UAAU,MACf,KAAK,YAAY,IACjB,KAAK,cAAc,MAEZ;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aACP;AACU,UAAA,OAAO,KAAK,UAAU;AAE5B,WAAO,KAAK,WAAW,KAChB,KAAK,CAAC,EAAE,MAAM,SAAS,OAAO,QAC9B,CAAC,KAAK,CAAC,EAAE,UACT,CAAC,KAAK,CAAC,EAAE,MAAM,UACf,EAAE,KAAK,CAAC,EAAE,UAAU,WAAW,KAAK,CAAC,EAAE,UAAU;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,QAAQ,UAClB;AACI,SAAK,WAAW;AAEhB,UAAM,WAAW,KAAK;AAIb,aAAA,iBAEL,SAAS,aAEL,KAAK,eAAe,SAAS,cAE7B,KAAK,iBAAA,GAGT,KAAK,eAAe,QAAQ,MAK5B,SAAS,MAAM,MAAM,GAErB,KAAK,cAAc,QAAQ;AAAA,EAEnC;AAAA;AAAA,EAGU,mBACV;AACU,UAAA,WAAW,KAAK,WAChB,YAAY,KAAK,WACjB,MAAM,SAAS,QAAQ;AAE7B,SAAK,YAAY,IACjB,KAAK,eAAe,IACpB,KAAK,aAAa,SAAS,YAC3B,KAAK,QAAQ,SAAS,KAEtB,KAAK,aAAa,IAAI,aAAa,SAAS,MAAM;AAElD,aAAS,IAAI,GAAG,IAAI,KAAK,KACzB;AACU,YAAA,KAAK,SAAS,QAAQ,CAAC,GACvB,QAAQ,GAAG,MAAM,OACjB,aAAa,IAAI;AAAA,QAAa,KAAK,WAAW;AAAA,QAChD,GAAG,cAAc,IAAI;AAAA,QACrB,GAAG,aAAa;AAAA,MAAA,GAEd,MAAM,IAAI;AAAA,QAAa,SAAS,WAAW;AAAA,QAC7C,GAAG,cAAc,IAAI;AAAA,QACrB,GAAG,aAAa;AAAA,MAAA,GAEd,UAAU,IAAI;AAAA,QAAY,SAAS,cAAc;AAAA,QACnD,GAAG,QAAQ;AAAA,QACX,GAAG;AAAA,SAED,QAAQ;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,MAAM,OAAO,SAAS,KAAK,EAAE,WAAW;AAAA,QACnD,UAAU;AAAA,QACV,UAAU,GAAG,MAAM;AAAA,QACnB,OAAO,GAAG,MAAM;AAAA,QAChB,YAAY;AAAA,MAAA;AAGX,WAAA,QAAQ,CAAC,IAAI;AAAA,IACtB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,eAAe,UACzB;AACS,QAAA,KAAK,QAAQ,QAKlB;AAAA,eAAS,MAAM,kBAAkB,SAAS,QAAQ,KAAK,UAAU,CAAC,GAElE,KAAK,kBACL,GAAA,KAAK,eAAe;AAEX,eAAA,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,IAAI,GAAG,KAChD;AACU,cAAA,QAAQ,KAAK,QAAQ,CAAC;AAEtB,cAAA,aAAa,KAAK,aAAa,MAAM,OAE3C,SAAS,QAAQ,KAAK,UAAU,EAAE,OAAO,KAAK;AAAA,MAClD;AAAA,IAAA;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,cAAc,UACxB;AACI,UAAM,SAAS,KAAK,qBAAqB,QAAQ,GAE3C,WAAW,KAAK,WAChB,aAAa,KAAK,YAClB,WAAW,OAAO,UAClB,YAAY,SAAS;AAG3B,aAAS,oBAAoB,KAAK,UAAU,gBAG5C,MAAM,OAAO,SAAS,KAAK,UAAU,EAChC,YAAY,UAAU,EACtB,QAAQ,SAAS,IAAI,GAO1B,SAAS,OAAO,KAAK,MAAM,GAC3B,SAAS,SAAS,KAAK,UAAU,MAAM,GAGvC,SAAS,MAAM,IAAI,KAAK,KAAK;AAG7B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG;AAEzC,WAAK,sBAAsB,UAAU,SAAS,UAAU,CAAC,CAAC;AAAA,EAElE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,sBAAsB,UAAoB,UACpD;AACU,UAAA,EAAE,UAAU,MAAM,MAAM,MAAU,IAAA,UAClC,oBAAoB,SAAS;AAE1B,aAAA,IAAI,GAAG,IAAI,mBAAmB;AAEnC,eAAS,QAAQ,KAAK,SAAS,SAAS,CAAC,GAAG,CAAC;AAGjD,aAAS,SAAS,KAAK,MAAM,MAAM,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,qBAAqB,UAC/B;AACI,QAAI,SAAS,KAAK;AAElB,UAAM,aAAa,KAAK;AAExB,QAAI,CAAC,QACL;AAIQ,UAAA,CAAC,gBAAgB,UAAU,GAC/B;AACU,cAAA,EAAE,gBAAgB,SAAS,QAAQ,UAAU,GAC7C,eAAe,IAAI,WAAW,WAAW;AAEtC,iBAAA,IAAI,GAAG,IAAI,aAAa;AAE7B,uBAAa,CAAC,IAAI;AAGtB,cAAM,WAAW;AAAA,UACb,MAAM,IAAI,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,UACnC,mBAAmB,IAAI,OAAO;AAAA,UAC9B,SAAS,aAAa,KAAK,EAAE,WAAW,aAAA,GAAgB,EAAI;AAAA,QAAA,GAG1D,UAAU,SAAS,QAAQ,UAAU,EAAE,QAAQ;AAErD,wBAAgB,UAAU,IAAI,IAAI,OAAO,SAAS,QAAQ;AAAA,MAC9D;AAEA,eAAS,gBAAgB,UAAU;AAAA,IACvC;AAEO,WAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,mBACV;AACI,SAAK,WAAW;AAEhB,UAAM,WAAW,KAAK;AAGlB,QAAA,CAAC,SAAS,aAAa;AAEvB;AAGJ,UAAM,EAAE,MAAM,MAAM,MAAM,SAAS,SAAS;AAE5C,SAAK,QAAQ,SAAS,KAAK,WAAW,MAAM,MAAM,MAAM,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OACrB;AACS,WAAA,KAAA,eAAe,aAAa,OAAOD,WAAS,WAAW,GAErD,KAAK,UAAU,cAAcA,WAAS,WAAW;AAAA,EAC5D;AAAA;AAAA,EAGU,iBACV;AACQ,QAAA,KAAK,cAAc,KAAK,MAC5B;AACS,WAAA,YAAY,KAAK,WAAW,SAAS;AAE1C,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KACzC;AACU,cAAA,QAAQ,KAAK,QAAQ,CAAC;AAEtB,cAAA,WAAW,MAAM,OAClB,SAAS,KAAK,UAAU,EACxB,SAAS,MAAM,SAAS,EACxB,qBAAqB;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAGU,oBACV;AACU,UAAA,OAAO,KAAK,UAAU;AAE5B,QAAI,KAAK,iBAAiB;AAEtB;AAGJ,SAAK,eAAe;AAEd,UAAA,KAAK,KAAK,UAAU,gBACpB,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,KAAK,GAAG,IACR,KAAK,GAAG,IAER,OAAO,KAAK,UAAU,QACtB,aAAa,KAAK;AAExB,QAAI,QAAQ;AAEZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GACtC;AACI,YAAM,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,IAAI,CAAC;AAEpB,iBAAW,OAAO,IAAK,IAAI,IAAM,IAAI,IAAK,IAC1C,WAAW,OAAO,IAAK,IAAI,IAAM,IAAI,IAAK;AAAA,IAC9C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,YACP;AACI,UAAM,cAAc,KAAK;AAEzB,WAAI,gBAGA,YAAY,cAAc,IAG1B,KAAK,eAGF;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,UAAU,QACjB;AACI,WAAA,KAAK,UAAU,QAER;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,YACP;AACI,WAAA,KAAK,cACL,KAAK,YAAY,IAEV;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,UACP;AACI,WAAA,KAAK,cACL,KAAK,YAAY,IAEV;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaO,QAAQ,SACf;AACS,SAAA,UAAU,YACX,KAAK,UAAU,aAAa,KAE5B,KAAK,UAAU,QAAA,GAGnB,KAAK,UAAU,MACf,KAAK,cAAc,MACnB,KAAK,WAAW,WAChB,KAAK,aAAa,MAClB,KAAK,WAAW,WAChB,KAAK,aAAa,MAClB,KAAK,YAAY,MACjB,KAAK,SAAS,MACd,KAAK,aAAa,MAClB,KAAK,QAAQ,SAAS,GACtB,KAAK,UAAU,MAEf,MAAM,QAAQ,OAAO;AAAA,EACzB;AACJ;AA7nCa,UAac,SAAS;AAAA;AAAA;AAAA;AAbvB,UAmBF,cAAc,IAAI;AAnBtB,IAAM,WAAN;"}