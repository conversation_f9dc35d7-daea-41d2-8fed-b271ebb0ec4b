{"version": 3, "file": "const.js", "sources": ["../src/const.ts"], "sourcesContent": ["/**\n * Supported line joints in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @see https://graphicdesign.stackexchange.com/questions/59018/what-is-a-bevel-join-of-two-lines-exactly-illustrator\n * @memberof PIXI\n * @static\n * @enum {string}\n */\nexport enum LINE_JOIN\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * 'miter': make a sharp corner where outer part of lines meet\n     * @default miter\n     */\n    MITER = 'miter',\n    /**\n     * 'bevel': add a square butt at each end of line segment and fill the triangle at turn\n     * @default bevel\n     */\n    BEVEL = 'bevel',\n    /**\n     * 'round': add an arc at the joint\n     * @default round\n     */\n    ROUND = 'round'\n}\n\n/**\n * Support line caps in `PIXI.LineStyle` for graphics.\n * @see PIXI.Graphics#lineStyle\n * @memberof PIXI\n * @static\n * @enum {string}\n */\nexport enum LINE_CAP\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * 'butt': don't add any cap at line ends (leaves orthogonal edges)\n     * @default butt\n     */\n    BUTT = 'butt',\n    /**\n     * 'round': add semicircle at ends\n     * @default round\n     */\n    ROUND = 'round',\n    /**\n     * 'square': add square at end (like `BUTT` except more length at end)\n     * @default square\n     */\n    SQUARE = 'square'\n}\n\n/**\n * @memberof PIXI\n * @deprecated\n */\nexport interface IGraphicsCurvesSettings\n{\n    adaptive: boolean;\n    maxLength: number;\n    minSegments: number;\n    maxSegments: number;\n\n    epsilon: number;\n\n    _segmentsCount(length: number, defaultSegments?: number): number;\n}\n\n/**\n * @private\n */\nexport const curves = {\n    adaptive: true,\n    maxLength: 10,\n    minSegments: 8,\n    maxSegments:  2048,\n\n    epsilon: 0.0001,\n\n    _segmentsCount(length: number, defaultSegments = 20)\n    {\n        if (!this.adaptive || !length || isNaN(length))\n        {\n            return defaultSegments;\n        }\n\n        let result = Math.ceil(length / this.maxLength);\n\n        if (result < this.minSegments)\n        {\n            result = this.minSegments;\n        }\n        else if (result > this.maxSegments)\n        {\n            result = this.maxSegments;\n        }\n\n        return result;\n    },\n};\n\n/**\n * @static\n * @readonly\n * @memberof PIXI\n * @name GRAPHICS_CURVES\n * @type {object}\n * @deprecated since 7.1.0\n * @see PIXI.Graphics.curves\n */\nexport const GRAPHICS_CURVES = curves;\n"], "names": ["LINE_JOIN", "LINE_CAP"], "mappings": ";AAQY,IAAA,YAAAA,kBAAAA,gBAORA,WAAA,QAAQ,SAKRA,WAAA,QAAQ,SAKRA,WAAA,QAAQ,SAjBAA,aAAA,kBA2BA,WAAL,kBAAKC,eAORA,UAAA,OAAO,QAKPA,UAAA,QAAQ,SAKRA,UAAA,SAAS,UAjBDA,YAAA,YAAA,EAAA;AAuCL,MAAM,SAAS;AAAA,EAClB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAc;AAAA,EAEd,SAAS;AAAA,EAET,eAAe,QAAgB,kBAAkB,IACjD;AACI,QAAI,CAAC,KAAK,YAAY,CAAC,UAAU,MAAM,MAAM;AAElC,aAAA;AAGX,QAAI,SAAS,KAAK,KAAK,SAAS,KAAK,SAAS;AAE1C,WAAA,SAAS,KAAK,cAEd,SAAS,KAAK,cAET,SAAS,KAAK,gBAEnB,SAAS,KAAK,cAGX;AAAA,EACX;AACJ,GAWa,kBAAkB;;;;;"}