{"version": 3, "file": "GraphicsGeometry.mjs", "sources": ["../src/GraphicsGeometry.ts"], "sourcesContent": ["import {\n    <PERSON>Texture,\n    Batch<PERSON>rawCall,\n    BatchGeometry,\n    BatchTextureArray,\n    Color,\n    DRAW_MODES,\n    Point,\n    WRAP_MODES\n} from '@pixi/core';\nimport { Bounds } from '@pixi/display';\nimport { GraphicsData } from './GraphicsData';\nimport {\n    BATCH_POOL, BatchPart, buildLine,\n    buildPoly,\n    DRAW_CALL_POOL,\n    FILL_COMMANDS\n} from './utils';\n\nimport type { IPointData, IShape, Matrix, Texture } from '@pixi/core';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\nconst tmpPoint = new Point();\n\n/**\n * The Graphics class contains methods used to draw primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.\n *\n * GraphicsGeometry is designed to not be continually updating the geometry since it's expensive\n * to re-tesselate using **earcut**. Consider using {@link PIXI.Mesh} for this use-case, it's much faster.\n * @memberof PIXI\n */\nexport class GraphicsGeometry extends BatchGeometry\n{\n    /** The maximum number of points to consider an object \"batchable\", able to be batched by the renderer's batch system. */\n    public static BATCHABLE_SIZE = 100;\n\n    /** Minimal distance between points that are considered different. Affects line tesselation. */\n    public closePointEps = 1e-4;\n\n    /** Padding to add to the bounds. */\n    public boundsPadding = 0;\n\n    uvsFloat32: Float32Array = null;\n    indicesUint16: Uint16Array | Uint32Array = null;\n    batchable = false;\n\n    /** An array of points to draw, 2 numbers per point */\n    points: number[] = [];\n\n    /** The collection of colors */\n    colors: number[] = [];\n\n    /** The UVs collection */\n    uvs: number[] = [];\n\n    /** The indices of the vertices */\n    indices: number[] = [];\n\n    /** Reference to the texture IDs. */\n    textureIds: number[] = [];\n\n    /**\n     * The collection of drawn shapes.\n     * @member {PIXI.GraphicsData[]}\n     */\n    graphicsData: Array<GraphicsData> = [];\n\n    /**\n     * List of current draw calls drived from the batches.\n     * @member {PIXI.BatchDrawCall[]}\n     */\n    drawCalls: Array<BatchDrawCall> = [];\n\n    /** Batches need to regenerated if the geometry is updated. */\n    batchDirty = -1;\n\n    /**\n     * Intermediate abstract format sent to batch system.\n     * Can be converted to drawCalls or to batchable objects.\n     * @member {PIXI.graphicsUtils.BatchPart[]}\n     */\n    batches: Array<BatchPart> = [];\n\n    /** Used to detect if the graphics object has changed. */\n    protected dirty = 0;\n\n    /** Used to check if the cache is dirty. */\n    protected cacheDirty = -1;\n\n    /** Used to detect if we cleared the graphicsData. */\n    protected clearDirty = 0;\n\n    /** Index of the last batched shape in the stack of calls. */\n    protected shapeIndex = 0;\n\n    /** Cached bounds. */\n    protected _bounds: Bounds = new Bounds();\n\n    /** The bounds dirty flag. */\n    protected boundsDirty = -1;\n\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor()\n    {\n        super();\n    }\n\n    /**\n     * Get the current bounds of the graphic geometry.\n     *\n     * Since 6.5.0, bounds of the graphics geometry are calculated based on the vertices of generated geometry.\n     * Since shapes or strokes with full transparency (`alpha: 0`) will not generate geometry, they are not considered\n     * when calculating bounds for the graphics geometry. See PR [#8343]{@link https://github.com/pixijs/pixijs/pull/8343}\n     * and issue [#8623]{@link https://github.com/pixijs/pixijs/pull/8623}.\n     * @readonly\n     */\n    public get bounds(): Bounds\n    {\n        this.updateBatches();\n\n        if (this.boundsDirty !== this.dirty)\n        {\n            this.boundsDirty = this.dirty;\n            this.calculateBounds();\n        }\n\n        return this._bounds;\n    }\n\n    /** Call if you changed graphicsData manually. Empties all batch buffers. */\n    protected invalidate(): void\n    {\n        this.boundsDirty = -1;\n        this.dirty++;\n        this.batchDirty++;\n        this.shapeIndex = 0;\n\n        this.points.length = 0;\n        this.colors.length = 0;\n        this.uvs.length = 0;\n        this.indices.length = 0;\n        this.textureIds.length = 0;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const batchPart = this.batches[i];\n\n            batchPart.reset();\n            BATCH_POOL.push(batchPart);\n        }\n\n        this.batches.length = 0;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This GraphicsGeometry object. Good for chaining method calls\n     */\n    public clear(): GraphicsGeometry\n    {\n        if (this.graphicsData.length > 0)\n        {\n            this.invalidate();\n            this.clearDirty++;\n            this.graphicsData.length = 0;\n        }\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - Defines style of the fill.\n     * @param lineStyle - Defines style of the lines.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawShape(\n        shape: IShape,\n        fillStyle: FillStyle = null,\n        lineStyle: LineStyle = null,\n        matrix: Matrix = null): GraphicsGeometry\n    {\n        const data = new GraphicsData(shape, fillStyle, lineStyle, matrix);\n\n        this.graphicsData.push(data);\n        this.dirty++;\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawHole(shape: IShape, matrix: Matrix = null): GraphicsGeometry\n    {\n        if (!this.graphicsData.length)\n        {\n            return null;\n        }\n\n        const data = new GraphicsData(shape, null, null, matrix);\n\n        const lastShape = this.graphicsData[this.graphicsData.length - 1];\n\n        data.lineStyle = lastShape.lineStyle;\n\n        lastShape.holes.push(data);\n\n        this.dirty++;\n\n        return this;\n    }\n\n    /** Destroys the GraphicsGeometry object. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        // destroy each of the GraphicsData objects\n        for (let i = 0; i < this.graphicsData.length; ++i)\n        {\n            this.graphicsData[i].destroy();\n        }\n\n        this.points.length = 0;\n        this.points = null;\n        this.colors.length = 0;\n        this.colors = null;\n        this.uvs.length = 0;\n        this.uvs = null;\n        this.indices.length = 0;\n        this.indices = null;\n        this.indexBuffer.destroy();\n        this.indexBuffer = null;\n        this.graphicsData.length = 0;\n        this.graphicsData = null;\n        this.drawCalls.length = 0;\n        this.drawCalls = null;\n        this.batches.length = 0;\n        this.batches = null;\n        this._bounds = null;\n    }\n\n    /**\n     * Check to see if a point is contained within this geometry.\n     * @param point - Point to check if it's contained.\n     * @returns {boolean} `true` if the point is contained within geometry.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        const graphicsData = this.graphicsData;\n\n        for (let i = 0; i < graphicsData.length; ++i)\n        {\n            const data = graphicsData[i];\n\n            if (!data.fillStyle.visible)\n            {\n                continue;\n            }\n\n            // only deal with fills..\n            if (data.shape)\n            {\n                if (data.matrix)\n                {\n                    data.matrix.applyInverse(point, tmpPoint);\n                }\n                else\n                {\n                    tmpPoint.copyFrom(point);\n                }\n\n                if (data.shape.contains(tmpPoint.x, tmpPoint.y))\n                {\n                    let hitHole = false;\n\n                    if (data.holes)\n                    {\n                        for (let i = 0; i < data.holes.length; i++)\n                        {\n                            const hole = data.holes[i];\n\n                            if (hole.shape.contains(tmpPoint.x, tmpPoint.y))\n                            {\n                                hitHole = true;\n                                break;\n                            }\n                        }\n                    }\n\n                    if (!hitHole)\n                    {\n                        return true;\n                    }\n                }\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Generates intermediate batch data. Either gets converted to drawCalls\n     * or used to convert to batch objects directly by the Graphics object.\n     */\n    updateBatches(): void\n    {\n        if (!this.graphicsData.length)\n        {\n            this.batchable = true;\n\n            return;\n        }\n\n        if (!this.validateBatching())\n        {\n            return;\n        }\n\n        this.cacheDirty = this.dirty;\n\n        const uvs = this.uvs;\n        const graphicsData = this.graphicsData;\n\n        let batchPart: BatchPart = null;\n\n        let currentStyle = null;\n\n        if (this.batches.length > 0)\n        {\n            batchPart = this.batches[this.batches.length - 1];\n            currentStyle = batchPart.style;\n        }\n\n        for (let i = this.shapeIndex; i < graphicsData.length; i++)\n        {\n            this.shapeIndex++;\n\n            const data = graphicsData[i];\n            const fillStyle = data.fillStyle;\n            const lineStyle = data.lineStyle;\n            const command = FILL_COMMANDS[data.type];\n\n            // build out the shapes points..\n            command.build(data);\n\n            if (data.matrix)\n            {\n                this.transformPoints(data.points, data.matrix);\n            }\n\n            if (fillStyle.visible || lineStyle.visible)\n            {\n                this.processHoles(data.holes);\n            }\n\n            for (let j = 0; j < 2; j++)\n            {\n                const style = (j === 0) ? fillStyle : lineStyle;\n\n                if (!style.visible) continue;\n\n                const nextTexture = style.texture.baseTexture;\n                const index = this.indices.length;\n                const attribIndex = this.points.length / 2;\n\n                nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                if (j === 0)\n                {\n                    this.processFill(data);\n                }\n                else\n                {\n                    this.processLine(data);\n                }\n\n                const size = (this.points.length / 2) - attribIndex;\n\n                if (size === 0) continue;\n                // close batch if style is different\n                if (batchPart && !this._compareStyles(currentStyle, style))\n                {\n                    batchPart.end(index, attribIndex);\n                    batchPart = null;\n                }\n                // spawn new batch if its first batch or previous was closed\n                if (!batchPart)\n                {\n                    batchPart = BATCH_POOL.pop() || new BatchPart();\n                    batchPart.begin(style, index, attribIndex);\n                    this.batches.push(batchPart);\n                    currentStyle = style;\n                }\n\n                this.addUvs(this.points, uvs, style.texture, attribIndex, size, style.matrix);\n            }\n        }\n\n        const index = this.indices.length;\n        const attrib = this.points.length / 2;\n\n        if (batchPart)\n        {\n            batchPart.end(index, attrib);\n        }\n\n        if (this.batches.length === 0)\n        {\n            // there are no visible styles in GraphicsData\n            // its possible that someone wants Graphics just for the bounds\n            this.batchable = true;\n\n            return;\n        }\n\n        const need32 = attrib > 0xffff;\n\n        // prevent allocation when length is same as buffer\n        if (this.indicesUint16 && this.indices.length === this.indicesUint16.length\n            && need32 === (this.indicesUint16.BYTES_PER_ELEMENT > 2))\n        {\n            this.indicesUint16.set(this.indices);\n        }\n        else\n        {\n            this.indicesUint16 = need32 ? new Uint32Array(this.indices) : new Uint16Array(this.indices);\n        }\n\n        // TODO make this a const..\n        this.batchable = this.isBatchable();\n\n        if (this.batchable)\n        {\n            this.packBatches();\n        }\n        else\n        {\n            this.buildDrawCalls();\n        }\n    }\n\n    /**\n     * Affinity check\n     * @param styleA\n     * @param styleB\n     */\n    protected _compareStyles(styleA: FillStyle | LineStyle, styleB: FillStyle | LineStyle): boolean\n    {\n        if (!styleA || !styleB)\n        {\n            return false;\n        }\n\n        if (styleA.texture.baseTexture !== styleB.texture.baseTexture)\n        {\n            return false;\n        }\n\n        if (styleA.color + styleA.alpha !== styleB.color + styleB.alpha)\n        {\n            return false;\n        }\n\n        if (!!(styleA as LineStyle).native !== !!(styleB as LineStyle).native)\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /** Test geometry for batching process. */\n    protected validateBatching(): boolean\n    {\n        if (this.dirty === this.cacheDirty || !this.graphicsData.length)\n        {\n            return false;\n        }\n\n        for (let i = 0, l = this.graphicsData.length; i < l; i++)\n        {\n            const data = this.graphicsData[i];\n            const fill = data.fillStyle;\n            const line = data.lineStyle;\n\n            if (fill && !fill.texture.baseTexture.valid) return false;\n            if (line && !line.texture.baseTexture.valid) return false;\n        }\n\n        return true;\n    }\n\n    /** Offset the indices so that it works with the batcher. */\n    protected packBatches(): void\n    {\n        this.batchDirty++;\n        this.uvsFloat32 = new Float32Array(this.uvs);\n\n        const batches = this.batches;\n\n        for (let i = 0, l = batches.length; i < l; i++)\n        {\n            const batch = batches[i];\n\n            for (let j = 0; j < batch.size; j++)\n            {\n                const index = batch.start + j;\n\n                this.indicesUint16[index] = this.indicesUint16[index] - batch.attribStart;\n            }\n        }\n    }\n\n    /**\n     * Checks to see if this graphics geometry can be batched.\n     * Currently it needs to be small enough and not contain any native lines.\n     */\n    protected isBatchable(): boolean\n    {\n        // prevent heavy mesh batching\n        if (this.points.length > 0xffff * 2)\n        {\n            return false;\n        }\n\n        const batches = this.batches;\n\n        for (let i = 0; i < batches.length; i++)\n        {\n            if ((batches[i].style as LineStyle).native)\n            {\n                return false;\n            }\n        }\n\n        return (this.points.length < GraphicsGeometry.BATCHABLE_SIZE * 2);\n    }\n\n    /** Converts intermediate batches data to drawCalls. */\n    protected buildDrawCalls(): void\n    {\n        let TICK = ++BaseTexture._globalBatch;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        let currentGroup: BatchDrawCall = DRAW_CALL_POOL.pop();\n\n        if (!currentGroup)\n        {\n            currentGroup = new BatchDrawCall();\n            currentGroup.texArray = new BatchTextureArray();\n        }\n        currentGroup.texArray.count = 0;\n        currentGroup.start = 0;\n        currentGroup.size = 0;\n        currentGroup.type = DRAW_MODES.TRIANGLES;\n\n        let textureCount = 0;\n        let currentTexture = null;\n        let textureId = 0;\n        let native = false;\n        let drawMode = DRAW_MODES.TRIANGLES;\n\n        let index = 0;\n\n        this.drawCalls.push(currentGroup);\n\n        // TODO - this can be simplified\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const data = this.batches[i];\n\n            // TODO add some full on MAX_TEXTURE CODE..\n            const maxTextures = 8;\n\n            // Forced cast for checking `native` without errors\n            const style = data.style as LineStyle;\n\n            const nextTexture = style.texture.baseTexture;\n\n            if (native !== !!style.native)\n            {\n                native = !!style.native;\n                drawMode = native ? DRAW_MODES.LINES : DRAW_MODES.TRIANGLES;\n\n                // force the batch to break!\n                currentTexture = null;\n                textureCount = maxTextures;\n                TICK++;\n            }\n\n            if (currentTexture !== nextTexture)\n            {\n                currentTexture = nextTexture;\n\n                if (nextTexture._batchEnabled !== TICK)\n                {\n                    if (textureCount === maxTextures)\n                    {\n                        TICK++;\n\n                        textureCount = 0;\n\n                        if (currentGroup.size > 0)\n                        {\n                            currentGroup = DRAW_CALL_POOL.pop();\n                            if (!currentGroup)\n                            {\n                                currentGroup = new BatchDrawCall();\n                                currentGroup.texArray = new BatchTextureArray();\n                            }\n                            this.drawCalls.push(currentGroup);\n                        }\n\n                        currentGroup.start = index;\n                        currentGroup.size = 0;\n                        currentGroup.texArray.count = 0;\n                        currentGroup.type = drawMode;\n                    }\n\n                    // TODO add this to the render part..\n                    // Hack! Because texture has protected `touched`\n                    nextTexture.touched = 1;// touch;\n\n                    nextTexture._batchEnabled = TICK;\n                    nextTexture._batchLocation = textureCount;\n                    nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                    currentGroup.texArray.elements[currentGroup.texArray.count++] = nextTexture;\n                    textureCount++;\n                }\n            }\n\n            currentGroup.size += data.size;\n            index += data.size;\n\n            textureId = nextTexture._batchLocation;\n\n            this.addColors(colors, style.color, style.alpha, data.attribSize, data.attribStart);\n            this.addTextureIds(textureIds, textureId, data.attribSize, data.attribStart);\n        }\n\n        BaseTexture._globalBatch = TICK;\n\n        // upload..\n        // merge for now!\n        this.packAttributes();\n    }\n\n    /** Packs attributes to single buffer. */\n    protected packAttributes(): void\n    {\n        const verts = this.points;\n        const uvs = this.uvs;\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        // verts are 2 positions.. so we * by 3 as there are 6 properties.. then 4 cos its bytes\n        const glPoints = new ArrayBuffer(verts.length * 3 * 4);\n        const f32 = new Float32Array(glPoints);\n        const u32 = new Uint32Array(glPoints);\n\n        let p = 0;\n\n        for (let i = 0; i < verts.length / 2; i++)\n        {\n            f32[p++] = verts[i * 2];\n            f32[p++] = verts[(i * 2) + 1];\n\n            f32[p++] = uvs[i * 2];\n            f32[p++] = uvs[(i * 2) + 1];\n\n            u32[p++] = colors[i];\n\n            f32[p++] = textureIds[i];\n        }\n\n        this._buffer.update(glPoints);\n        this._indexBuffer.update(this.indicesUint16);\n    }\n\n    /**\n     * Process fill part of Graphics.\n     * @param data\n     */\n    protected processFill(data: GraphicsData): void\n    {\n        if (data.holes.length)\n        {\n            buildPoly.triangulate(data, this);\n        }\n        else\n        {\n            const command = FILL_COMMANDS[data.type];\n\n            command.triangulate(data, this);\n        }\n    }\n\n    /**\n     * Process line part of Graphics.\n     * @param data\n     */\n    protected processLine(data: GraphicsData): void\n    {\n        buildLine(data, this);\n\n        for (let i = 0; i < data.holes.length; i++)\n        {\n            buildLine(data.holes[i], this);\n        }\n    }\n\n    /**\n     * Process the holes data.\n     * @param holes\n     */\n    protected processHoles(holes: Array<GraphicsData>): void\n    {\n        for (let i = 0; i < holes.length; i++)\n        {\n            const hole = holes[i];\n            const command = FILL_COMMANDS[hole.type];\n\n            command.build(hole);\n\n            if (hole.matrix)\n            {\n                this.transformPoints(hole.points, hole.matrix);\n            }\n        }\n    }\n\n    /** Update the local bounds of the object. Expensive to use performance-wise. */\n    protected calculateBounds(): void\n    {\n        const bounds = this._bounds;\n\n        bounds.clear();\n        bounds.addVertexData((this.points as any), 0, this.points.length);\n        bounds.pad(this.boundsPadding, this.boundsPadding);\n    }\n\n    /**\n     * Transform points using matrix.\n     * @param points - Points to transform\n     * @param matrix - Transform matrix\n     */\n    protected transformPoints(points: Array<number>, matrix: Matrix): void\n    {\n        for (let i = 0; i < points.length / 2; i++)\n        {\n            const x = points[(i * 2)];\n            const y = points[(i * 2) + 1];\n\n            points[(i * 2)] = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n            points[(i * 2) + 1] = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n        }\n    }\n\n    /**\n     * Add colors.\n     * @param colors - List of colors to add to\n     * @param color - Color to add\n     * @param alpha - Alpha to use\n     * @param size - Number of colors to add\n     * @param offset\n     */\n    protected addColors(\n        colors: Array<number>,\n        color: number,\n        alpha: number,\n        size: number,\n        offset = 0): void\n    {\n        const bgr = Color.shared\n            .setValue(color)\n            .toLittleEndianNumber();\n\n        const result = Color.shared\n            .setValue(bgr)\n            .toPremultiplied(alpha);\n\n        colors.length = Math.max(colors.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            colors[offset + i] = result;\n        }\n    }\n\n    /**\n     * Add texture id that the shader/fragment wants to use.\n     * @param textureIds\n     * @param id\n     * @param size\n     * @param offset\n     */\n    protected addTextureIds(\n        textureIds: Array<number>,\n        id: number,\n        size: number,\n        offset = 0): void\n    {\n        textureIds.length = Math.max(textureIds.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            textureIds[offset + i] = id;\n        }\n    }\n\n    /**\n     * Generates the UVs for a shape.\n     * @param verts - Vertices\n     * @param uvs - UVs\n     * @param texture - Reference to Texture\n     * @param start - Index buffer start index.\n     * @param size - The size/length for index buffer.\n     * @param matrix - Optional transform for all points.\n     */\n    protected addUvs(\n        verts: Array<number>,\n        uvs: Array<number>,\n        texture: Texture,\n        start: number,\n        size: number,\n        matrix: Matrix = null): void\n    {\n        let index = 0;\n        const uvsStart = uvs.length;\n        const frame = texture.frame;\n\n        while (index < size)\n        {\n            let x = verts[(start + index) * 2];\n            let y = verts[((start + index) * 2) + 1];\n\n            if (matrix)\n            {\n                const nx = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n\n                y = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n                x = nx;\n            }\n\n            index++;\n\n            uvs.push(x / frame.width, y / frame.height);\n        }\n\n        const baseTexture = texture.baseTexture;\n\n        if (frame.width < baseTexture.width\n            || frame.height < baseTexture.height)\n        {\n            this.adjustUvs(uvs, texture, uvsStart, size);\n        }\n    }\n\n    /**\n     * Modify uvs array according to position of texture region\n     * Does not work with rotated or trimmed textures\n     * @param uvs - array\n     * @param texture - region\n     * @param start - starting index for uvs\n     * @param size - how many points to adjust\n     */\n    protected adjustUvs(uvs: Array<number>, texture: Texture, start: number, size: number): void\n    {\n        const baseTexture = texture.baseTexture;\n        const eps = 1e-6;\n        const finish = start + (size * 2);\n        const frame = texture.frame;\n        const scaleX = frame.width / baseTexture.width;\n        const scaleY = frame.height / baseTexture.height;\n        let offsetX = frame.x / frame.width;\n        let offsetY = frame.y / frame.height;\n        let minX = Math.floor(uvs[start] + eps);\n        let minY = Math.floor(uvs[start + 1] + eps);\n\n        for (let i = start + 2; i < finish; i += 2)\n        {\n            minX = Math.min(minX, Math.floor(uvs[i] + eps));\n            minY = Math.min(minY, Math.floor(uvs[i + 1] + eps));\n        }\n        offsetX -= minX;\n        offsetY -= minY;\n        for (let i = start; i < finish; i += 2)\n        {\n            uvs[i] = (uvs[i] + offsetX) * scaleX;\n            uvs[i + 1] = (uvs[i + 1] + offsetY) * scaleY;\n        }\n    }\n}\n"], "names": ["_GraphicsGeometry", "i", "index"], "mappings": ";;;;;;;AAuBA,MAAM,WAAW,IAAI,SAUR,oBAAN,MAAMA,2BAAyB,cACtC;AAAA;AAAA,EAsEI,cACA;AACU,aAnEV,KAAO,gBAAgB,MAGvB,KAAO,gBAAgB,GAEI,KAAA,aAAA,MACgB,KAAA,gBAAA,MAC/B,KAAA,YAAA,IAGZ,KAAA,SAAmB,IAGnB,KAAA,SAAmB,IAGnB,KAAA,MAAgB,IAGhB,KAAA,UAAoB,IAGpB,KAAA,aAAuB,IAMvB,KAAA,eAAoC,IAMpC,KAAA,YAAkC,IAGrB,KAAA,aAAA,IAOb,KAAA,UAA4B,IAG5B,KAAU,QAAQ,GAGlB,KAAU,aAAa,IAGvB,KAAU,aAAa,GAGvB,KAAU,aAAa,GAGb,KAAA,UAAkB,IAAI,UAGhC,KAAU,cAAc;AAAA,EAMxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAW,SACX;AACI,WAAA,KAAK,cAAc,GAEf,KAAK,gBAAgB,KAAK,UAE1B,KAAK,cAAc,KAAK,OACxB,KAAK,gBAAA,IAGF,KAAK;AAAA,EAChB;AAAA;AAAA,EAGU,aACV;AACS,SAAA,cAAc,IACnB,KAAK,SACL,KAAK,cACL,KAAK,aAAa,GAElB,KAAK,OAAO,SAAS,GACrB,KAAK,OAAO,SAAS,GACrB,KAAK,IAAI,SAAS,GAClB,KAAK,QAAQ,SAAS,GACtB,KAAK,WAAW,SAAS;AAEzB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ;AAElC,WAAA,UAAU,CAAC,EAAE,SAAS,MAAA,GAC3B,eAAe,KAAK,KAAK,UAAU,CAAC,CAAC;AAGzC,SAAK,UAAU,SAAS;AAExB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KACzC;AACU,YAAA,YAAY,KAAK,QAAQ,CAAC;AAEhC,gBAAU,MAAM,GAChB,WAAW,KAAK,SAAS;AAAA,IAC7B;AAEA,SAAK,QAAQ,SAAS;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,QACP;AACI,WAAI,KAAK,aAAa,SAAS,MAE3B,KAAK,WACL,GAAA,KAAK,cACL,KAAK,aAAa,SAAS,IAGxB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,UACH,OACA,YAAuB,MACvB,YAAuB,MACvB,SAAiB,MACrB;AACI,UAAM,OAAO,IAAI,aAAa,OAAO,WAAW,WAAW,MAAM;AAEjE,WAAA,KAAK,aAAa,KAAK,IAAI,GAC3B,KAAK,SAEE;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,SAAS,OAAe,SAAiB,MAChD;AACQ,QAAA,CAAC,KAAK,aAAa;AAEZ,aAAA;AAGX,UAAM,OAAO,IAAI,aAAa,OAAO,MAAM,MAAM,MAAM,GAEjD,YAAY,KAAK,aAAa,KAAK,aAAa,SAAS,CAAC;AAE3D,WAAA,KAAA,YAAY,UAAU,WAE3B,UAAU,MAAM,KAAK,IAAI,GAEzB,KAAK,SAEE;AAAA,EACX;AAAA;AAAA,EAGO,UACP;AACI,UAAM,QAAQ;AAGd,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,EAAE;AAEvC,WAAA,aAAa,CAAC,EAAE,QAAQ;AAG5B,SAAA,OAAO,SAAS,GACrB,KAAK,SAAS,MACd,KAAK,OAAO,SAAS,GACrB,KAAK,SAAS,MACd,KAAK,IAAI,SAAS,GAClB,KAAK,MAAM,MACX,KAAK,QAAQ,SAAS,GACtB,KAAK,UAAU,MACf,KAAK,YAAY,QAAQ,GACzB,KAAK,cAAc,MACnB,KAAK,aAAa,SAAS,GAC3B,KAAK,eAAe,MACpB,KAAK,UAAU,SAAS,GACxB,KAAK,YAAY,MACjB,KAAK,QAAQ,SAAS,GACtB,KAAK,UAAU,MACf,KAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OACrB;AACI,UAAM,eAAe,KAAK;AAE1B,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAC3C;AACU,YAAA,OAAO,aAAa,CAAC;AAEtB,UAAA,KAAK,UAAU,WAMhB,KAAK,UAED,KAAK,SAEL,KAAK,OAAO,aAAa,OAAO,QAAQ,IAIxC,SAAS,SAAS,KAAK,GAGvB,KAAK,MAAM,SAAS,SAAS,GAAG,SAAS,CAAC,IAC9C;AACI,YAAI,UAAU;AAEd,YAAI,KAAK;AAEL,mBAASC,KAAI,GAAGA,KAAI,KAAK,MAAM,QAAQA;AAEtB,gBAAA,KAAK,MAAMA,EAAC,EAEhB,MAAM,SAAS,SAAS,GAAG,SAAS,CAAC,GAC9C;AACc,wBAAA;AACV;AAAA,YACJ;AAAA;AAIR,YAAI,CAAC;AAEM,iBAAA;AAAA,MAEf;AAAA,IAER;AAEO,WAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBACA;AACQ,QAAA,CAAC,KAAK,aAAa,QACvB;AACI,WAAK,YAAY;AAEjB;AAAA,IACJ;AAEI,QAAA,CAAC,KAAK,iBAAiB;AAEvB;AAGJ,SAAK,aAAa,KAAK;AAEvB,UAAM,MAAM,KAAK,KACX,eAAe,KAAK;AAEtB,QAAA,YAAuB,MAEvB,eAAe;AAEf,SAAK,QAAQ,SAAS,MAEtB,YAAY,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,GAChD,eAAe,UAAU;AAG7B,aAAS,IAAI,KAAK,YAAY,IAAI,aAAa,QAAQ,KACvD;AACS,WAAA;AAEC,YAAA,OAAO,aAAa,CAAC,GACrB,YAAY,KAAK,WACjB,YAAY,KAAK;AACP,oBAAc,KAAK,IAAI,EAG/B,MAAM,IAAI,GAEd,KAAK,UAEL,KAAK,gBAAgB,KAAK,QAAQ,KAAK,MAAM,IAG7C,UAAU,WAAW,UAAU,YAE/B,KAAK,aAAa,KAAK,KAAK;AAGhC,eAAS,IAAI,GAAG,IAAI,GAAG,KACvB;AACU,cAAA,QAAS,MAAM,IAAK,YAAY;AAEtC,YAAI,CAAC,MAAM;AAAS;AAEd,cAAA,cAAc,MAAM,QAAQ,aAC5BC,SAAQ,KAAK,QAAQ,QACrB,cAAc,KAAK,OAAO,SAAS;AAE7B,oBAAA,WAAW,WAAW,QAE9B,MAAM,IAEN,KAAK,YAAY,IAAI,IAIrB,KAAK,YAAY,IAAI;AAGzB,cAAM,OAAQ,KAAK,OAAO,SAAS,IAAK;AAEpC,iBAAS,MAET,aAAa,CAAC,KAAK,eAAe,cAAc,KAAK,MAErD,UAAU,IAAIA,QAAO,WAAW,GAChC,YAAY,OAGX,cAED,YAAY,WAAW,IAAI,KAAK,IAAI,UAAA,GACpC,UAAU,MAAM,OAAOA,QAAO,WAAW,GACzC,KAAK,QAAQ,KAAK,SAAS,GAC3B,eAAe,QAGnB,KAAK,OAAO,KAAK,QAAQ,KAAK,MAAM,SAAS,aAAa,MAAM,MAAM,MAAM;AAAA,MAChF;AAAA,IACJ;AAEA,UAAM,QAAQ,KAAK,QAAQ,QACrB,SAAS,KAAK,OAAO,SAAS;AAEhC,QAAA,aAEA,UAAU,IAAI,OAAO,MAAM,GAG3B,KAAK,QAAQ,WAAW,GAC5B;AAGI,WAAK,YAAY;AAEjB;AAAA,IACJ;AAEA,UAAM,SAAS,SAAS;AAGpB,SAAK,iBAAiB,KAAK,QAAQ,WAAW,KAAK,cAAc,UAC9D,WAAY,KAAK,cAAc,oBAAoB,IAEtD,KAAK,cAAc,IAAI,KAAK,OAAO,IAInC,KAAK,gBAAgB,SAAS,IAAI,YAAY,KAAK,OAAO,IAAI,IAAI,YAAY,KAAK,OAAO,GAI9F,KAAK,YAAY,KAAK,eAElB,KAAK,YAEL,KAAK,YAAY,IAIjB,KAAK;EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,eAAe,QAA+B,QACxD;AACQ,WAAA,EAAA,CAAC,UAAU,CAAC,UAKZ,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,eAK9C,OAAO,QAAQ,OAAO,UAAU,OAAO,QAAQ,OAAO,SAKtD,CAAC,CAAE,OAAqB,UAAW,CAAC,CAAE,OAAqB;AAAA,EAMnE;AAAA;AAAA,EAGU,mBACV;AACI,QAAI,KAAK,UAAU,KAAK,cAAc,CAAC,KAAK,aAAa;AAE9C,aAAA;AAGF,aAAA,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,IAAI,GAAG,KACrD;AACU,YAAA,OAAO,KAAK,aAAa,CAAC,GAC1B,OAAO,KAAK,WACZ,OAAO,KAAK;AAEd,UAAA,QAAQ,CAAC,KAAK,QAAQ,YAAY,SAClC,QAAQ,CAAC,KAAK,QAAQ,YAAY;AAAc,eAAA;AAAA,IACxD;AAEO,WAAA;AAAA,EACX;AAAA;AAAA,EAGU,cACV;AACI,SAAK,cACL,KAAK,aAAa,IAAI,aAAa,KAAK,GAAG;AAE3C,UAAM,UAAU,KAAK;AAErB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAC3C;AACU,YAAA,QAAQ,QAAQ,CAAC;AAEvB,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,KAChC;AACU,cAAA,QAAQ,MAAM,QAAQ;AAE5B,aAAK,cAAc,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,MAAM;AAAA,MAClE;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,cACV;AAEQ,QAAA,KAAK,OAAO,SAAS,QAAS;AAEvB,aAAA;AAGX,UAAM,UAAU,KAAK;AAErB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAE3B,UAAA,QAAQ,CAAC,EAAE,MAAoB;AAEzB,eAAA;AAIf,WAAQ,KAAK,OAAO,SAASF,mBAAiB,iBAAiB;AAAA,EACnE;AAAA;AAAA,EAGU,iBACV;AACQ,QAAA,OAAO,EAAE,YAAY;AAEzB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ;AAElC,WAAA,UAAU,CAAC,EAAE,SAAS,MAAA,GAC3B,eAAe,KAAK,KAAK,UAAU,CAAC,CAAC;AAGzC,SAAK,UAAU,SAAS;AAExB,UAAM,SAAS,KAAK,QACd,aAAa,KAAK;AAEpB,QAAA,eAA8B,eAAe;AAE5C,qBAED,eAAe,IAAI,cAAA,GACnB,aAAa,WAAW,IAAI,kBAEhC,IAAA,aAAa,SAAS,QAAQ,GAC9B,aAAa,QAAQ,GACrB,aAAa,OAAO,GACpB,aAAa,OAAO,WAAW;AAE3B,QAAA,eAAe,GACf,iBAAiB,MACjB,YAAY,GACZ,SAAS,IACT,WAAW,WAAW,WAEtB,QAAQ;AAEP,SAAA,UAAU,KAAK,YAAY;AAGhC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KACzC;AACI,YAAM,OAAO,KAAK,QAAQ,CAAC,GAGrB,cAAc,GAGd,QAAQ,KAAK,OAEb,cAAc,MAAM,QAAQ;AAE9B,iBAAW,CAAC,CAAC,MAAM,WAEnB,SAAS,CAAC,CAAC,MAAM,QACjB,WAAW,SAAS,WAAW,QAAQ,WAAW,WAGlD,iBAAiB,MACjB,eAAe,aACf,SAGA,mBAAmB,gBAEnB,iBAAiB,aAEb,YAAY,kBAAkB,SAE1B,iBAAiB,gBAEjB,QAEA,eAAe,GAEX,aAAa,OAAO,MAEpB,eAAe,eAAe,IACzB,GAAA,iBAED,eAAe,IAAI,cAAc,GACjC,aAAa,WAAW,IAAI,kBAAA,IAEhC,KAAK,UAAU,KAAK,YAAY,IAGpC,aAAa,QAAQ,OACrB,aAAa,OAAO,GACpB,aAAa,SAAS,QAAQ,GAC9B,aAAa,OAAO,WAKxB,YAAY,UAAU,GAEtB,YAAY,gBAAgB,MAC5B,YAAY,iBAAiB,cAC7B,YAAY,WAAW,WAAW,QAElC,aAAa,SAAS,SAAS,aAAa,SAAS,OAAO,IAAI,aAChE,kBAIR,aAAa,QAAQ,KAAK,MAC1B,SAAS,KAAK,MAEd,YAAY,YAAY,gBAExB,KAAK,UAAU,QAAQ,MAAM,OAAO,MAAM,OAAO,KAAK,YAAY,KAAK,WAAW,GAClF,KAAK,cAAc,YAAY,WAAW,KAAK,YAAY,KAAK,WAAW;AAAA,IAC/E;AAEY,gBAAA,eAAe,MAI3B,KAAK,eAAe;AAAA,EACxB;AAAA;AAAA,EAGU,iBACV;AACI,UAAM,QAAQ,KAAK,QACb,MAAM,KAAK,KACX,SAAS,KAAK,QACd,aAAa,KAAK,YAGlB,WAAW,IAAI,YAAY,MAAM,SAAS,IAAI,CAAC,GAC/C,MAAM,IAAI,aAAa,QAAQ,GAC/B,MAAM,IAAI,YAAY,QAAQ;AAEpC,QAAI,IAAI;AAER,aAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG;AAElC,UAAI,GAAG,IAAI,MAAM,IAAI,CAAC,GACtB,IAAI,GAAG,IAAI,MAAO,IAAI,IAAK,CAAC,GAE5B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GACpB,IAAI,GAAG,IAAI,IAAK,IAAI,IAAK,CAAC,GAE1B,IAAI,GAAG,IAAI,OAAO,CAAC,GAEnB,IAAI,GAAG,IAAI,WAAW,CAAC;AAGtB,SAAA,QAAQ,OAAO,QAAQ,GAC5B,KAAK,aAAa,OAAO,KAAK,aAAa;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,YAAY,MACtB;AACQ,SAAK,MAAM,SAEX,UAAU,YAAY,MAAM,IAAI,IAIhB,cAAc,KAAK,IAAI,EAE/B,YAAY,MAAM,IAAI;AAAA,EAEtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,YAAY,MACtB;AACI,cAAU,MAAM,IAAI;AAEpB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AAEnC,gBAAU,KAAK,MAAM,CAAC,GAAG,IAAI;AAAA,EAErC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,aAAa,OACvB;AACI,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAClC;AACU,YAAA,OAAO,MAAM,CAAC;AACJ,oBAAc,KAAK,IAAI,EAE/B,MAAM,IAAI,GAEd,KAAK,UAEL,KAAK,gBAAgB,KAAK,QAAQ,KAAK,MAAM;AAAA,IAErD;AAAA,EACJ;AAAA;AAAA,EAGU,kBACV;AACI,UAAM,SAAS,KAAK;AAEpB,WAAO,SACP,OAAO,cAAe,KAAK,QAAgB,GAAG,KAAK,OAAO,MAAM,GAChE,OAAO,IAAI,KAAK,eAAe,KAAK,aAAa;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,gBAAgB,QAAuB,QACjD;AACI,aAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KACvC;AACU,YAAA,IAAI,OAAQ,IAAI,CAAE,GAClB,IAAI,OAAQ,IAAI,IAAK,CAAC;AAEpB,aAAA,IAAI,CAAE,IAAK,OAAO,IAAI,IAAM,OAAO,IAAI,IAAK,OAAO,IAC3D,OAAQ,IAAI,IAAK,CAAC,IAAK,OAAO,IAAI,IAAM,OAAO,IAAI,IAAK,OAAO;AAAA,IACnE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUU,UACN,QACA,OACA,OACA,MACA,SAAS,GACb;AACI,UAAM,MAAM,MAAM,OACb,SAAS,KAAK,EACd,qBAEC,GAAA,SAAS,MAAM,OAChB,SAAS,GAAG,EACZ,gBAAgB,KAAK;AAE1B,WAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,SAAS,IAAI;AAE5C,aAAA,IAAI,GAAG,IAAI,MAAM;AAEf,aAAA,SAAS,CAAC,IAAI;AAAA,EAE7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASU,cACN,YACA,IACA,MACA,SAAS,GACb;AACI,eAAW,SAAS,KAAK,IAAI,WAAW,QAAQ,SAAS,IAAI;AAEpD,aAAA,IAAI,GAAG,IAAI,MAAM;AAEX,iBAAA,SAAS,CAAC,IAAI;AAAA,EAEjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWU,OACN,OACA,KACA,SACA,OACA,MACA,SAAiB,MACrB;AACI,QAAI,QAAQ;AACZ,UAAM,WAAW,IAAI,QACf,QAAQ,QAAQ;AAEtB,WAAO,QAAQ,QACf;AACQ,UAAA,IAAI,OAAO,QAAQ,SAAS,CAAC,GAC7B,IAAI,OAAQ,QAAQ,SAAS,IAAK,CAAC;AAEvC,UAAI,QACJ;AACI,cAAM,KAAM,OAAO,IAAI,IAAM,OAAO,IAAI,IAAK,OAAO;AAE/C,YAAA,OAAO,IAAI,IAAM,OAAO,IAAI,IAAK,OAAO,IAC7C,IAAI;AAAA,MACR;AAEA,eAEA,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM;AAAA,IAC9C;AAEA,UAAM,cAAc,QAAQ;AAE5B,KAAI,MAAM,QAAQ,YAAY,SACvB,MAAM,SAAS,YAAY,WAE9B,KAAK,UAAU,KAAK,SAAS,UAAU,IAAI;AAAA,EAEnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUU,UAAU,KAAoB,SAAkB,OAAe,MACzE;AACU,UAAA,cAAc,QAAQ,aACtB,MAAM,MACN,SAAS,QAAS,OAAO,GACzB,QAAQ,QAAQ,OAChB,SAAS,MAAM,QAAQ,YAAY,OACnC,SAAS,MAAM,SAAS,YAAY;AACtC,QAAA,UAAU,MAAM,IAAI,MAAM,OAC1B,UAAU,MAAM,IAAI,MAAM,QAC1B,OAAO,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,GAClC,OAAO,KAAK,MAAM,IAAI,QAAQ,CAAC,IAAI,GAAG;AAE1C,aAAS,IAAI,QAAQ,GAAG,IAAI,QAAQ,KAAK;AAE9B,aAAA,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,GAC9C,OAAO,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC;AAEtD,eAAW,MACX,WAAW;AACX,aAAS,IAAI,OAAO,IAAI,QAAQ,KAAK;AAEjC,UAAI,CAAC,KAAK,IAAI,CAAC,IAAI,WAAW,QAC9B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,WAAW;AAAA,EAE9C;AACJ;AAv3Ba,kBAGK,iBAAiB;AAH5B,IAAM,mBAAN;"}