{"version": 3, "file": "const.js", "sources": ["../src/const.ts"], "sourcesContent": ["/**\n * Represents the update priorities used by internal PIXI classes when registered with\n * the {@link PIXI.Ticker} object. Higher priority items are updated first and lower\n * priority items, such as render, should go later.\n * @static\n * @memberof PIXI\n * @enum {number}\n */\nexport enum UPDATE_PRIORITY\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * Highest priority used for interaction events in {@link PIXI.EventSystem}\n     * @default 50\n     */\n    INTERACTION = 50,\n    /**\n     * High priority updating, used by {@link PIXI.AnimatedSprite}\n     * @default 25\n     */\n    HIGH = 25,\n    /**\n     * Default priority for ticker events, see {@link PIXI.Ticker#add}.\n     * @default 0\n     */\n    NORMAL = 0,\n    /**\n     * Low priority used for {@link PIXI.Application} rendering.\n     * @default -25\n     */\n    LOW = -25,\n    /**\n     * Lowest priority used for {@link PIXI.BasePrepare} utility.\n     * @default -50\n     */\n    UTILITY = -50,\n}\n"], "names": ["UPDATE_PRIORITY"], "mappings": ";AAQY,IAAA,oCAAAA,sBAORA,iBAAAA,iBAAA,cAAc,EAAd,IAAA,eAKAA,iBAAA,iBAAA,OAAO,EAAP,IAAA,QAKAA,kCAAA,SAAS,CAAA,IAAT,UAKAA,iBAAAA,iBAAA,MAAM,GAAA,IAAN,OAKAA,iBAAA,iBAAA,UAAU,GAAV,IAAA,WA3BQA,mBAAA,mBAAA,CAAA,CAAA;;"}