{"version": 3, "file": "settings.mjs", "sources": ["../src/settings.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\nimport { deprecation } from '@pixi/utils';\nimport { Ticker } from './Ticker';\n\nObject.defineProperties(settings, {\n    /**\n     * Target frames per millisecond.\n     * @static\n     * @name TARGET_FPMS\n     * @memberof PIXI.settings\n     * @type {number}\n     * @deprecated since 7.1.0\n     * @see PIXI.Ticker.targetFPMS\n     */\n    TARGET_FPMS: {\n        get()\n        {\n            return Ticker.targetFPMS;\n        },\n        set(value: number)\n        {\n            if (process.env.DEBUG)\n            {\n                deprecation('7.1.0', 'settings.TARGET_FPMS is deprecated, use Ticker.targetFPMS');\n            }\n\n            Ticker.targetFPMS = value;\n        },\n    },\n});\n\nexport { settings };\n"], "names": [], "mappings": ";;;;AAIA,OAAO,iBAAiB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9B,aAAa;AAAA,IACT,MACA;AACI,aAAO,OAAO;AAAA,IAClB;AAAA,IACA,IAAI,OACJ;AAGQ,kBAAY,SAAS,2DAA2D,GAGpF,OAAO,aAAa;AAAA,IACxB;AAAA,EACJ;AACJ,CAAC;"}