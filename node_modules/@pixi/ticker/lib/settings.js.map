{"version": 3, "file": "settings.js", "sources": ["../src/settings.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\nimport { deprecation } from '@pixi/utils';\nimport { Ticker } from './Ticker';\n\nObject.defineProperties(settings, {\n    /**\n     * Target frames per millisecond.\n     * @static\n     * @name TARGET_FPMS\n     * @memberof PIXI.settings\n     * @type {number}\n     * @deprecated since 7.1.0\n     * @see PIXI.Ticker.targetFPMS\n     */\n    TARGET_FPMS: {\n        get()\n        {\n            return Ticker.targetFPMS;\n        },\n        set(value: number)\n        {\n            if (process.env.DEBUG)\n            {\n                deprecation('7.1.0', 'settings.TARGET_FPMS is deprecated, use Ticker.targetFPMS');\n            }\n\n            Ticker.targetFPMS = value;\n        },\n    },\n});\n\nexport { settings };\n"], "names": ["settings", "Ticker", "deprecation"], "mappings": ";;AAIA,OAAO,iBAAiBA,SAAAA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9B,aAAa;AAAA,IACT,MACA;AACI,aAAOC,OAAAA,OAAO;AAAA,IAClB;AAAA,IACA,IAAI,OACJ;AAGQC,YAAA,YAAY,SAAS,2DAA2D,GAGpFD,OAAAA,OAAO,aAAa;AAAA,IACxB;AAAA,EACJ;AACJ,CAAC;;;;;;;"}