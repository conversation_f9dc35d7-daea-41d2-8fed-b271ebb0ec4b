{"version": 3, "file": "TickerPlugin.js", "sources": ["../src/TickerPlugin.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { UPDATE_PRIORITY } from './const';\nimport { Ticker } from './Ticker';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\n\nexport interface TickerPluginOptions\n{\n    /**\n     * Automatically starts the rendering after the construction.\n     *  **Note**: Setting this parameter to `false` does NOT stop the shared ticker even if you set\n     *  `options.sharedTicker` to `true` in case that it is already started. Stop it by your own.\n     * @memberof PIXI.IApplicationOptions\n     * @default true\n     */\n    autoStart?: boolean;\n    /**\n     * Set`true` to use `Ticker.shared`, `false` to create new ticker.\n     *  If set to `false`, you cannot register a handler to occur before anything that runs on the shared ticker.\n     *  The system ticker will always run before both the shared ticker and the app ticker.\n     * @memberof PIXI.IApplicationOptions\n     * @default false\n     */\n    sharedTicker?: boolean;\n}\n\n/**\n * Middleware for for Application Ticker.\n * @class\n * @memberof PIXI\n */\nexport class TickerPlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Application;\n\n    static start: () => void;\n    static stop: () => void;\n    static _ticker: Ticker;\n    static ticker: Ticker;\n\n    /**\n     * Initialize the plugin with scope of application instance\n     * @static\n     * @private\n     * @param {object} [options] - See application options\n     */\n    static init(options?: GlobalMixins.IApplicationOptions): void\n    {\n        // Set default\n        options = Object.assign({\n            autoStart: true,\n            sharedTicker: false,\n        }, options);\n\n        // Create ticker setter\n        Object.defineProperty(this, 'ticker',\n            {\n                set(ticker)\n                {\n                    if (this._ticker)\n                    {\n                        this._ticker.remove(this.render, this);\n                    }\n                    this._ticker = ticker;\n                    if (ticker)\n                    {\n                        ticker.add(this.render, this, UPDATE_PRIORITY.LOW);\n                    }\n                },\n                get()\n                {\n                    return this._ticker;\n                },\n            });\n\n        /**\n         * Convenience method for stopping the render.\n         * @method\n         * @memberof PIXI.Application\n         * @instance\n         */\n        this.stop = (): void =>\n        {\n            this._ticker.stop();\n        };\n\n        /**\n         * Convenience method for starting the render.\n         * @method\n         * @memberof PIXI.Application\n         * @instance\n         */\n        this.start = (): void =>\n        {\n            this._ticker.start();\n        };\n\n        /**\n         * Internal reference to the ticker.\n         * @type {PIXI.Ticker}\n         * @name _ticker\n         * @memberof PIXI.Application#\n         * @private\n         */\n        this._ticker = null;\n\n        /**\n         * Ticker for doing render updates.\n         * @type {PIXI.Ticker}\n         * @name ticker\n         * @memberof PIXI.Application#\n         * @default PIXI.Ticker.shared\n         */\n        this.ticker = options.sharedTicker ? Ticker.shared : new Ticker();\n\n        // Start the rendering\n        if (options.autoStart)\n        {\n            this.start();\n        }\n    }\n\n    /**\n     * Clean up the ticker, scoped to application.\n     * @static\n     * @private\n     */\n    static destroy(): void\n    {\n        if (this._ticker)\n        {\n            const oldTicker = this._ticker;\n\n            this.ticker = null;\n            oldTicker.destroy();\n        }\n    }\n}\n\nextensions.add(TickerPlugin);\n"], "names": ["UPDATE_PRIORITY", "Ticker", "ExtensionType", "extensions"], "mappings": ";;AA+BO,MAAM,aACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeI,OAAO,KAAK,SACZ;AAEI,cAAU,OAAO,OAAO;AAAA,MACpB,WAAW;AAAA,MACX,cAAc;AAAA,IAAA,GACf,OAAO,GAGV,OAAO;AAAA,MAAe;AAAA,MAAM;AAAA,MACxB;AAAA,QACI,IAAI,QACJ;AACQ,eAAK,WAEL,KAAK,QAAQ,OAAO,KAAK,QAAQ,IAAI,GAEzC,KAAK,UAAU,QACX,UAEA,OAAO,IAAI,KAAK,QAAQ,MAAMA,uBAAgB,GAAG;AAAA,QAEzD;AAAA,QACA,MACA;AACI,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,IAAA,GAQJ,KAAK,OAAO,MACZ;AACI,WAAK,QAAQ;IAAK,GAStB,KAAK,QAAQ,MACb;AACI,WAAK,QAAQ;IAAM,GAUvB,KAAK,UAAU,MASf,KAAK,SAAS,QAAQ,eAAeC,OAAO,OAAA,SAAS,IAAIA,OAAAA,UAGrD,QAAQ,aAER,KAAK;EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UACP;AACI,QAAI,KAAK,SACT;AACI,YAAM,YAAY,KAAK;AAElB,WAAA,SAAS,MACd,UAAU,QAAQ;AAAA,IACtB;AAAA,EACJ;AACJ;AA3Ga,aAGF,YAA+BC,WAAc,cAAA;AA0GxDC,WAAAA,WAAW,IAAI,YAAY;;"}