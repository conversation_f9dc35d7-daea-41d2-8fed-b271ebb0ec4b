{"version": 3, "file": "TickerListener.mjs", "sources": ["../src/TickerListener.ts"], "sourcesContent": ["import type { TickerCallback } from './Ticker';\n\n/**\n * Internal class for handling the priority sorting of ticker handlers.\n * @private\n * @class\n * @memberof PIXI\n */\nexport class TickerListener<T = any>\n{\n    /** The current priority. */\n    public priority: number;\n    /** The next item in chain. */\n    public next: TickerListener = null;\n    /** The previous item in chain. */\n    public previous: TickerListener = null;\n\n    /** The handler function to execute. */\n    private fn: TickerCallback<T>;\n    /** The calling to execute. */\n    private context: T;\n    /** If this should only execute once. */\n    private once: boolean;\n    /** `true` if this listener has been destroyed already. */\n    private _destroyed = false;\n\n    /**\n     * Constructor\n     * @private\n     * @param fn - The listener function to be added for one update\n     * @param context - The listener context\n     * @param priority - The priority for emitting\n     * @param once - If the handler should fire once\n     */\n    constructor(fn: TickerCallback<T>, context: T = null, priority = 0, once = false)\n    {\n        this.fn = fn;\n        this.context = context;\n        this.priority = priority;\n        this.once = once;\n    }\n\n    /**\n     * Simple compare function to figure out if a function and context match.\n     * @private\n     * @param fn - The listener function to be added for one update\n     * @param context - The listener context\n     * @returns `true` if the listener match the arguments\n     */\n    match(fn: TickerCallback<T>, context: any = null): boolean\n    {\n        return this.fn === fn && this.context === context;\n    }\n\n    /**\n     * Emit by calling the current function.\n     * @private\n     * @param deltaTime - time since the last emit.\n     * @returns Next ticker\n     */\n    emit(deltaTime: number): TickerListener\n    {\n        if (this.fn)\n        {\n            if (this.context)\n            {\n                this.fn.call(this.context, deltaTime);\n            }\n            else\n            {\n                (this as TickerListener<any>).fn(deltaTime);\n            }\n        }\n\n        const redirect = this.next;\n\n        if (this.once)\n        {\n            this.destroy(true);\n        }\n\n        // Soft-destroying should remove\n        // the next reference\n        if (this._destroyed)\n        {\n            this.next = null;\n        }\n\n        return redirect;\n    }\n\n    /**\n     * Connect to the list.\n     * @private\n     * @param previous - Input node, previous listener\n     */\n    connect(previous: TickerListener): void\n    {\n        this.previous = previous;\n        if (previous.next)\n        {\n            previous.next.previous = this;\n        }\n        this.next = previous.next;\n        previous.next = this;\n    }\n\n    /**\n     * Destroy and don't use after this.\n     * @private\n     * @param hard - `true` to remove the `next` reference, this\n     *        is considered a hard destroy. Soft destroy maintains the next reference.\n     * @returns The listener to redirect while emitting or removing.\n     */\n    destroy(hard = false): TickerListener\n    {\n        this._destroyed = true;\n        this.fn = null;\n        this.context = null;\n\n        // Disconnect, hook up next and previous\n        if (this.previous)\n        {\n            this.previous.next = this.next;\n        }\n\n        if (this.next)\n        {\n            this.next.previous = this.previous;\n        }\n\n        // Redirect to the next item\n        const redirect = this.next;\n\n        // Remove references\n        this.next = hard ? null : redirect;\n        this.previous = null;\n\n        return redirect;\n    }\n}\n"], "names": [], "mappings": "AAQO,MAAM,eACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBI,YAAY,IAAuB,UAAa,MAAM,WAAW,GAAG,OAAO,IAC3E;AAtBA,SAAO,OAAuB,MAE9B,KAAO,WAA2B,MASlC,KAAQ,aAAa,IAYZ,KAAA,KAAK,IACV,KAAK,UAAU,SACf,KAAK,WAAW,UAChB,KAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,IAAuB,UAAe,MAC5C;AACI,WAAO,KAAK,OAAO,MAAM,KAAK,YAAY;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,WACL;AACQ,SAAK,OAED,KAAK,UAEL,KAAK,GAAG,KAAK,KAAK,SAAS,SAAS,IAInC,KAA6B,GAAG,SAAS;AAIlD,UAAM,WAAW,KAAK;AAElB,WAAA,KAAK,QAEL,KAAK,QAAQ,EAAI,GAKjB,KAAK,eAEL,KAAK,OAAO,OAGT;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,UACR;AACI,SAAK,WAAW,UACZ,SAAS,SAET,SAAS,KAAK,WAAW,OAE7B,KAAK,OAAO,SAAS,MACrB,SAAS,OAAO;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,OAAO,IACf;AACS,SAAA,aAAa,IAClB,KAAK,KAAK,MACV,KAAK,UAAU,MAGX,KAAK,aAEL,KAAK,SAAS,OAAO,KAAK,OAG1B,KAAK,SAEL,KAAK,KAAK,WAAW,KAAK;AAI9B,UAAM,WAAW,KAAK;AAGtB,WAAA,KAAK,OAAO,OAAO,OAAO,UAC1B,KAAK,WAAW,MAET;AAAA,EACX;AACJ;"}