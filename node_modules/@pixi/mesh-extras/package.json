{"name": "@pixi/mesh-extras", "version": "7.4.3", "main": "lib/index.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "description": "Custom Mesh display objects, like Rope and SimplePlane", "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "http://pixijs.com/", "bugs": "https://github.com/pixijs/pixijs/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pixijs/pixijs.git"}, "publishConfig": {"access": "public"}, "files": ["lib", "*.d.ts"], "peerDependencies": {"@pixi/core": "7.4.3", "@pixi/mesh": "7.4.3"}}