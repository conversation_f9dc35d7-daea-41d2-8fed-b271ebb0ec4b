{"version": 3, "file": "NineSlicePlane.mjs", "sources": ["../src/NineSlicePlane.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\nimport { SimplePlane } from './SimplePlane';\n\nimport type { ITypedArray } from '@pixi/core';\n\nconst DEFAULT_BORDER_SIZE = 10;\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface NineSlicePlane extends GlobalMixins.NineSlicePlane {}\n\n/**\n * The NineSlicePlane allows you to stretch a texture using 9-slice scaling. The corners will remain unscaled (useful\n * for buttons with rounded corners for example) and the other areas will be scaled horizontally and or vertically\n *\n * <pre>\n *      A                          B\n *    +---+----------------------+---+\n *  C | 1 |          2           | 3 |\n *    +---+----------------------+---+\n *    |   |                      |   |\n *    | 4 |          5           | 6 |\n *    |   |                      |   |\n *    +---+----------------------+---+\n *  D | 7 |          8           | 9 |\n *    +---+----------------------+---+\n *  When changing this objects width and/or height:\n *     areas 1 3 7 and 9 will remain unscaled.\n *     areas 2 and 8 will be stretched horizontally\n *     areas 4 and 6 will be stretched vertically\n *     area 5 will be stretched both horizontally and vertically\n * </pre>\n * @example\n * import { NineSlicePlane, Texture } from 'pixi.js';\n *\n * const plane9 = new NineSlicePlane(Texture.from('BoxWithRoundedCorners.png'), 15, 15, 15, 15);\n * @memberof PIXI\n */\nexport class NineSlicePlane extends SimplePlane\n{\n    private _origWidth: number;\n    private _origHeight: number;\n\n    /**\n     * The width of the left column (a).\n     * @private\n     */\n    _leftWidth: number;\n\n    /**\n     * The width of the right column (b)\n     * @private\n     */\n    _rightWidth: number;\n\n    /**\n     * The height of the top row (c)\n     * @private\n     */\n    _topHeight: number;\n\n    /**\n     * The height of the bottom row (d)\n     * @private\n     */\n    _bottomHeight: number;\n\n    /**\n     * @param texture - The texture to use on the NineSlicePlane.\n     * @param {number} [leftWidth=10] - size of the left vertical bar (A)\n     * @param {number} [topHeight=10] - size of the top horizontal bar (C)\n     * @param {number} [rightWidth=10] - size of the right vertical bar (B)\n     * @param {number} [bottomHeight=10] - size of the bottom horizontal bar (D)\n     */\n    constructor(\n        texture: Texture,\n        leftWidth?: number,\n        topHeight?: number,\n        rightWidth?: number,\n        bottomHeight?: number\n    )\n    {\n        super(Texture.WHITE, 4, 4);\n\n        this._origWidth = texture.orig.width;\n        this._origHeight = texture.orig.height;\n\n        /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n        this._width = this._origWidth;\n\n        /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n        this._height = this._origHeight;\n\n        this._leftWidth = leftWidth ?? texture.defaultBorders?.left ?? DEFAULT_BORDER_SIZE;\n        this._rightWidth = rightWidth ?? texture.defaultBorders?.right ?? DEFAULT_BORDER_SIZE;\n        this._topHeight = topHeight ?? texture.defaultBorders?.top ?? DEFAULT_BORDER_SIZE;\n        this._bottomHeight = bottomHeight ?? texture.defaultBorders?.bottom ?? DEFAULT_BORDER_SIZE;\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n    }\n\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n        this._refresh();\n    }\n\n    get vertices(): ITypedArray\n    {\n        return this.geometry.getBuffer('aVertexPosition').data;\n    }\n\n    set vertices(value: ITypedArray)\n    {\n        this.geometry.getBuffer('aVertexPosition').data = value;\n    }\n\n    /** Updates the horizontal vertices. */\n    public updateHorizontalVertices(): void\n    {\n        const vertices = this.vertices;\n\n        const scale = this._getMinScale();\n\n        vertices[9] = vertices[11] = vertices[13] = vertices[15] = this._topHeight * scale;\n        vertices[17] = vertices[19] = vertices[21] = vertices[23] = this._height - (this._bottomHeight * scale);\n        vertices[25] = vertices[27] = vertices[29] = vertices[31] = this._height;\n    }\n\n    /** Updates the vertical vertices. */\n    public updateVerticalVertices(): void\n    {\n        const vertices = this.vertices;\n\n        const scale = this._getMinScale();\n\n        vertices[2] = vertices[10] = vertices[18] = vertices[26] = this._leftWidth * scale;\n        vertices[4] = vertices[12] = vertices[20] = vertices[28] = this._width - (this._rightWidth * scale);\n        vertices[6] = vertices[14] = vertices[22] = vertices[30] = this._width;\n    }\n\n    /**\n     * Returns the smaller of a set of vertical and horizontal scale of nine slice corners.\n     * @returns Smaller number of vertical and horizontal scale.\n     */\n    private _getMinScale(): number\n    {\n        const w = this._leftWidth + this._rightWidth;\n        const scaleW = this._width > w ? 1.0 : this._width / w;\n\n        const h = this._topHeight + this._bottomHeight;\n        const scaleH = this._height > h ? 1.0 : this._height / h;\n\n        const scale = Math.min(scaleW, scaleH);\n\n        return scale;\n    }\n\n    /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n        this._refresh();\n    }\n\n    /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n        this._refresh();\n    }\n\n    /** The width of the left column. */\n    get leftWidth(): number\n    {\n        return this._leftWidth;\n    }\n\n    set leftWidth(value: number)\n    {\n        this._leftWidth = value;\n        this._refresh();\n    }\n\n    /** The width of the right column. */\n    get rightWidth(): number\n    {\n        return this._rightWidth;\n    }\n\n    set rightWidth(value: number)\n    {\n        this._rightWidth = value;\n        this._refresh();\n    }\n\n    /** The height of the top row. */\n    get topHeight(): number\n    {\n        return this._topHeight;\n    }\n\n    set topHeight(value: number)\n    {\n        this._topHeight = value;\n        this._refresh();\n    }\n\n    /** The height of the bottom row. */\n    get bottomHeight(): number\n    {\n        return this._bottomHeight;\n    }\n\n    set bottomHeight(value: number)\n    {\n        this._bottomHeight = value;\n        this._refresh();\n    }\n\n    /** Refreshes NineSlicePlane coords. All of them. */\n    private _refresh(): void\n    {\n        const texture = this.texture;\n\n        const uvs = this.geometry.buffers[1].data;\n\n        this._origWidth = texture.orig.width;\n        this._origHeight = texture.orig.height;\n\n        const _uvw = 1.0 / this._origWidth;\n        const _uvh = 1.0 / this._origHeight;\n\n        uvs[0] = uvs[8] = uvs[16] = uvs[24] = 0;\n        uvs[1] = uvs[3] = uvs[5] = uvs[7] = 0;\n        uvs[6] = uvs[14] = uvs[22] = uvs[30] = 1;\n        uvs[25] = uvs[27] = uvs[29] = uvs[31] = 1;\n\n        uvs[2] = uvs[10] = uvs[18] = uvs[26] = _uvw * this._leftWidth;\n        uvs[4] = uvs[12] = uvs[20] = uvs[28] = 1 - (_uvw * this._rightWidth);\n        uvs[9] = uvs[11] = uvs[13] = uvs[15] = _uvh * this._topHeight;\n        uvs[17] = uvs[19] = uvs[21] = uvs[23] = 1 - (_uvh * this._bottomHeight);\n\n        this.updateHorizontalVertices();\n        this.updateVerticalVertices();\n\n        this.geometry.buffers[0].update();\n        this.geometry.buffers[1].update();\n    }\n}\n"], "names": [], "mappings": ";;AAKA,MAAM,sBAAsB;AAgCrB,MAAM,uBAAuB,YACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCI,YACI,SACA,WACA,WACA,YACA,cAEJ;AACU,UAAA,QAAQ,OAAO,GAAG,CAAC,GAEpB,KAAA,aAAa,QAAQ,KAAK,OAC/B,KAAK,cAAc,QAAQ,KAAK,QAGhC,KAAK,SAAS,KAAK,YAGnB,KAAK,UAAU,KAAK,aAEpB,KAAK,aAAa,aAAa,QAAQ,gBAAgB,QAAQ,qBAC/D,KAAK,cAAc,cAAc,QAAQ,gBAAgB,SAAS,qBAClE,KAAK,aAAa,aAAa,QAAQ,gBAAgB,OAAO,qBAC9D,KAAK,gBAAgB,gBAAgB,QAAQ,gBAAgB,UAAU,qBAGvE,KAAK,UAAU;AAAA,EACnB;AAAA,EAEO,iBACP;AACI,SAAK,aAAa,KAAK,OAAO,QAAQ,WACtC,KAAK;EACT;AAAA,EAEA,IAAI,WACJ;AACI,WAAO,KAAK,SAAS,UAAU,iBAAiB,EAAE;AAAA,EACtD;AAAA,EAEA,IAAI,SAAS,OACb;AACI,SAAK,SAAS,UAAU,iBAAiB,EAAE,OAAO;AAAA,EACtD;AAAA;AAAA,EAGO,2BACP;AACI,UAAM,WAAW,KAAK,UAEhB,QAAQ,KAAK;AAEnB,aAAS,CAAC,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,KAAK,aAAa,OAC7E,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,KAAK,UAAW,KAAK,gBAAgB,OACjG,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,KAAK;AAAA,EACrE;AAAA;AAAA,EAGO,yBACP;AACI,UAAM,WAAW,KAAK,UAEhB,QAAQ,KAAK;AAEnB,aAAS,CAAC,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,KAAK,aAAa,OAC7E,SAAS,CAAC,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,KAAK,SAAU,KAAK,cAAc,OAC7F,SAAS,CAAC,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,KAAK;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,eACR;AACU,UAAA,IAAI,KAAK,aAAa,KAAK,aAC3B,SAAS,KAAK,SAAS,IAAI,IAAM,KAAK,SAAS,GAE/C,IAAI,KAAK,aAAa,KAAK,eAC3B,SAAS,KAAK,UAAU,IAAI,IAAM,KAAK,UAAU;AAEzC,WAAA,KAAK,IAAI,QAAQ,MAAM;AAAA,EAGzC;AAAA;AAAA,EAGA,IAAI,QACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,MAAM,OACV;AACS,SAAA,SAAS,OACd,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,SACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,OAAO,OACX;AACS,SAAA,UAAU,OACf,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,YACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,UAAU,OACd;AACS,SAAA,aAAa,OAClB,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,aACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,WAAW,OACf;AACS,SAAA,cAAc,OACnB,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,YACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,UAAU,OACd;AACS,SAAA,aAAa,OAClB,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,eACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,aAAa,OACjB;AACS,SAAA,gBAAgB,OACrB,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGQ,WACR;AACU,UAAA,UAAU,KAAK,SAEf,MAAM,KAAK,SAAS,QAAQ,CAAC,EAAE;AAErC,SAAK,aAAa,QAAQ,KAAK,OAC/B,KAAK,cAAc,QAAQ,KAAK;AAEhC,UAAM,OAAO,IAAM,KAAK,YAClB,OAAO,IAAM,KAAK;AAExB,QAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,GACtC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GACpC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,GACvC,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,GAExC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,OAAO,KAAK,YACnD,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAK,OAAO,KAAK,aACxD,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,OAAO,KAAK,YACnD,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAK,OAAO,KAAK,eAEzD,KAAK,yBAAyB,GAC9B,KAAK,uBAEL,GAAA,KAAK,SAAS,QAAQ,CAAC,EAAE,UACzB,KAAK,SAAS,QAAQ,CAAC,EAAE,OAAO;AAAA,EACpC;AACJ;"}