{"version": 3, "file": "SimpleMesh.mjs", "sources": ["../src/SimpleMesh.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\nimport { Mesh, MeshGeometry, MeshMaterial } from '@pixi/mesh';\n\nimport type { DRAW_MODES, IArrayBuffer, ITypedArray, Renderer } from '@pixi/core';\n\n/**\n * The Simple Mesh class mimics Mesh in PixiJS v4, providing easy-to-use constructor arguments.\n * For more robust customization, use {@link PIXI.Mesh}.\n * @memberof PIXI\n */\nexport class SimpleMesh extends Mesh\n{\n    /** Upload vertices buffer each frame. */\n    public autoUpdate: boolean;\n\n    /**\n     * @param texture - The texture to use\n     * @param {Float32Array} [vertices] - if you want to specify the vertices\n     * @param {Float32Array} [uvs] - if you want to specify the uvs\n     * @param {Uint16Array} [indices] - if you want to specify the indices\n     * @param drawMode - the drawMode, can be any of the Mesh.DRAW_MODES consts\n     */\n    constructor(\n        texture: Texture = Texture.EMPTY,\n        vertices?: IArrayBuffer,\n        uvs?: IArrayBuffer,\n        indices?: IA<PERSON>yBuffer,\n        drawMode?: DRAW_MODES\n    )\n    {\n        const geometry = new MeshGeometry(vertices, uvs, indices);\n\n        geometry.getBuffer('aVertexPosition').static = false;\n\n        const meshMaterial = new MeshMaterial(texture);\n\n        super(geometry, meshMaterial, null, drawMode);\n\n        this.autoUpdate = true;\n    }\n\n    /**\n     * Collection of vertices data.\n     * @type {Float32Array}\n     */\n    get vertices(): ITypedArray\n    {\n        return this.geometry.getBuffer('aVertexPosition').data;\n    }\n    set vertices(value: ITypedArray)\n    {\n        this.geometry.getBuffer('aVertexPosition').data = value;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this.autoUpdate)\n        {\n            this.geometry.getBuffer('aVertexPosition').update();\n        }\n\n        super._render(renderer);\n    }\n}\n"], "names": [], "mappings": ";;AAUO,MAAM,mBAAmB,KAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWI,YACI,UAAmB,QAAQ,OAC3B,UACA,KACA,SACA,UAEJ;AACI,UAAM,WAAW,IAAI,aAAa,UAAU,KAAK,OAAO;AAE/C,aAAA,UAAU,iBAAiB,EAAE,SAAS;AAEzC,UAAA,eAAe,IAAI,aAAa,OAAO;AAEvC,UAAA,UAAU,cAAc,MAAM,QAAQ,GAE5C,KAAK,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WACJ;AACI,WAAO,KAAK,SAAS,UAAU,iBAAiB,EAAE;AAAA,EACtD;AAAA,EACA,IAAI,SAAS,OACb;AACI,SAAK,SAAS,UAAU,iBAAiB,EAAE,OAAO;AAAA,EACtD;AAAA,EAEA,QAAQ,UACR;AACQ,SAAK,cAEL,KAAK,SAAS,UAAU,iBAAiB,EAAE,UAG/C,MAAM,QAAQ,QAAQ;AAAA,EAC1B;AACJ;"}