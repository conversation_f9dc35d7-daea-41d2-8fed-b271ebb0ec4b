{"version": 3, "file": "PlaneGeometry.js", "sources": ["../../src/geometry/PlaneGeometry.ts"], "sourcesContent": ["import { MeshGeometry } from '@pixi/mesh';\n\n/**\n * @memberof PIXI\n */\nexport class PlaneGeometry extends MeshGeometry\n{\n    public segWidth: number;\n    public segHeight: number;\n    public width: number;\n    public height: number;\n\n    /**\n     * @param width - The width of the plane.\n     * @param height - The height of the plane.\n     * @param segWidth - Number of horizontal segments.\n     * @param segHeight - Number of vertical segments.\n     */\n    constructor(width = 100, height = 100, segWidth = 10, segHeight = 10)\n    {\n        super();\n\n        this.segWidth = segWidth;\n        this.segHeight = segHeight;\n\n        this.width = width;\n        this.height = height;\n\n        this.build();\n    }\n\n    /**\n     * Refreshes plane coordinates\n     * @private\n     */\n    build(): void\n    {\n        const total = this.segWidth * this.segHeight;\n        const verts = [];\n        const uvs = [];\n        const indices = [];\n\n        const segmentsX = this.segWidth - 1;\n        const segmentsY = this.segHeight - 1;\n\n        const sizeX = (this.width) / segmentsX;\n        const sizeY = (this.height) / segmentsY;\n\n        for (let i = 0; i < total; i++)\n        {\n            const x = (i % this.segWidth);\n            const y = ((i / this.segWidth) | 0);\n\n            verts.push(x * sizeX, y * sizeY);\n            uvs.push(x / segmentsX, y / segmentsY);\n        }\n\n        const totalSub = segmentsX * segmentsY;\n\n        for (let i = 0; i < totalSub; i++)\n        {\n            const xpos = i % segmentsX;\n            const ypos = (i / segmentsX) | 0;\n\n            const value = (ypos * this.segWidth) + xpos;\n            const value2 = (ypos * this.segWidth) + xpos + 1;\n            const value3 = ((ypos + 1) * this.segWidth) + xpos;\n            const value4 = ((ypos + 1) * this.segWidth) + xpos + 1;\n\n            indices.push(value, value2, value3,\n                value2, value4, value3);\n        }\n\n        this.buffers[0].data = new Float32Array(verts);\n        this.buffers[1].data = new Float32Array(uvs);\n        this.indexBuffer.data = new Uint16Array(indices);\n\n        // ensure that the changes are uploaded\n        this.buffers[0].update();\n        this.buffers[1].update();\n        this.indexBuffer.update();\n    }\n}\n"], "names": ["MeshGeometry"], "mappings": ";;AAKO,MAAM,sBAAsBA,KAAAA,aACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYI,YAAY,QAAQ,KAAK,SAAS,KAAK,WAAW,IAAI,YAAY,IAClE;AACU,aAEN,KAAK,WAAW,UAChB,KAAK,YAAY,WAEjB,KAAK,QAAQ,OACb,KAAK,SAAS,QAEd,KAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACA;AACI,UAAM,QAAQ,KAAK,WAAW,KAAK,WAC7B,QAAQ,CAAC,GACT,MAAM,CACN,GAAA,UAAU,CAAC,GAEX,YAAY,KAAK,WAAW,GAC5B,YAAY,KAAK,YAAY,GAE7B,QAAS,KAAK,QAAS,WACvB,QAAS,KAAK,SAAU;AAE9B,aAAS,IAAI,GAAG,IAAI,OAAO,KAC3B;AACI,YAAM,IAAK,IAAI,KAAK,UACd,IAAM,IAAI,KAAK,WAAY;AAE3B,YAAA,KAAK,IAAI,OAAO,IAAI,KAAK,GAC/B,IAAI,KAAK,IAAI,WAAW,IAAI,SAAS;AAAA,IACzC;AAEA,UAAM,WAAW,YAAY;AAE7B,aAAS,IAAI,GAAG,IAAI,UAAU,KAC9B;AACI,YAAM,OAAO,IAAI,WACX,OAAQ,IAAI,YAAa,GAEzB,QAAS,OAAO,KAAK,WAAY,MACjC,SAAU,OAAO,KAAK,WAAY,OAAO,GACzC,UAAW,OAAO,KAAK,KAAK,WAAY,MACxC,UAAW,OAAO,KAAK,KAAK,WAAY,OAAO;AAE7C,cAAA;AAAA,QAAK;AAAA,QAAO;AAAA,QAAQ;AAAA,QACxB;AAAA,QAAQ;AAAA,QAAQ;AAAA,MAAA;AAAA,IACxB;AAEA,SAAK,QAAQ,CAAC,EAAE,OAAO,IAAI,aAAa,KAAK,GAC7C,KAAK,QAAQ,CAAC,EAAE,OAAO,IAAI,aAAa,GAAG,GAC3C,KAAK,YAAY,OAAO,IAAI,YAAY,OAAO,GAG/C,KAAK,QAAQ,CAAC,EAAE,OAChB,GAAA,KAAK,QAAQ,CAAC,EAAE,UAChB,KAAK,YAAY;EACrB;AACJ;;"}