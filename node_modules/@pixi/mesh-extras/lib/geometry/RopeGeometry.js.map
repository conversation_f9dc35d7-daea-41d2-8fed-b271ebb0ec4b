{"version": 3, "file": "RopeGeometry.js", "sources": ["../../src/geometry/RopeGeometry.ts"], "sourcesContent": ["import { MeshGeometry } from '@pixi/mesh';\n\nimport type { IPoint } from '@pixi/core';\n\n/**\n * RopeGeometry allows you to draw a geometry across several points and then manipulate these points.\n * @example\n * import { Point, RopeGeometry } from 'pixi.js';\n *\n * for (let i = 0; i < 20; i++) {\n *     points.push(new Point(i * 50, 0));\n * };\n * const rope = new RopeGeometry(100, points);\n * @memberof PIXI\n */\nexport class RopeGeometry extends MeshGeometry\n{\n    /** An array of points that determine the rope. */\n    public points: IPoint[];\n\n    /** Rope texture scale, if zero then the rope texture is stretched. */\n    public readonly textureScale: number;\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    _width: number;\n\n    /**\n     * @param width - The width (i.e., thickness) of the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param textureScale - By default the rope texture will be stretched to match\n     *     rope length. If textureScale is positive this value will be treated as a scaling\n     *     factor and the texture will preserve its aspect ratio instead. To create a tiling rope\n     *     set baseTexture.wrapMode to {@link PIXI.WRAP_MODES.REPEAT} and use a power of two texture,\n     *     then set textureScale=1 to keep the original texture pixel size.\n     *     In order to reduce alpha channel artifacts provide a larger texture and downsample -\n     *     i.e. set textureScale=0.5 to scale it down twice.\n     */\n    constructor(width = 200, points: IPoint[], textureScale = 0)\n    {\n        super(new Float32Array(points.length * 4),\n            new Float32Array(points.length * 4),\n            new Uint16Array((points.length - 1) * 6));\n\n        this.points = points;\n        this._width = width;\n        this.textureScale = textureScale;\n\n        this.build();\n    }\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    /** Refreshes Rope indices and uvs */\n    private build(): void\n    {\n        const points = this.points;\n\n        if (!points) return;\n\n        const vertexBuffer = this.getBuffer('aVertexPosition');\n        const uvBuffer = this.getBuffer('aTextureCoord');\n        const indexBuffer = this.getIndex();\n\n        // if too little points, or texture hasn't got UVs set yet just move on.\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        // if the number of points has changed we will need to recreate the arraybuffers\n        if (vertexBuffer.data.length / 4 !== points.length)\n        {\n            vertexBuffer.data = new Float32Array(points.length * 4);\n            uvBuffer.data = new Float32Array(points.length * 4);\n            indexBuffer.data = new Uint16Array((points.length - 1) * 6);\n        }\n\n        const uvs = uvBuffer.data;\n        const indices = indexBuffer.data;\n\n        uvs[0] = 0;\n        uvs[1] = 0;\n        uvs[2] = 0;\n        uvs[3] = 1;\n\n        let amount = 0;\n        let prev = points[0];\n        const textureWidth = this._width * this.textureScale;\n        const total = points.length; // - 1;\n\n        for (let i = 0; i < total; i++)\n        {\n            // time to do some smart drawing!\n            const index = i * 4;\n\n            if (this.textureScale > 0)\n            {\n                // calculate pixel distance from previous point\n                const dx = prev.x - points[i].x;\n                const dy = prev.y - points[i].y;\n                const distance = Math.sqrt((dx * dx) + (dy * dy));\n\n                prev = points[i];\n                amount += distance / textureWidth;\n            }\n            else\n            {\n                // stretch texture\n                amount = i / (total - 1);\n            }\n\n            uvs[index] = amount;\n            uvs[index + 1] = 0;\n\n            uvs[index + 2] = amount;\n            uvs[index + 3] = 1;\n        }\n\n        let indexCount = 0;\n\n        for (let i = 0; i < total - 1; i++)\n        {\n            const index = i * 2;\n\n            indices[indexCount++] = index;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 2;\n\n            indices[indexCount++] = index + 2;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 3;\n        }\n\n        // ensure that the changes are uploaded\n        uvBuffer.update();\n        indexBuffer.update();\n\n        this.updateVertices();\n    }\n\n    /** refreshes vertices of Rope mesh */\n    public updateVertices(): void\n    {\n        const points = this.points;\n\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        let lastPoint = points[0];\n        let nextPoint;\n        let perpX = 0;\n        let perpY = 0;\n\n        const vertices = this.buffers[0].data;\n        const total = points.length;\n        const halfWidth = this.textureScale > 0 ? this.textureScale * this._width / 2 : this._width / 2;\n\n        for (let i = 0; i < total; i++)\n        {\n            const point = points[i];\n            const index = i * 4;\n\n            if (i < points.length - 1)\n            {\n                nextPoint = points[i + 1];\n            }\n            else\n            {\n                nextPoint = point;\n            }\n\n            perpY = -(nextPoint.x - lastPoint.x);\n            perpX = nextPoint.y - lastPoint.y;\n\n            let ratio = (1 - (i / (total - 1))) * 10;\n\n            if (ratio > 1)\n            {\n                ratio = 1;\n            }\n\n            const perpLength = Math.sqrt((perpX * perpX) + (perpY * perpY));\n\n            if (perpLength < 1e-6)\n            {\n                perpX = 0;\n                perpY = 0;\n            }\n            else\n            {\n                perpX /= perpLength;\n                perpY /= perpLength;\n\n                perpX *= halfWidth;\n                perpY *= halfWidth;\n            }\n\n            vertices[index] = point.x + perpX;\n            vertices[index + 1] = point.y + perpY;\n            vertices[index + 2] = point.x - perpX;\n            vertices[index + 3] = point.y - perpY;\n\n            lastPoint = point;\n        }\n\n        this.buffers[0].update();\n    }\n\n    public update(): void\n    {\n        if (this.textureScale > 0)\n        {\n            this.build(); // we need to update UVs\n        }\n        else\n        {\n            this.updateVertices();\n        }\n    }\n}\n"], "names": ["MeshGeometry"], "mappings": ";;AAeO,MAAM,qBAAqBA,KAAAA,aAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBI,YAAY,QAAQ,KAAK,QAAkB,eAAe,GAC1D;AACI;AAAA,MAAM,IAAI,aAAa,OAAO,SAAS,CAAC;AAAA,MACpC,IAAI,aAAa,OAAO,SAAS,CAAC;AAAA,MAClC,IAAI,aAAa,OAAO,SAAS,KAAK,CAAC;AAAA,IAAA,GAEtC,KAAA,SAAS,QACd,KAAK,SAAS,OACd,KAAK,eAAe,cAEpB,KAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAGQ,QACR;AACI,UAAM,SAAS,KAAK;AAEpB,QAAI,CAAC;AAAQ;AAEb,UAAM,eAAe,KAAK,UAAU,iBAAiB,GAC/C,WAAW,KAAK,UAAU,eAAe,GACzC,cAAc,KAAK,SAAS;AAGlC,QAAI,OAAO,SAAS;AAEhB;AAIA,iBAAa,KAAK,SAAS,MAAM,OAAO,WAExC,aAAa,OAAO,IAAI,aAAa,OAAO,SAAS,CAAC,GACtD,SAAS,OAAO,IAAI,aAAa,OAAO,SAAS,CAAC,GAClD,YAAY,OAAO,IAAI,aAAa,OAAO,SAAS,KAAK,CAAC;AAG9D,UAAM,MAAM,SAAS,MACf,UAAU,YAAY;AAE5B,QAAI,CAAC,IAAI,GACT,IAAI,CAAC,IAAI,GACT,IAAI,CAAC,IAAI,GACT,IAAI,CAAC,IAAI;AAET,QAAI,SAAS,GACT,OAAO,OAAO,CAAC;AACnB,UAAM,eAAe,KAAK,SAAS,KAAK,cAClC,QAAQ,OAAO;AAErB,aAAS,IAAI,GAAG,IAAI,OAAO,KAC3B;AAEI,YAAM,QAAQ,IAAI;AAEd,UAAA,KAAK,eAAe,GACxB;AAEU,cAAA,KAAK,KAAK,IAAI,OAAO,CAAC,EAAE,GACxB,KAAK,KAAK,IAAI,OAAO,CAAC,EAAE,GACxB,WAAW,KAAK,KAAM,KAAK,KAAO,KAAK,EAAG;AAEhD,eAAO,OAAO,CAAC,GACf,UAAU,WAAW;AAAA,MACzB;AAII,iBAAS,KAAK,QAAQ;AAG1B,UAAI,KAAK,IAAI,QACb,IAAI,QAAQ,CAAC,IAAI,GAEjB,IAAI,QAAQ,CAAC,IAAI,QACjB,IAAI,QAAQ,CAAC,IAAI;AAAA,IACrB;AAEA,QAAI,aAAa;AAEjB,aAAS,IAAI,GAAG,IAAI,QAAQ,GAAG,KAC/B;AACI,YAAM,QAAQ,IAAI;AAEV,cAAA,YAAY,IAAI,OACxB,QAAQ,YAAY,IAAI,QAAQ,GAChC,QAAQ,YAAY,IAAI,QAAQ,GAEhC,QAAQ,YAAY,IAAI,QAAQ,GAChC,QAAQ,YAAY,IAAI,QAAQ,GAChC,QAAQ,YAAY,IAAI,QAAQ;AAAA,IACpC;AAGA,aAAS,UACT,YAAY,OAAO,GAEnB,KAAK;EACT;AAAA;AAAA,EAGO,iBACP;AACI,UAAM,SAAS,KAAK;AAEpB,QAAI,OAAO,SAAS;AAEhB;AAGJ,QAAI,YAAY,OAAO,CAAC,GACpB,WACA,QAAQ,GACR,QAAQ;AAEZ,UAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,MAC3B,QAAQ,OAAO,QACf,YAAY,KAAK,eAAe,IAAI,KAAK,eAAe,KAAK,SAAS,IAAI,KAAK,SAAS;AAE9F,aAAS,IAAI,GAAG,IAAI,OAAO,KAC3B;AACI,YAAM,QAAQ,OAAO,CAAC,GAChB,QAAQ,IAAI;AAEd,UAAI,OAAO,SAAS,IAEpB,YAAY,OAAO,IAAI,CAAC,IAIxB,YAAY,OAGhB,QAAQ,EAAE,UAAU,IAAI,UAAU,IAClC,QAAQ,UAAU,IAAI,UAAU;AAEhC,UAAI,SAAS,IAAK,KAAK,QAAQ,MAAO;AAElC,cAAQ,MAER,QAAQ;AAGZ,YAAM,aAAa,KAAK,KAAM,QAAQ,QAAU,QAAQ,KAAM;AAE1D,mBAAa,QAEb,QAAQ,GACR,QAAQ,MAIR,SAAS,YACT,SAAS,YAET,SAAS,WACT,SAAS,YAGb,SAAS,KAAK,IAAI,MAAM,IAAI,OAC5B,SAAS,QAAQ,CAAC,IAAI,MAAM,IAAI,OAChC,SAAS,QAAQ,CAAC,IAAI,MAAM,IAAI,OAChC,SAAS,QAAQ,CAAC,IAAI,MAAM,IAAI,OAEhC,YAAY;AAAA,IAChB;AAEK,SAAA,QAAQ,CAAC,EAAE,OAAO;AAAA,EAC3B;AAAA,EAEO,SACP;AACQ,SAAK,eAAe,IAEpB,KAAK,UAIL,KAAK;EAEb;AACJ;;"}