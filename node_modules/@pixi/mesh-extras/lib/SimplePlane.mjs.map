{"version": 3, "file": "SimplePlane.mjs", "sources": ["../src/SimplePlane.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\nimport { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { PlaneGeometry } from './geometry/PlaneGeometry';\n\nimport type{ Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * The SimplePlane allows you to draw a texture across several points and then manipulate these points\n * @example\n * import { Point, SimplePlane, Texture } from 'pixi.js';\n *\n * for (let i = 0; i < 20; i++) {\n *     points.push(new Point(i * 50, 0));\n * }\n * const SimplePlane = new SimplePlane(Texture.from('snake.png'), points);\n * @memberof PIXI\n */\nexport class SimplePlane extends Mesh\n{\n    /** The geometry is automatically updated when the texture size changes. */\n    public autoResize: boolean;\n\n    protected _textureID: number;\n\n    /**\n     * @param texture - The texture to use on the SimplePlane.\n     * @param verticesX - The number of vertices in the x-axis\n     * @param verticesY - The number of vertices in the y-axis\n     */\n    constructor(texture: Texture, verticesX?: number, verticesY?: number)\n    {\n        const planeGeometry = new PlaneGeometry(texture.width, texture.height, verticesX, verticesY);\n        const meshMaterial = new MeshMaterial(Texture.WHITE);\n\n        super(planeGeometry, meshMaterial);\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n        this.autoResize = true;\n    }\n\n    /**\n     * Method used for overrides, to do something in case texture frame was changed.\n     * Meshes based on plane can override it and change more details based on texture.\n     */\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n\n        const geometry: PlaneGeometry = this.geometry as any;\n        const { width, height } = this.shader.texture;\n\n        if (this.autoResize && (geometry.width !== width || geometry.height !== height))\n        {\n            geometry.width = this.shader.texture.width;\n            geometry.height = this.shader.texture.height;\n            geometry.build();\n        }\n    }\n\n    set texture(value: Texture)\n    {\n        // Track texture same way sprite does.\n        // For generated meshes like NineSlicePlane it can change the geometry.\n        // Unfortunately, this method might not work if you directly change texture in material.\n\n        if (this.shader.texture === value)\n        {\n            return;\n        }\n\n        this.shader.texture = value;\n        this._textureID = -1;\n\n        if (value.baseTexture.valid)\n        {\n            this.textureUpdated();\n        }\n        else\n        {\n            value.once('update', this.textureUpdated, this);\n        }\n    }\n\n    get texture(): Texture\n    {\n        return this.shader.texture;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this._textureID !== this.shader.texture._updateID)\n        {\n            this.textureUpdated();\n        }\n\n        super._render(renderer);\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.shader.texture.off('update', this.textureUpdated, this);\n        super.destroy(options);\n    }\n}\n"], "names": [], "mappings": ";;;AAkBO,MAAM,oBAAoB,KACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWI,YAAY,SAAkB,WAAoB,WAClD;AACI,UAAM,gBAAgB,IAAI,cAAc,QAAQ,OAAO,QAAQ,QAAQ,WAAW,SAAS,GACrF,eAAe,IAAI,aAAa,QAAQ,KAAK;AAEnD,UAAM,eAAe,YAAY,GAG5B,KAAA,UAAU,SACf,KAAK,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,iBACP;AACS,SAAA,aAAa,KAAK,OAAO,QAAQ;AAEhC,UAAA,WAA0B,KAAK,UAC/B,EAAE,OAAO,WAAW,KAAK,OAAO;AAElC,SAAK,eAAe,SAAS,UAAU,SAAS,SAAS,WAAW,YAEpE,SAAS,QAAQ,KAAK,OAAO,QAAQ,OACrC,SAAS,SAAS,KAAK,OAAO,QAAQ,QACtC,SAAS,MAAM;AAAA,EAEvB;AAAA,EAEA,IAAI,QAAQ,OACZ;AAKQ,SAAK,OAAO,YAAY,UAK5B,KAAK,OAAO,UAAU,OACtB,KAAK,aAAa,IAEd,MAAM,YAAY,QAElB,KAAK,eAAe,IAIpB,MAAM,KAAK,UAAU,KAAK,gBAAgB,IAAI;AAAA,EAEtD;AAAA,EAEA,IAAI,UACJ;AACI,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EAEA,QAAQ,UACR;AACQ,SAAK,eAAe,KAAK,OAAO,QAAQ,aAExC,KAAK,eAAe,GAGxB,MAAM,QAAQ,QAAQ;AAAA,EAC1B;AAAA,EAEO,QAAQ,SACf;AACS,SAAA,OAAO,QAAQ,IAAI,UAAU,KAAK,gBAAgB,IAAI,GAC3D,MAAM,QAAQ,OAAO;AAAA,EACzB;AACJ;"}