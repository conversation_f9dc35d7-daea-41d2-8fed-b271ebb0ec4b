{"version": 3, "file": "SimpleRope.mjs", "sources": ["../src/SimpleRope.ts"], "sourcesContent": ["import { WRAP_MODES } from '@pixi/core';\nimport { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { RopeGeometry } from './geometry/RopeGeometry';\n\nimport type { IPoint, Renderer, Texture } from '@pixi/core';\n\n/**\n * The rope allows you to draw a texture across several points and then manipulate these points\n * @example\n * import { Point, SimpleRope, Texture } from 'pixi.js';\n *\n * for (let i = 0; i < 20; i++) {\n *     points.push(new Point(i * 50, 0));\n * };\n * const rope = new SimpleRope(Texture.from('snake.png'), points);\n * @memberof PIXI\n */\nexport class SimpleRope extends Mesh\n{\n    public autoUpdate: boolean;\n\n    /**\n     * Note: The wrap mode of the texture is set to REPEAT if `textureScale` is positive.\n     * @param texture - The texture to use on the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param {number} textureScale - Optional. Positive values scale rope texture\n     * keeping its aspect ratio. You can reduce alpha channel artifacts by providing a larger texture\n     * and downsampling here. If set to zero, texture will be stretched instead.\n     */\n    constructor(texture: Texture, points: IPoint[], textureScale = 0)\n    {\n        const ropeGeometry = new RopeGeometry(texture.height, points, textureScale);\n        const meshMaterial = new MeshMaterial(texture);\n\n        if (textureScale > 0)\n        {\n            // attempt to set UV wrapping, will fail on non-power of two textures\n            texture.baseTexture.wrapMode = WRAP_MODES.REPEAT;\n        }\n        super(ropeGeometry, meshMaterial);\n\n        /**\n         * re-calculate vertices by rope points each frame\n         * @member {boolean}\n         */\n        this.autoUpdate = true;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        const geometry: RopeGeometry = this.geometry as any;\n\n        if (this.autoUpdate || geometry._width !== this.shader.texture.height)\n        {\n            geometry._width = this.shader.texture.height;\n            geometry.update();\n        }\n\n        super._render(renderer);\n    }\n}\n"], "names": [], "mappings": ";;;AAiBO,MAAM,mBAAmB,KAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWI,YAAY,SAAkB,QAAkB,eAAe,GAC/D;AACU,UAAA,eAAe,IAAI,aAAa,QAAQ,QAAQ,QAAQ,YAAY,GACpE,eAAe,IAAI,aAAa,OAAO;AAEzC,mBAAe,MAGf,QAAQ,YAAY,WAAW,WAAW,SAE9C,MAAM,cAAc,YAAY,GAMhC,KAAK,aAAa;AAAA,EACtB;AAAA,EAEA,QAAQ,UACR;AACI,UAAM,WAAyB,KAAK;AAEpC,KAAI,KAAK,cAAc,SAAS,WAAW,KAAK,OAAO,QAAQ,YAE3D,SAAS,SAAS,KAAK,OAAO,QAAQ,QACtC,SAAS,OAGb,IAAA,MAAM,QAAQ,QAAQ;AAAA,EAC1B;AACJ;"}