{"version": 3, "file": "MeshMaterial.js", "sources": ["../src/MeshMaterial.ts"], "sourcesContent": ["import { Color, Matrix, Program, Shader, TextureMatrix } from '@pixi/core';\nimport fragment from './shader/mesh.frag';\nimport vertex from './shader/mesh.vert';\n\nimport type { ColorSource, Texture, utils } from '@pixi/core';\n\nexport interface IMeshMaterialOptions\n{\n    alpha?: number;\n    tint?: ColorSource;\n    pluginName?: string;\n    program?: Program;\n    uniforms?: utils.Dict<unknown>;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface MeshMaterial extends GlobalMixins.MeshMaterial {}\n\n/**\n * Slightly opinionated default shader for PixiJS 2D objects.\n * @memberof PIXI\n */\nexport class MeshMaterial extends Shader\n{\n    /**\n     * TextureMatrix instance for this Mesh, used to track Texture changes.\n     * @readonly\n     */\n    public readonly uvMatrix: TextureMatrix;\n\n    /**\n     * `true` if shader can be batch with the renderer's batch system.\n     * @default true\n     */\n    public batchable: boolean;\n\n    /**\n     * Renderer plugin for batching.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    // Internal-only properties\n    _tintRGB: number;\n\n    /**\n     * Only do update if tint or alpha changes.\n     * @private\n     * @default false\n     */\n    private _colorDirty: boolean;\n    private _alpha: number;\n    private _tintColor: Color;\n\n    /**\n     * @param uSampler - Texture that material uses to render.\n     * @param options - Additional options\n     * @param {number} [options.alpha=1] - Default alpha.\n     * @param {PIXI.ColorSource} [options.tint=0xFFFFFF] - Default tint.\n     * @param {string} [options.pluginName='batch'] - Renderer plugin for batching.\n     * @param {PIXI.Program} [options.program=0xFFFFFF] - Custom program.\n     * @param {object} [options.uniforms] - Custom uniforms.\n     */\n    constructor(uSampler: Texture, options?: IMeshMaterialOptions)\n    {\n        const uniforms = {\n            uSampler,\n            alpha: 1,\n            uTextureMatrix: Matrix.IDENTITY,\n            uColor: new Float32Array([1, 1, 1, 1]),\n        };\n\n        // Set defaults\n        options = Object.assign({\n            tint: 0xFFFFFF,\n            alpha: 1,\n            pluginName: 'batch',\n        }, options);\n\n        if (options.uniforms)\n        {\n            Object.assign(uniforms, options.uniforms);\n        }\n\n        super(options.program || Program.from(vertex, fragment), uniforms);\n\n        this._colorDirty = false;\n\n        this.uvMatrix = new TextureMatrix(uSampler);\n        this.batchable = options.program === undefined;\n        this.pluginName = options.pluginName;\n\n        this._tintColor = new Color(options.tint);\n        this._tintRGB = this._tintColor.toLittleEndianNumber();\n        this._colorDirty = true;\n        this.alpha = options.alpha;\n    }\n\n    /** Reference to the texture being rendered. */\n    get texture(): Texture\n    {\n        return this.uniforms.uSampler;\n    }\n    set texture(value: Texture)\n    {\n        if (this.uniforms.uSampler !== value)\n        {\n            if (!this.uniforms.uSampler.baseTexture.alphaMode !== !value.baseTexture.alphaMode)\n            {\n                this._colorDirty = true;\n            }\n\n            this.uniforms.uSampler = value;\n            this.uvMatrix.texture = value;\n        }\n    }\n\n    /**\n     * This gets automatically set by the object using this.\n     * @default 1\n     */\n    set alpha(value: number)\n    {\n        if (value === this._alpha) return;\n\n        this._alpha = value;\n        this._colorDirty = true;\n    }\n    get alpha(): number\n    {\n        return this._alpha;\n    }\n\n    /**\n     * Multiply tint for the material.\n     * @default 0xFFFFFF\n     */\n    set tint(value: ColorSource)\n    {\n        if (value === this.tint) return;\n\n        this._tintColor.setValue(value);\n        this._tintRGB = this._tintColor.toLittleEndianNumber();\n        this._colorDirty = true;\n    }\n    get tint(): ColorSource\n    {\n        return this._tintColor.value;\n    }\n\n    /**\n     * Get the internal number from tint color\n     * @ignore\n     */\n    get tintValue(): number\n    {\n        return this._tintColor.toNumber();\n    }\n\n    /** Gets called automatically by the Mesh. Intended to be overridden for custom {@link PIXI.MeshMaterial} objects. */\n    public update(): void\n    {\n        if (this._colorDirty)\n        {\n            this._colorDirty = false;\n            const baseTexture = this.texture.baseTexture;\n            const applyToChannels = (baseTexture.alphaMode as unknown as boolean);\n\n            Color.shared\n                .setValue(this._tintColor)\n                .premultiply(this._alpha, applyToChannels)\n                .toArray(this.uniforms.uColor);\n        }\n        if (this.uvMatrix.update())\n        {\n            this.uniforms.uTextureMatrix = this.uvMatrix.mapCoord;\n        }\n    }\n}\n"], "names": ["Shader", "Matrix", "Program", "vertex", "fragment", "TextureMatrix", "Color"], "mappings": ";;AAsBO,MAAM,qBAAqBA,KAAAA,OAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwCI,YAAY,UAAmB,SAC/B;AACI,UAAM,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,MACP,gBAAgBC,KAAO,OAAA;AAAA,MACvB,QAAQ,IAAI,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAAA;AAIzC,cAAU,OAAO,OAAO;AAAA,MACpB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IAAA,GACb,OAAO,GAEN,QAAQ,YAER,OAAO,OAAO,UAAU,QAAQ,QAAQ,GAG5C,MAAM,QAAQ,WAAWC,KAAA,QAAQ,KAAKC,cAAQC,OAAAA,OAAQ,GAAG,QAAQ,GAEjE,KAAK,cAAc,IAEnB,KAAK,WAAW,IAAIC,KAAc,cAAA,QAAQ,GAC1C,KAAK,YAAY,QAAQ,YAAY,QACrC,KAAK,aAAa,QAAQ,YAE1B,KAAK,aAAa,IAAIC,KAAAA,MAAM,QAAQ,IAAI,GACxC,KAAK,WAAW,KAAK,WAAW,wBAChC,KAAK,cAAc,IACnB,KAAK,QAAQ,QAAQ;AAAA,EACzB;AAAA;AAAA,EAGA,IAAI,UACJ;AACI,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,IAAI,QAAQ,OACZ;AACQ,SAAK,SAAS,aAAa,UAEvB,CAAC,KAAK,SAAS,SAAS,YAAY,aAAc,CAAC,MAAM,YAAY,cAErE,KAAK,cAAc,KAGvB,KAAK,SAAS,WAAW,OACzB,KAAK,SAAS,UAAU;AAAA,EAEhC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM,OACV;AACQ,cAAU,KAAK,WAEnB,KAAK,SAAS,OACd,KAAK,cAAc;AAAA,EACvB;AAAA,EACA,IAAI,QACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK,OACT;AACQ,cAAU,KAAK,SAEnB,KAAK,WAAW,SAAS,KAAK,GAC9B,KAAK,WAAW,KAAK,WAAW,qBAAqB,GACrD,KAAK,cAAc;AAAA,EACvB;AAAA,EACA,IAAI,OACJ;AACI,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YACJ;AACW,WAAA,KAAK,WAAW;EAC3B;AAAA;AAAA,EAGO,SACP;AACI,QAAI,KAAK,aACT;AACI,WAAK,cAAc;AAEb,YAAA,kBADc,KAAK,QAAQ,YACI;AAErCA,WAAA,MAAM,OACD,SAAS,KAAK,UAAU,EACxB,YAAY,KAAK,QAAQ,eAAe,EACxC,QAAQ,KAAK,SAAS,MAAM;AAAA,IACrC;AACI,SAAK,SAAS,OAAO,MAErB,KAAK,SAAS,iBAAiB,KAAK,SAAS;AAAA,EAErD;AACJ;;"}