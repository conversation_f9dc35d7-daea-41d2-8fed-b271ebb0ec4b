{"version": 3, "file": "Mesh.js", "sources": ["../src/Mesh.ts"], "sourcesContent": ["import { DRAW_MODES, Point, Polygon, settings, State } from '@pixi/core';\nimport { Container } from '@pixi/display';\nimport { MeshBatchUvs } from './MeshBatchUvs';\n\nimport type { BLEND_MODES, Buffer, ColorSource, Geometry, IPointData, Renderer, Shader, Texture } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\nimport type { MeshMaterial } from './MeshMaterial';\n\nconst tempPoint = new Point();\nconst tempPolygon = new Polygon();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Mesh extends GlobalMixins.Mesh {}\n\n/**\n * Base mesh class.\n *\n * This class empowers you to have maximum flexibility to render any kind of WebGL visuals you can think of.\n * This class assumes a certain level of WebGL knowledge.\n * If you know a bit this should abstract enough away to make your life easier!\n *\n * Pretty much ALL WebGL can be broken down into the following:\n * - Geometry - The structure and data for the mesh. This can include anything from positions, uvs, normals, colors etc..\n * - Shader - This is the shader that PixiJS will render the geometry with (attributes in the shader must match the geometry)\n * - State - This is the state of WebGL required to render the mesh.\n *\n * Through a combination of the above elements you can render anything you want, 2D or 3D!\n * @memberof PIXI\n */\nexport class Mesh<T extends Shader = MeshMaterial> extends Container\n{\n    /**\n     * Used by the @pixi/canvas-mesh package to draw meshes using canvas.\n     * Added here because we cannot mixin a static property to Mesh type.\n     * @ignore\n     */\n    public static defaultCanvasPadding: number;\n\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Mesh objects.\n     * @type {PIXI.Shader|PIXI.MeshMaterial}\n     */\n    public shader: T;\n\n    /**\n     * Represents the WebGL state the Mesh required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    public state: State;\n\n    /** The way the Mesh should be drawn, can be any of the {@link PIXI.DRAW_MODES} constants. */\n    public drawMode: DRAW_MODES;\n\n    /**\n     * Typically the index of the IndexBuffer where to start drawing.\n     * @default 0\n     */\n    public start: number;\n\n    /**\n     * How much of the geometry to draw, by default `0` renders everything.\n     * @default 0\n     */\n    public size: number;\n\n    private _geometry: Geometry;\n\n    /** This is the caching layer used by the batcher. */\n    private vertexData: Float32Array;\n\n    /** If geometry is changed used to decide to re-transform the vertexData. */\n    private vertexDirty: number;\n    private _transformID: number;\n\n    /** Internal roundPixels field. */\n    private _roundPixels: boolean;\n\n    /** Batched UV's are cached for atlas textures. */\n    private batchUvs: MeshBatchUvs;\n\n    // Internal-only properties\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    uvs: Float32Array;\n\n    /**\n     * These are used as easy access for batching.\n     * @private\n     */\n    indices: Uint16Array;\n    _tintRGB: number;\n    _texture: Texture;\n\n    /**\n     * @param geometry - The geometry the mesh will use.\n     * @param {PIXI.MeshMaterial} shader - The shader the mesh will use.\n     * @param state - The state that the WebGL context is required to be in to render the mesh\n     *        if no state is provided, uses {@link PIXI.State.for2d} to create a 2D state for PixiJS.\n     * @param drawMode - The drawMode, can be any of the {@link PIXI.DRAW_MODES} constants.\n     */\n    constructor(geometry: Geometry, shader: T, state?: State, drawMode: DRAW_MODES = DRAW_MODES.TRIANGLES)\n    {\n        super();\n\n        this.geometry = geometry;\n        this.shader = shader;\n        this.state = state || State.for2d();\n        this.drawMode = drawMode;\n        this.start = 0;\n        this.size = 0;\n\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = new Float32Array(1);\n        this.vertexDirty = -1;\n\n        this._transformID = -1;\n        this._roundPixels = settings.ROUND_PIXELS;\n        this.batchUvs = null;\n    }\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh objects.\n     */\n    get geometry(): Geometry\n    {\n        return this._geometry;\n    }\n\n    set geometry(value: Geometry)\n    {\n        if (this._geometry === value)\n        {\n            return;\n        }\n\n        if (this._geometry)\n        {\n            this._geometry.refCount--;\n\n            if (this._geometry.refCount === 0)\n            {\n                this._geometry.dispose();\n            }\n        }\n\n        this._geometry = value;\n\n        if (this._geometry)\n        {\n            this._geometry.refCount++;\n        }\n\n        this.vertexDirty = -1;\n    }\n\n    /**\n     * To change mesh uv's, change its uvBuffer data and increment its _updateID.\n     * @readonly\n     */\n    get uvBuffer(): Buffer\n    {\n        return this.geometry.buffers[1];\n    }\n\n    /**\n     * To change mesh vertices, change its uvBuffer data and increment its _updateID.\n     * Incrementing _updateID is optional because most of Mesh objects do it anyway.\n     * @readonly\n     */\n    get verticesBuffer(): Buffer\n    {\n        return this.geometry.buffers[0];\n    }\n\n    /** Alias for {@link PIXI.Mesh#shader}. */\n    set material(value: T)\n    {\n        this.shader = value;\n    }\n\n    get material(): T\n    {\n        return this.shader;\n    }\n\n    /**\n     * The blend mode to be applied to the Mesh. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL;\n     */\n    set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}\n     * @default false\n     */\n    set roundPixels(value: boolean)\n    {\n        if (this._roundPixels !== value)\n        {\n            this._transformID = -1;\n        }\n        this._roundPixels = value;\n    }\n\n    get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    /**\n     * The multiply tint applied to the Mesh. This is a hex value. A value of\n     * `0xFFFFFF` will remove any tint effect.\n     *\n     * Null for non-MeshMaterial shaders\n     * @default 0xFFFFFF\n     */\n    get tint(): ColorSource\n    {\n        return 'tint' in this.shader ? (this.shader as unknown as MeshMaterial).tint : null;\n    }\n\n    set tint(value: ColorSource)\n    {\n        (this.shader as unknown as MeshMaterial).tint = value;\n    }\n\n    /**\n     * The tint color as a RGB integer\n     * @ignore\n     */\n    get tintValue(): number\n    {\n        return (this.shader as unknown as MeshMaterial).tintValue;\n    }\n\n    /** The texture that the Mesh uses. Null for non-MeshMaterial shaders */\n    get texture(): Texture\n    {\n        return 'texture' in this.shader ? (this.shader as unknown as MeshMaterial).texture : null;\n    }\n\n    set texture(value: Texture)\n    {\n        (this.shader as unknown as MeshMaterial).texture = value;\n    }\n\n    /**\n     * Standard renderer draw.\n     * @param renderer - Instance to renderer.\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // set properties for batching..\n        // TODO could use a different way to grab verts?\n        const vertices = this.geometry.buffers[0].data;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        // TODO benchmark check for attribute size..\n        if (\n            shader.batchable\n            && this.drawMode === DRAW_MODES.TRIANGLES\n            && vertices.length < Mesh.BATCHABLE_SIZE * 2\n        )\n        {\n            this._renderToBatch(renderer);\n        }\n        else\n        {\n            this._renderDefault(renderer);\n        }\n    }\n\n    /**\n     * Standard non-batching way of rendering.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderDefault(renderer: Renderer): void\n    {\n        const shader = this.shader as unknown as MeshMaterial;\n\n        shader.alpha = this.worldAlpha;\n        if (shader.update)\n        {\n            shader.update();\n        }\n\n        renderer.batch.flush();\n\n        // bind and sync uniforms..\n        shader.uniforms.translationMatrix = this.transform.worldTransform.toArray(true);\n        renderer.shader.bind(shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // bind the geometry...\n        renderer.geometry.bind(this.geometry, shader);\n\n        // then render it\n        renderer.geometry.draw(this.drawMode, this.size, this.start, this.geometry.instanceCount);\n    }\n\n    /**\n     * Rendering by using the Batch system.\n     * @param renderer - Instance to renderer.\n     */\n    protected _renderToBatch(renderer: Renderer): void\n    {\n        const geometry = this.geometry;\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (shader.uvMatrix)\n        {\n            shader.uvMatrix.update();\n            this.calculateUvs();\n        }\n\n        // set properties for batching..\n        this.calculateVertices();\n        this.indices = geometry.indexBuffer.data as Uint16Array;\n        this._tintRGB = shader._tintRGB;\n        this._texture = shader.texture;\n\n        const pluginName = (this.material as unknown as MeshMaterial).pluginName;\n\n        renderer.batch.setObjectRenderer(renderer.plugins[pluginName]);\n        renderer.plugins[pluginName].render(this);\n    }\n\n    /** Updates vertexData field based on transform and vertices. */\n    public calculateVertices(): void\n    {\n        const geometry = this.geometry;\n        const verticesBuffer = geometry.buffers[0];\n        const vertices = verticesBuffer.data;\n        const vertexDirtyId = verticesBuffer._updateID;\n\n        if (vertexDirtyId === this.vertexDirty && this._transformID === this.transform._worldID)\n        {\n            return;\n        }\n\n        this._transformID = this.transform._worldID;\n\n        if (this.vertexData.length !== vertices.length)\n        {\n            this.vertexData = new Float32Array(vertices.length);\n        }\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const vertexData = this.vertexData;\n\n        for (let i = 0; i < vertexData.length / 2; i++)\n        {\n            const x = vertices[(i * 2)];\n            const y = vertices[(i * 2) + 1];\n\n            vertexData[(i * 2)] = (a * x) + (c * y) + tx;\n            vertexData[(i * 2) + 1] = (b * x) + (d * y) + ty;\n        }\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round(vertexData[i] * resolution) / resolution;\n            }\n        }\n\n        this.vertexDirty = vertexDirtyId;\n    }\n\n    /** Updates uv field based on from geometry uv's or batchUvs. */\n    public calculateUvs(): void\n    {\n        const geomUvs = this.geometry.buffers[1];\n        const shader = this.shader as unknown as MeshMaterial;\n\n        if (!shader.uvMatrix.isSimple)\n        {\n            if (!this.batchUvs)\n            {\n                this.batchUvs = new MeshBatchUvs(geomUvs, shader.uvMatrix);\n            }\n            this.batchUvs.update();\n            this.uvs = this.batchUvs.data;\n        }\n        else\n        {\n            this.uvs = geomUvs.data as Float32Array;\n        }\n    }\n\n    /**\n     * Updates the bounds of the mesh as a rectangle. The bounds calculation takes the worldTransform into account.\n     * there must be a aVertexPosition attribute present in the geometry for bounds to be calculated correctly.\n     */\n    protected _calculateBounds(): void\n    {\n        this.calculateVertices();\n\n        this._bounds.addVertexData(this.vertexData, 0, this.vertexData.length);\n    }\n\n    /**\n     * Tests if a point is inside this mesh. Works only for PIXI.DRAW_MODES.TRIANGLES.\n     * @param point - The point to test.\n     * @returns - The result of the test.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        if (!this.getBounds().contains(point.x, point.y))\n        {\n            return false;\n        }\n\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const vertices = this.geometry.getBuffer('aVertexPosition').data;\n\n        const points = tempPolygon.points;\n        const indices = this.geometry.getIndex().data;\n        const len = indices.length;\n        const step = this.drawMode === 4 ? 3 : 1;\n\n        for (let i = 0; i + 2 < len; i += step)\n        {\n            const ind0 = indices[i] * 2;\n            const ind1 = indices[i + 1] * 2;\n            const ind2 = indices[i + 2] * 2;\n\n            points[0] = vertices[ind0];\n            points[1] = vertices[ind0 + 1];\n            points[2] = vertices[ind1];\n            points[3] = vertices[ind1 + 1];\n            points[4] = vertices[ind2];\n            points[5] = vertices[ind2 + 1];\n\n            if (tempPolygon.contains(tempPoint.x, tempPoint.y))\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        if (this._cachedTexture)\n        {\n            this._cachedTexture.destroy();\n            this._cachedTexture = null;\n        }\n\n        this.geometry = null;\n        this.shader = null;\n        this.state = null;\n        this.uvs = null;\n        this.indices = null;\n        this.vertexData = null;\n    }\n\n    /** The maximum number of vertices to consider batchable. Generally, the complexity of the geometry. */\n    public static BATCHABLE_SIZE = 100;\n}\n"], "names": ["Point", "Polygon", "_Mesh", "Container", "DRAW_MODES", "State", "settings", "MeshBatchUvs"], "mappings": ";;AAQA,MAAM,YAAY,IAAIA,WAChB,GAAA,cAAc,IAAIC,KAAA,QAAA,GAoBX,QAAN,MAAMC,eAA8CC,kBAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyEI,YAAY,UAAoB,QAAW,OAAe,WAAuBC,gBAAW,WAC5F;AACU,aAEN,KAAK,WAAW,UAChB,KAAK,SAAS,QACd,KAAK,QAAQ,SAASC,WAAM,SAC5B,KAAK,WAAW,UAChB,KAAK,QAAQ,GACb,KAAK,OAAO,GAEZ,KAAK,MAAM,MACX,KAAK,UAAU,MACf,KAAK,aAAa,IAAI,aAAa,CAAC,GACpC,KAAK,cAAc,IAEnB,KAAK,eAAe,IACpB,KAAK,eAAeC,KAAS,SAAA,cAC7B,KAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,SAAS,OACb;AACQ,SAAK,cAAc,UAKnB,KAAK,cAEL,KAAK,UAAU,YAEX,KAAK,UAAU,aAAa,KAE5B,KAAK,UAAU,QAAA,IAIvB,KAAK,YAAY,OAEb,KAAK,aAEL,KAAK,UAAU,YAGnB,KAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WACJ;AACW,WAAA,KAAK,SAAS,QAAQ,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,iBACJ;AACW,WAAA,KAAK,SAAS,QAAQ,CAAC;AAAA,EAClC;AAAA;AAAA,EAGA,IAAI,SAAS,OACb;AACI,SAAK,SAAS;AAAA,EAClB;AAAA,EAEA,IAAI,WACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU,OACd;AACI,SAAK,MAAM,YAAY;AAAA,EAC3B;AAAA,EAEA,IAAI,YACJ;AACI,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,YAAY,OAChB;AACQ,SAAK,iBAAiB,UAEtB,KAAK,eAAe,KAExB,KAAK,eAAe;AAAA,EACxB;AAAA,EAEA,IAAI,cACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,OACJ;AACI,WAAO,UAAU,KAAK,SAAU,KAAK,OAAmC,OAAO;AAAA,EACnF;AAAA,EAEA,IAAI,KAAK,OACT;AACK,SAAK,OAAmC,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YACJ;AACI,WAAQ,KAAK,OAAmC;AAAA,EACpD;AAAA;AAAA,EAGA,IAAI,UACJ;AACI,WAAO,aAAa,KAAK,SAAU,KAAK,OAAmC,UAAU;AAAA,EACzF;AAAA,EAEA,IAAI,QAAQ,OACZ;AACK,SAAK,OAAmC,UAAU;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,QAAQ,UAClB;AAGI,UAAM,WAAW,KAAK,SAAS,QAAQ,CAAC,EAAE;AAC3B,SAAK,OAIT,aACJ,KAAK,aAAaF,gBAAW,aAC7B,SAAS,SAASF,OAAK,iBAAiB,IAG3C,KAAK,eAAe,QAAQ,IAI5B,KAAK,eAAe,QAAQ;AAAA,EAEpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,eAAe,UACzB;AACI,UAAM,SAAS,KAAK;AAEb,WAAA,QAAQ,KAAK,YAChB,OAAO,UAEP,OAAO,OAAA,GAGX,SAAS,MAAM,MAAA,GAGf,OAAO,SAAS,oBAAoB,KAAK,UAAU,eAAe,QAAQ,EAAI,GAC9E,SAAS,OAAO,KAAK,MAAM,GAG3B,SAAS,MAAM,IAAI,KAAK,KAAK,GAG7B,SAAS,SAAS,KAAK,KAAK,UAAU,MAAM,GAG5C,SAAS,SAAS,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,aAAa;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,eAAe,UACzB;AACI,UAAM,WAAW,KAAK,UAChB,SAAS,KAAK;AAEhB,WAAO,aAEP,OAAO,SAAS,UAChB,KAAK,aAAa,IAItB,KAAK,kBAAA,GACL,KAAK,UAAU,SAAS,YAAY,MACpC,KAAK,WAAW,OAAO,UACvB,KAAK,WAAW,OAAO;AAEjB,UAAA,aAAc,KAAK,SAAqC;AAE9D,aAAS,MAAM,kBAAkB,SAAS,QAAQ,UAAU,CAAC,GAC7D,SAAS,QAAQ,UAAU,EAAE,OAAO,IAAI;AAAA,EAC5C;AAAA;AAAA,EAGO,oBACP;AAEU,UAAA,iBADW,KAAK,SACU,QAAQ,CAAC,GACnC,WAAW,eAAe,MAC1B,gBAAgB,eAAe;AAErC,QAAI,kBAAkB,KAAK,eAAe,KAAK,iBAAiB,KAAK,UAAU;AAE3E;AAGJ,SAAK,eAAe,KAAK,UAAU,UAE/B,KAAK,WAAW,WAAW,SAAS,WAEpC,KAAK,aAAa,IAAI,aAAa,SAAS,MAAM;AAGhD,UAAA,KAAK,KAAK,UAAU,gBACpB,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,KAAK,GAAG,IACR,KAAK,GAAG,IAER,aAAa,KAAK;AAExB,aAAS,IAAI,GAAG,IAAI,WAAW,SAAS,GAAG,KAC3C;AACU,YAAA,IAAI,SAAU,IAAI,CAAE,GACpB,IAAI,SAAU,IAAI,IAAK,CAAC;AAE9B,iBAAY,IAAI,CAAE,IAAK,IAAI,IAAM,IAAI,IAAK,IAC1C,WAAY,IAAI,IAAK,CAAC,IAAK,IAAI,IAAM,IAAI,IAAK;AAAA,IAClD;AAEA,QAAI,KAAK,cACT;AACI,YAAM,aAAaI,KAAS,SAAA;AAE5B,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AAE1B,mBAAA,CAAC,IAAI,KAAK,MAAM,WAAW,CAAC,IAAI,UAAU,IAAI;AAAA,IAEjE;AAEA,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA,EAGO,eACP;AACI,UAAM,UAAU,KAAK,SAAS,QAAQ,CAAC,GACjC,SAAS,KAAK;AAEf,WAAO,SAAS,WAWjB,KAAK,MAAM,QAAQ,QATd,KAAK,aAEN,KAAK,WAAW,IAAIC,0BAAa,SAAS,OAAO,QAAQ,IAE7D,KAAK,SAAS,UACd,KAAK,MAAM,KAAK,SAAS;AAAA,EAMjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,mBACV;AACS,SAAA,kBAAA,GAEL,KAAK,QAAQ,cAAc,KAAK,YAAY,GAAG,KAAK,WAAW,MAAM;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OACrB;AACQ,QAAA,CAAC,KAAK,YAAY,SAAS,MAAM,GAAG,MAAM,CAAC;AAEpC,aAAA;AAGN,SAAA,eAAe,aAAa,OAAO,SAAS;AAE3C,UAAA,WAAW,KAAK,SAAS,UAAU,iBAAiB,EAAE,MAEtD,SAAS,YAAY,QACrB,UAAU,KAAK,SAAS,WAAW,MACnC,MAAM,QAAQ,QACd,OAAO,KAAK,aAAa,IAAI,IAAI;AAEvC,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,MAClC;AACI,YAAM,OAAO,QAAQ,CAAC,IAAI,GACpB,OAAO,QAAQ,IAAI,CAAC,IAAI,GACxB,OAAO,QAAQ,IAAI,CAAC,IAAI;AAE9B,UAAA,OAAO,CAAC,IAAI,SAAS,IAAI,GACzB,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,GAC7B,OAAO,CAAC,IAAI,SAAS,IAAI,GACzB,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,GAC7B,OAAO,CAAC,IAAI,SAAS,IAAI,GACzB,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,GAEzB,YAAY,SAAS,UAAU,GAAG,UAAU,CAAC;AAEtC,eAAA;AAAA,IAEf;AAEO,WAAA;AAAA,EACX;AAAA,EAEO,QAAQ,SACf;AACI,UAAM,QAAQ,OAAO,GAEjB,KAAK,mBAEL,KAAK,eAAe,QACpB,GAAA,KAAK,iBAAiB,OAG1B,KAAK,WAAW,MAChB,KAAK,SAAS,MACd,KAAK,QAAQ,MACb,KAAK,MAAM,MACX,KAAK,UAAU,MACf,KAAK,aAAa;AAAA,EACtB;AAIJ;AAjda,MAgdK,iBAAiB;AAhd5B,IAAM,OAAN;;"}