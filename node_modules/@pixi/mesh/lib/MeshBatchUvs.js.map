{"version": 3, "file": "MeshBatchUvs.js", "sources": ["../src/MeshBatchUvs.ts"], "sourcesContent": ["import type { Buffer, TextureMatrix } from '@pixi/core';\n\n/**\n * Class controls cache for UV mapping from Texture normal space to BaseTexture normal space.\n * @memberof PIXI\n */\nexport class MeshBatchUvs\n{\n    /** UV Buffer data. */\n    public readonly data: Float32Array;\n\n    /** Buffer with normalized UV's. */\n    public uvBuffer: Buffer;\n\n    /** Material UV matrix. */\n    public uvMatrix: TextureMatrix;\n\n    private _bufferUpdateId: number;\n    private _textureUpdateId: number;\n\n    // Internal-only properties\n    _updateID: number;\n\n    /**\n     * @param uvBuffer - Buffer with normalized uv's\n     * @param uvMatrix - Material UV matrix\n     */\n    constructor(uvBuffer: Buffer, uvMatrix: TextureMatrix)\n    {\n        this.uvBuffer = uvBuffer;\n        this.uvMatrix = uvMatrix;\n        this.data = null;\n\n        this._bufferUpdateId = -1;\n        this._textureUpdateId = -1;\n        this._updateID = 0;\n    }\n\n    /**\n     * Updates\n     * @param forceUpdate - force the update\n     */\n    public update(forceUpdate?: boolean): void\n    {\n        if (!forceUpdate\n            && this._bufferUpdateId === this.uvBuffer._updateID\n            && this._textureUpdateId === this.uvMatrix._updateID\n        )\n        {\n            return;\n        }\n\n        this._bufferUpdateId = this.uvBuffer._updateID;\n        this._textureUpdateId = this.uvMatrix._updateID;\n\n        const data = this.uvBuffer.data as Float32Array;\n\n        if (!this.data || this.data.length !== data.length)\n        {\n            (this.data as any) = new Float32Array(data.length);\n        }\n\n        this.uvMatrix.multiplyUvs(data, this.data);\n\n        this._updateID++;\n    }\n}\n"], "names": [], "mappings": ";AAMO,MAAM,aACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBI,YAAY,UAAkB,UAC9B;AACI,SAAK,WAAW,UAChB,KAAK,WAAW,UAChB,KAAK,OAAO,MAEZ,KAAK,kBAAkB,IACvB,KAAK,mBAAmB,IACxB,KAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,OAAO,aACd;AACQ,QAAA,CAAC,eACE,KAAK,oBAAoB,KAAK,SAAS,aACvC,KAAK,qBAAqB,KAAK,SAAS;AAG3C;AAGJ,SAAK,kBAAkB,KAAK,SAAS,WACrC,KAAK,mBAAmB,KAAK,SAAS;AAEhC,UAAA,OAAO,KAAK,SAAS;AAEvB,KAAA,CAAC,KAAK,QAAQ,KAAK,KAAK,WAAW,KAAK,YAEvC,KAAK,OAAe,IAAI,aAAa,KAAK,MAAM,IAGrD,KAAK,SAAS,YAAY,MAAM,KAAK,IAAI,GAEzC,KAAK;AAAA,EACT;AACJ;;"}