{"version": 3, "file": "MeshGeometry.js", "sources": ["../src/MeshGeometry.ts"], "sourcesContent": ["import { Buffer, Geometry, TYPES } from '@pixi/core';\n\nimport type { IArrayBuffer } from '@pixi/core';\n\n/**\n * Standard 2D geometry used in PixiJS.\n *\n * Geometry can be defined without passing in a style or data if required.\n * @example\n * import { Geometry } from 'pixi.js';\n *\n * const geometry = new Geometry();\n *\n * geometry.addAttribute('positions', [0, 0, 100, 0, 100, 100, 0, 100], 2);\n * geometry.addAttribute('uvs', [0, 0, 1, 0, 1, 1, 0, 1], 2);\n * geometry.addIndex([0, 1, 2, 1, 3, 2]);\n * @memberof PIXI\n */\nexport class MeshGeometry extends Geometry\n{\n    // Internal-only properties\n    /**\n     * Dirty flag to limit update calls on Mesh. For example,\n     * limiting updates on a single Mesh instance with a shared Geometry\n     * within the render loop.\n     * @private\n     * @default -1\n     */\n    _updateId: number;\n\n    /**\n     * @param {Float32Array|number[]} [vertices] - Positional data on geometry.\n     * @param {Float32Array|number[]} [uvs] - Texture UVs.\n     * @param {Uint16Array|number[]} [index] - IndexBuffer\n     */\n    constructor(vertices?: IArrayBuffer, uvs?: IArrayBuffer, index?: IArrayBuffer)\n    {\n        super();\n\n        const verticesBuffer = new Buffer(vertices);\n        const uvsBuffer = new Buffer(uvs, true);\n        const indexBuffer = new Buffer(index, true, true);\n\n        this.addAttribute('aVertexPosition', verticesBuffer, 2, false, TYPES.FLOAT)\n            .addAttribute('aTextureCoord', uvsBuffer, 2, false, TYPES.FLOAT)\n            .addIndex(indexBuffer);\n\n        this._updateId = -1;\n    }\n\n    /**\n     * If the vertex position is updated.\n     * @readonly\n     * @private\n     */\n    get vertexDirtyId(): number\n    {\n        return this.buffers[0]._updateID;\n    }\n}\n"], "names": ["Geometry", "<PERSON><PERSON><PERSON>", "TYPES"], "mappings": ";;AAkBO,MAAM,qBAAqBA,KAAAA,SAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBI,YAAY,UAAyB,KAAoB,OACzD;AACU;AAEN,UAAM,iBAAiB,IAAIC,YAAO,QAAQ,GACpC,YAAY,IAAIA,KAAAA,OAAO,KAAK,EAAI,GAChC,cAAc,IAAIA,KAAO,OAAA,OAAO,IAAM,EAAI;AAE3C,SAAA,aAAa,mBAAmB,gBAAgB,GAAG,IAAOC,WAAM,KAAK,EACrE,aAAa,iBAAiB,WAAW,GAAG,IAAOA,WAAM,KAAK,EAC9D,SAAS,WAAW,GAEzB,KAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,gBACJ;AACW,WAAA,KAAK,QAAQ,CAAC,EAAE;AAAA,EAC3B;AACJ;;"}