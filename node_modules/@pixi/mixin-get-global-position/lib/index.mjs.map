{"version": 3, "file": "index.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["/// <reference path=\"../global.d.ts\" />\nimport { Point } from '@pixi/core';\nimport { DisplayObject } from '@pixi/display';\n\n/**\n * Returns the global position of the displayObject. Does not depend on object scale, rotation and pivot.\n * @method getGlobalPosition\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Point} [point=new PIXI.Point()] - The point to write the global value to.\n * @param {boolean} [skipUpdate=false] - Setting to true will stop the transforms of the scene graph from\n *  being updated. This means the calculation returned MAY be out of date BUT will give you a\n *  nice performance boost.\n * @returns {PIXI.Point} The updated point.\n */\nDisplayObject.prototype.getGlobalPosition = function getGlobalPosition(point: Point = new Point(), skipUpdate = false): Point\n{\n    if (this.parent)\n    {\n        this.parent.toGlobal(this.position, point, skipUpdate);\n    }\n    else\n    {\n        point.x = this.position.x;\n        point.y = this.position.y;\n    }\n\n    return point;\n};\n"], "names": [], "mappings": ";;AAcA,cAAc,UAAU,oBAAoB,SAA2B,QAAe,IAAI,SAAS,aAAa,IAChH;AACI,SAAI,KAAK,SAEL,KAAK,OAAO,SAAS,KAAK,UAAU,OAAO,UAAU,KAIrD,MAAM,IAAI,KAAK,SAAS,GACxB,MAAM,IAAI,KAAK,SAAS,IAGrB;AACX;"}