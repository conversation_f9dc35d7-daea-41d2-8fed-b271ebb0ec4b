{"version": 3, "file": "Rectangle.js", "sources": ["../../src/shapes/Rectangle.ts"], "sourcesContent": ["import { SHAPES } from '../const';\nimport { Point } from '../Point';\n\nimport type { Matrix } from '../Matrix';\n\nconst tempPoints = [new Point(), new Point(), new Point(), new Point()];\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Rectangle extends GlobalMixins.Rectangle {}\n\n/**\n * Size object, contains width and height\n * @memberof PIXI\n * @typedef {object} ISize\n * @property {number} width - Width component\n * @property {number} height - Height component\n */\n\n/**\n * Rectangle object is an area defined by its position, as indicated by its top-left corner\n * point (x, y) and by its width and its height.\n * @memberof PIXI\n */\nexport class Rectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RECT\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RECT;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rectangle\n     * @param width - The overall width of the rectangle\n     * @param height - The overall height of the rectangle\n     */\n    constructor(x: string | number = 0, y: string | number = 0, width: string | number = 0, height: string | number = 0)\n    {\n        this.x = Number(x);\n        this.y = Number(y);\n        this.width = Number(width);\n        this.height = Number(height);\n        this.type = SHAPES.RECT;\n    }\n\n    /** Returns the left edge of the rectangle. */\n    get left(): number\n    {\n        return this.x;\n    }\n\n    /** Returns the right edge of the rectangle. */\n    get right(): number\n    {\n        return this.x + this.width;\n    }\n\n    /** Returns the top edge of the rectangle. */\n    get top(): number\n    {\n        return this.y;\n    }\n\n    /** Returns the bottom edge of the rectangle. */\n    get bottom(): number\n    {\n        return this.y + this.height;\n    }\n\n    /** A constant empty rectangle. */\n    static get EMPTY(): Rectangle\n    {\n        return new Rectangle(0, 0, 0, 0);\n    }\n\n    /**\n     * Creates a clone of this Rectangle\n     * @returns a copy of the rectangle\n     */\n    clone(): Rectangle\n    {\n        return new Rectangle(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Copies another rectangle to this one.\n     * @param rectangle - The rectangle to copy from.\n     * @returns Returns itself.\n     */\n    copyFrom(rectangle: Rectangle): Rectangle\n    {\n        this.x = rectangle.x;\n        this.y = rectangle.y;\n        this.width = rectangle.width;\n        this.height = rectangle.height;\n\n        return this;\n    }\n\n    /**\n     * Copies this rectangle to another one.\n     * @param rectangle - The rectangle to copy to.\n     * @returns Returns given parameter.\n     */\n    copyTo(rectangle: Rectangle): Rectangle\n    {\n        rectangle.x = this.x;\n        rectangle.y = this.y;\n        rectangle.width = this.width;\n        rectangle.height = this.height;\n\n        return rectangle;\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rectangle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Rectangle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        if (x >= this.x && x < this.x + this.width)\n        {\n            if (y >= this.y && y < this.y + this.height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Determines whether the `other` Rectangle transformed by `transform` intersects with `this` Rectangle object.\n     * Returns true only if the area of the intersection is >0, this means that Rectangles\n     * sharing a side are not overlapping. Another side effect is that an arealess rectangle\n     * (width or height equal to zero) can't intersect any other rectangle.\n     * @param {Rectangle} other - The Rectangle to intersect with `this`.\n     * @param {Matrix} transform - The transformation matrix of `other`.\n     * @returns {boolean} A value of `true` if the transformed `other` Rectangle intersects with `this`; otherwise `false`.\n     */\n    intersects(other: Rectangle, transform?: Matrix): boolean\n    {\n        if (!transform)\n        {\n            const x0 = this.x < other.x ? other.x : this.x;\n            const x1 = this.right > other.right ? other.right : this.right;\n\n            if (x1 <= x0)\n            {\n                return false;\n            }\n\n            const y0 = this.y < other.y ? other.y : this.y;\n            const y1 = this.bottom > other.bottom ? other.bottom : this.bottom;\n\n            return y1 > y0;\n        }\n\n        const x0 = this.left;\n        const x1 = this.right;\n        const y0 = this.top;\n        const y1 = this.bottom;\n\n        if (x1 <= x0 || y1 <= y0)\n        {\n            return false;\n        }\n\n        const lt = tempPoints[0].set(other.left, other.top);\n        const lb = tempPoints[1].set(other.left, other.bottom);\n        const rt = tempPoints[2].set(other.right, other.top);\n        const rb = tempPoints[3].set(other.right, other.bottom);\n\n        if (rt.x <= lt.x || lb.y <= lt.y)\n        {\n            return false;\n        }\n\n        const s = Math.sign((transform.a * transform.d) - (transform.b * transform.c));\n\n        if (s === 0)\n        {\n            return false;\n        }\n\n        transform.apply(lt, lt);\n        transform.apply(lb, lb);\n        transform.apply(rt, rt);\n        transform.apply(rb, rb);\n\n        if (Math.max(lt.x, lb.x, rt.x, rb.x) <= x0\n            || Math.min(lt.x, lb.x, rt.x, rb.x) >= x1\n            || Math.max(lt.y, lb.y, rt.y, rb.y) <= y0\n            || Math.min(lt.y, lb.y, rt.y, rb.y) >= y1)\n        {\n            return false;\n        }\n\n        const nx = s * (lb.y - lt.y);\n        const ny = s * (lt.x - lb.x);\n        const n00 = (nx * x0) + (ny * y0);\n        const n10 = (nx * x1) + (ny * y0);\n        const n01 = (nx * x0) + (ny * y1);\n        const n11 = (nx * x1) + (ny * y1);\n\n        if (Math.max(n00, n10, n01, n11) <= (nx * lt.x) + (ny * lt.y)\n            || Math.min(n00, n10, n01, n11) >= (nx * rb.x) + (ny * rb.y))\n        {\n            return false;\n        }\n\n        const mx = s * (lt.y - rt.y);\n        const my = s * (rt.x - lt.x);\n        const m00 = (mx * x0) + (my * y0);\n        const m10 = (mx * x1) + (my * y0);\n        const m01 = (mx * x0) + (my * y1);\n        const m11 = (mx * x1) + (my * y1);\n\n        if (Math.max(m00, m10, m01, m11) <= (mx * lt.x) + (my * lt.y)\n            || Math.min(m00, m10, m01, m11) >= (mx * rb.x) + (my * rb.y))\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * Pads the rectangle making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     * @returns Returns itself.\n     */\n    pad(paddingX = 0, paddingY = paddingX): this\n    {\n        this.x -= paddingX;\n        this.y -= paddingY;\n\n        this.width += paddingX * 2;\n        this.height += paddingY * 2;\n\n        return this;\n    }\n\n    /**\n     * Fits this rectangle around the passed one.\n     * @param rectangle - The rectangle to fit.\n     * @returns Returns itself.\n     */\n    fit(rectangle: Rectangle): this\n    {\n        const x1 = Math.max(this.x, rectangle.x);\n        const x2 = Math.min(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.max(this.y, rectangle.y);\n        const y2 = Math.min(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = Math.max(x2 - x1, 0);\n        this.y = y1;\n        this.height = Math.max(y2 - y1, 0);\n\n        return this;\n    }\n\n    /**\n     * Enlarges rectangle that way its corners lie on grid\n     * @param resolution - resolution\n     * @param eps - precision\n     * @returns Returns itself.\n     */\n    ceil(resolution = 1, eps = 0.001): this\n    {\n        const x2 = Math.ceil((this.x + this.width - eps) * resolution) / resolution;\n        const y2 = Math.ceil((this.y + this.height - eps) * resolution) / resolution;\n\n        this.x = Math.floor((this.x + eps) * resolution) / resolution;\n        this.y = Math.floor((this.y + eps) * resolution) / resolution;\n\n        this.width = x2 - this.x;\n        this.height = y2 - this.y;\n\n        return this;\n    }\n\n    /**\n     * Enlarges this rectangle to include the passed rectangle.\n     * @param rectangle - The rectangle to include.\n     * @returns Returns itself.\n     */\n    enlarge(rectangle: Rectangle): this\n    {\n        const x1 = Math.min(this.x, rectangle.x);\n        const x2 = Math.max(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.min(this.y, rectangle.y);\n        const y2 = Math.max(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = x2 - x1;\n        this.y = y1;\n        this.height = y2 - y1;\n\n        return this;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Rectangle.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    };\n}\n"], "names": ["Point", "SHAPES", "x0", "y0"], "mappings": ";;AAKA,MAAM,aAAa,CAAC,IAAIA,MAAA,MAAA,GAAS,IAAIA,MAAAA,SAAS,IAAIA,YAAS,GAAA,IAAIA,aAAO;AAkB/D,MAAM,UACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBI,YAAY,IAAqB,GAAG,IAAqB,GAAG,QAAyB,GAAG,SAA0B,GAClH;AACS,SAAA,IAAI,OAAO,CAAC,GACjB,KAAK,IAAI,OAAO,CAAC,GACjB,KAAK,QAAQ,OAAO,KAAK,GACzB,KAAK,SAAS,OAAO,MAAM,GAC3B,KAAK,OAAOC,OAAO,OAAA;AAAA,EACvB;AAAA;AAAA,EAGA,IAAI,OACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAGA,IAAI,QACJ;AACW,WAAA,KAAK,IAAI,KAAK;AAAA,EACzB;AAAA;AAAA,EAGA,IAAI,MACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAGA,IAAI,SACJ;AACW,WAAA,KAAK,IAAI,KAAK;AAAA,EACzB;AAAA;AAAA,EAGA,WAAW,QACX;AACI,WAAO,IAAI,UAAU,GAAG,GAAG,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACA;AACW,WAAA,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,WACT;AACI,WAAA,KAAK,IAAI,UAAU,GACnB,KAAK,IAAI,UAAU,GACnB,KAAK,QAAQ,UAAU,OACvB,KAAK,SAAS,UAAU,QAEjB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WACP;AACI,WAAA,UAAU,IAAI,KAAK,GACnB,UAAU,IAAI,KAAK,GACnB,UAAU,QAAQ,KAAK,OACvB,UAAU,SAAS,KAAK,QAEjB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,GAAW,GACpB;AACQ,WAAA,KAAK,SAAS,KAAK,KAAK,UAAU,IAE3B,KAGP,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,SAE7B,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,EAO7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAW,OAAkB,WAC7B;AACI,QAAI,CAAC,WACL;AACI,YAAMC,MAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK;AAG7C,WAFW,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,KAAK,UAE/CA;AAEC,eAAA;AAGX,YAAMC,MAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK;AAG7C,cAFW,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK,UAEhDA;AAAAA,IAChB;AAEM,UAAA,KAAK,KAAK,MACV,KAAK,KAAK,OACV,KAAK,KAAK,KACV,KAAK,KAAK;AAEZ,QAAA,MAAM,MAAM,MAAM;AAEX,aAAA;AAGX,UAAM,KAAK,WAAW,CAAC,EAAE,IAAI,MAAM,MAAM,MAAM,GAAG,GAC5C,KAAK,WAAW,CAAC,EAAE,IAAI,MAAM,MAAM,MAAM,MAAM,GAC/C,KAAK,WAAW,CAAC,EAAE,IAAI,MAAM,OAAO,MAAM,GAAG,GAC7C,KAAK,WAAW,CAAC,EAAE,IAAI,MAAM,OAAO,MAAM,MAAM;AAEtD,QAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG;AAEpB,aAAA;AAGL,UAAA,IAAI,KAAK,KAAM,UAAU,IAAI,UAAU,IAAM,UAAU,IAAI,UAAU,CAAE;AAY7E,QAVI,MAAM,MAKV,UAAU,MAAM,IAAI,EAAE,GACtB,UAAU,MAAM,IAAI,EAAE,GACtB,UAAU,MAAM,IAAI,EAAE,GACtB,UAAU,MAAM,IAAI,EAAE,GAElB,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,MACjC,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,MACpC,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,MACpC,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK;AAEhC,aAAA;AAGX,UAAM,KAAK,KAAK,GAAG,IAAI,GAAG,IACpB,KAAK,KAAK,GAAG,IAAI,GAAG,IACpB,MAAO,KAAK,KAAO,KAAK,IACxB,MAAO,KAAK,KAAO,KAAK,IACxB,MAAO,KAAK,KAAO,KAAK,IACxB,MAAO,KAAK,KAAO,KAAK;AAE1B,QAAA,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,KAAM,KAAK,GAAG,IAAM,KAAK,GAAG,KACpD,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,KAAM,KAAK,GAAG,IAAM,KAAK,GAAG;AAEnD,aAAA;AAGX,UAAM,KAAK,KAAK,GAAG,IAAI,GAAG,IACpB,KAAK,KAAK,GAAG,IAAI,GAAG,IACpB,MAAO,KAAK,KAAO,KAAK,IACxB,MAAO,KAAK,KAAO,KAAK,IACxB,MAAO,KAAK,KAAO,KAAK,IACxB,MAAO,KAAK,KAAO,KAAK;AAE1B,WAAA,EAAA,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,KAAM,KAAK,GAAG,IAAM,KAAK,GAAG,KACpD,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,KAAM,KAAK,GAAG,IAAM,KAAK,GAAG;AAAA,EAMlE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,WAAW,GAAG,WAAW,UAC7B;AACI,WAAA,KAAK,KAAK,UACV,KAAK,KAAK,UAEV,KAAK,SAAS,WAAW,GACzB,KAAK,UAAU,WAAW,GAEnB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WACJ;AACI,UAAM,KAAK,KAAK,IAAI,KAAK,GAAG,UAAU,CAAC,GACjC,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,UAAU,IAAI,UAAU,KAAK,GAChE,KAAK,KAAK,IAAI,KAAK,GAAG,UAAU,CAAC,GACjC,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,UAAU,IAAI,UAAU,MAAM;AAEnE,WAAA,KAAA,IAAI,IACT,KAAK,QAAQ,KAAK,IAAI,KAAK,IAAI,CAAC,GAChC,KAAK,IAAI,IACT,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,CAAC,GAE1B;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,aAAa,GAAG,MAAM,MAC3B;AACU,UAAA,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ,OAAO,UAAU,IAAI,YAC3D,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS,OAAO,UAAU,IAAI;AAElE,WAAA,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,OAAO,UAAU,IAAI,YACnD,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,OAAO,UAAU,IAAI,YAEnD,KAAK,QAAQ,KAAK,KAAK,GACvB,KAAK,SAAS,KAAK,KAAK,GAEjB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,WACR;AACI,UAAM,KAAK,KAAK,IAAI,KAAK,GAAG,UAAU,CAAC,GACjC,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,UAAU,IAAI,UAAU,KAAK,GAChE,KAAK,KAAK,IAAI,KAAK,GAAG,UAAU,CAAC,GACjC,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,UAAU,IAAI,UAAU,MAAM;AAExE,WAAA,KAAK,IAAI,IACT,KAAK,QAAQ,KAAK,IAClB,KAAK,IAAI,IACT,KAAK,SAAS,KAAK,IAEZ;AAAA,EACX;AACJ;AAII,UAAU,UAAU,WAAW,WAC/B;AACW,SAAA,2BAA2B,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,KAAK,KAAK,WAAW,KAAK,MAAM;AAClG;;"}