{"version": 3, "file": "Ellipse.js", "sources": ["../../src/shapes/Ellipse.ts"], "sourcesContent": ["import { SHAPES } from '../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Ellipse object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Ellipse\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.ELIP\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.ELIP;\n\n    /**\n     * @param x - The X coordinate of the center of this ellipse\n     * @param y - The Y coordinate of the center of this ellipse\n     * @param halfWidth - The half width of this ellipse\n     * @param halfHeight - The half height of this ellipse\n     */\n    constructor(x = 0, y = 0, halfWidth = 0, halfHeight = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = halfWidth;\n        this.height = halfHeight;\n\n        this.type = SHAPES.ELIP;\n    }\n\n    /**\n     * Creates a clone of this Ellipse instance\n     * @returns {PIXI.Ellipse} A copy of the ellipse\n     */\n    clone(): Ellipse\n    {\n        return new Ellipse(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this ellipse\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coords are within this ellipse\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        // normalize the coords to an ellipse with center 0,0\n        let normx = ((x - this.x) / this.width);\n        let normy = ((y - this.y) / this.height);\n\n        normx *= normx;\n        normy *= normy;\n\n        return (normx + normy <= 1);\n    }\n\n    /**\n     * Returns the framing rectangle of the ellipse as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.width, this.y - this.height, this.width, this.height);\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Ellipse.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    };\n}\n"], "names": ["SHAPES", "Rectangle"], "mappings": ";;AAOO,MAAM,QACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BI,YAAY,IAAI,GAAG,IAAI,GAAG,YAAY,GAAG,aAAa,GACtD;AACI,SAAK,IAAI,GACT,KAAK,IAAI,GACT,KAAK,QAAQ,WACb,KAAK,SAAS,YAEd,KAAK,OAAOA,OAAO,OAAA;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACA;AACW,WAAA,IAAI,QAAQ,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,GAAW,GACpB;AACI,QAAI,KAAK,SAAS,KAAK,KAAK,UAAU;AAE3B,aAAA;AAIP,QAAA,SAAU,IAAI,KAAK,KAAK,KAAK,OAC7B,SAAU,IAAI,KAAK,KAAK,KAAK;AAEjC,WAAA,SAAS,OACT,SAAS,OAED,QAAQ,SAAS;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YACA;AACI,WAAO,IAAIC,UAAA,UAAU,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,OAAO,KAAK,MAAM;AAAA,EAC3F;AACJ;AAII,QAAQ,UAAU,WAAW,WAC7B;AACW,SAAA,yBAAyB,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,KAAK,KAAK,WAAW,KAAK,MAAM;AAChG;;"}