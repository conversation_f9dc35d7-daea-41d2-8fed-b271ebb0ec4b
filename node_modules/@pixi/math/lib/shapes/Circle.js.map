{"version": 3, "file": "Circle.js", "sources": ["../../src/shapes/Circle.ts"], "sourcesContent": ["import { SHAPES } from './../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Circle object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Circle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.CIRC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.CIRC;\n\n    /**\n     * @param x - The X coordinate of the center of this circle\n     * @param y - The Y coordinate of the center of this circle\n     * @param radius - The radius of the circle\n     */\n    constructor(x = 0, y = 0, radius = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.radius = radius;\n\n        this.type = SHAPES.CIRC;\n    }\n\n    /**\n     * Creates a clone of this Circle instance\n     * @returns A copy of the Circle\n     */\n    clone(): Circle\n    {\n        return new Circle(this.x, this.y, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this circle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Circle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.radius <= 0)\n        {\n            return false;\n        }\n\n        const r2 = this.radius * this.radius;\n        let dx = (this.x - x);\n        let dy = (this.y - y);\n\n        dx *= dx;\n        dy *= dy;\n\n        return (dx + dy <= r2);\n    }\n\n    /**\n     * Returns the framing rectangle of the circle as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.radius, this.y - this.radius, this.radius * 2, this.radius * 2);\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Circle.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`;\n    };\n}\n"], "names": ["SHAPES", "Rectangle"], "mappings": ";;AAOO,MAAM,OACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBI,YAAY,IAAI,GAAG,IAAI,GAAG,SAAS,GACnC;AACS,SAAA,IAAI,GACT,KAAK,IAAI,GACT,KAAK,SAAS,QAEd,KAAK,OAAOA,OAAAA,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACA;AACI,WAAO,IAAI,OAAO,KAAK,GAAG,KAAK,GAAG,KAAK,MAAM;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,GAAW,GACpB;AACI,QAAI,KAAK,UAAU;AAER,aAAA;AAGL,UAAA,KAAK,KAAK,SAAS,KAAK;AAC9B,QAAI,KAAM,KAAK,IAAI,GACf,KAAM,KAAK,IAAI;AAEnB,WAAA,MAAM,IACN,MAAM,IAEE,KAAK,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YACA;AACI,WAAO,IAAIC,UAAA,UAAU,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAAA,EACrG;AACJ;AAII,OAAO,UAAU,WAAW,WAC5B;AACW,SAAA,wBAAwB,KAAK,CAAC,MAAM,KAAK,CAAC,WAAW,KAAK,MAAM;AAC3E;;"}