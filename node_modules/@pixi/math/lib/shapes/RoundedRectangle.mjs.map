{"version": 3, "file": "RoundedRectangle.mjs", "sources": ["../../src/shapes/RoundedRectangle.ts"], "sourcesContent": ["import { SHAPES } from '../const';\n\n/**\n * The Rounded Rectangle object is an area that has nice rounded corners, as indicated by its\n * top-left corner point (x, y) and by its width and its height and its radius.\n * @memberof PIXI\n */\nexport class RoundedRectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /** @default 20 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RREC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RREC;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rounded rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rounded rectangle\n     * @param width - The overall width of this rounded rectangle\n     * @param height - The overall height of this rounded rectangle\n     * @param radius - Controls the radius of the rounded corners\n     */\n    constructor(x = 0, y = 0, width = 0, height = 0, radius = 20)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n        this.radius = radius;\n        this.type = SHAPES.RREC;\n    }\n\n    /**\n     * Creates a clone of this Rounded Rectangle.\n     * @returns - A copy of the rounded rectangle.\n     */\n    clone(): RoundedRectangle\n    {\n        return new RoundedRectangle(this.x, this.y, this.width, this.height, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rounded Rectangle\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this Rounded Rectangle.\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n        if (x >= this.x && x <= this.x + this.width)\n        {\n            if (y >= this.y && y <= this.y + this.height)\n            {\n                const radius = Math.max(0, Math.min(this.radius, Math.min(this.width, this.height) / 2));\n\n                if ((y >= this.y + radius && y <= this.y + this.height - radius)\n                || (x >= this.x + radius && x <= this.x + this.width - radius))\n                {\n                    return true;\n                }\n                let dx = x - (this.x + radius);\n                let dy = y - (this.y + radius);\n                const radius2 = radius * radius;\n\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + this.width - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dy = y - (this.y + this.height - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    RoundedRectangle.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}`\n            + `width=${this.width} height=${this.height} radius=${this.radius}]`;\n    };\n}\n"], "names": [], "mappings": ";AAOO,MAAM,iBACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BI,YAAY,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,IAC1D;AACI,SAAK,IAAI,GACT,KAAK,IAAI,GACT,KAAK,QAAQ,OACb,KAAK,SAAS,QACd,KAAK,SAAS,QACd,KAAK,OAAO,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACA;AACW,WAAA,IAAI,iBAAiB,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,QAAQ,KAAK,MAAM;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,GAAW,GACpB;AACI,QAAI,KAAK,SAAS,KAAK,KAAK,UAAU;AAE3B,aAAA;AAEX,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,SAE9B,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,QACtC;AACI,YAAM,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAEvF,UAAK,KAAK,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,KAAK,SAAS,UACrD,KAAK,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,KAAK,QAAQ;AAE5C,eAAA;AAEP,UAAA,KAAK,KAAK,KAAK,IAAI,SACnB,KAAK,KAAK,KAAK,IAAI;AACvB,YAAM,UAAU,SAAS;AAiBzB,UAfK,KAAK,KAAO,KAAK,MAAO,YAI7B,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,SAC3B,KAAK,KAAO,KAAK,MAAO,aAI7B,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,SAC5B,KAAK,KAAO,KAAK,MAAO,aAI7B,KAAK,KAAK,KAAK,IAAI,SACd,KAAK,KAAO,KAAK,MAAO;AAElB,eAAA;AAAA,IAEf;AAGG,WAAA;AAAA,EACX;AACJ;AAII,iBAAiB,UAAU,WAAW,WACtC;AACI,SAAO,kCAAkC,KAAK,CAAC,MAAM,KAAK,CAAC,SAC5C,KAAK,KAAK,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM;AACzE;"}