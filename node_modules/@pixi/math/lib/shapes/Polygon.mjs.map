{"version": 3, "file": "Polygon.mjs", "sources": ["../../src/shapes/Polygon.ts"], "sourcesContent": ["import { SHAPES } from '../const';\n\nimport type { IPointData } from '../IPointData';\n\n/**\n * A class to define a shape via user defined coordinates.\n * @memberof PIXI\n */\nexport class Polygon\n{\n    /** An array of the points of this polygon. */\n    public points: number[];\n\n    /** `false` after moveTo, `true` after `closePath`. In all other cases it is `true`. */\n    public closeStroke: boolean;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.POLY\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.POLY;\n\n    constructor(points: IPointData[] | number[]);\n    constructor(...points: IPointData[] | number[]);\n\n    /**\n     * @param {PIXI.IPointData[]|number[]} points - This can be an array of Points\n     *  that form the polygon, a flat array of numbers that will be interpreted as [x,y, x,y, ...], or\n     *  the arguments passed can be all the points of the polygon e.g.\n     *  `new Polygon(new Point(), new Point(), ...)`, or the arguments passed can be flat\n     *  x,y values e.g. `new Polygon(x,y, x,y, x,y, ...)` where `x` and `y` are Numbers.\n     */\n    constructor(...points: any[])\n    {\n        let flat: IPointData[] | number[] = Array.isArray(points[0]) ? points[0] : points;\n\n        // if this is an array of points, convert it to a flat array of numbers\n        if (typeof flat[0] !== 'number')\n        {\n            const p: number[] = [];\n\n            for (let i = 0, il = flat.length; i < il; i++)\n            {\n                p.push((flat[i] as IPointData).x, (flat[i] as IPointData).y);\n            }\n\n            flat = p;\n        }\n\n        this.points = flat as number[];\n        this.type = SHAPES.POLY;\n        this.closeStroke = true;\n    }\n\n    /**\n     * Creates a clone of this polygon.\n     * @returns - A copy of the polygon.\n     */\n    clone(): Polygon\n    {\n        const points = this.points.slice();\n        const polygon = new Polygon(points);\n\n        polygon.closeStroke = this.closeStroke;\n\n        return polygon;\n    }\n\n    /**\n     * Checks whether the x and y coordinates passed to this function are contained within this polygon.\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this polygon.\n     */\n    contains(x: number, y: number): boolean\n    {\n        let inside = false;\n\n        // use some raycasting to test hits\n        // https://github.com/substack/point-in-polygon/blob/master/index.js\n        const length = this.points.length / 2;\n\n        for (let i = 0, j = length - 1; i < length; j = i++)\n        {\n            const xi = this.points[i * 2];\n            const yi = this.points[(i * 2) + 1];\n            const xj = this.points[j * 2];\n            const yj = this.points[(j * 2) + 1];\n            const intersect = ((yi > y) !== (yj > y)) && (x < ((xj - xi) * ((y - yi) / (yj - yi))) + xi);\n\n            if (intersect)\n            {\n                inside = !inside;\n            }\n        }\n\n        return inside;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Polygon.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Polygon`\n            + `closeStroke=${this.closeStroke}`\n            + `points=${this.points.reduce((pointsDesc, currentPoint) => `${pointsDesc}, ${currentPoint}`, '')}]`;\n    };\n}\n"], "names": [], "mappings": ";AAQO,MAAM,QACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBI,eAAe,QACf;AACQ,QAAA,OAAgC,MAAM,QAAQ,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI;AAG3E,QAAI,OAAO,KAAK,CAAC,KAAM,UACvB;AACI,YAAM,IAAc,CAAA;AAEpB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI;AAEpC,UAAA,KAAM,KAAK,CAAC,EAAiB,GAAI,KAAK,CAAC,EAAiB,CAAC;AAGxD,aAAA;AAAA,IACX;AAEA,SAAK,SAAS,MACd,KAAK,OAAO,OAAO,MACnB,KAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACA;AACU,UAAA,SAAS,KAAK,OAAO,SACrB,UAAU,IAAI,QAAQ,MAAM;AAE1B,WAAA,QAAA,cAAc,KAAK,aAEpB;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,GAAW,GACpB;AACI,QAAI,SAAS;AAIP,UAAA,SAAS,KAAK,OAAO,SAAS;AAE3B,aAAA,IAAI,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,IAAI,KAChD;AACU,YAAA,KAAK,KAAK,OAAO,IAAI,CAAC,GACtB,KAAK,KAAK,OAAQ,IAAI,IAAK,CAAC,GAC5B,KAAK,KAAK,OAAO,IAAI,CAAC,GACtB,KAAK,KAAK,OAAQ,IAAI,IAAK,CAAC;AACd,WAAK,KAAQ,KAAK,KAAQ,KAAM,KAAK,QAAQ,IAAI,OAAO,KAAK,OAAQ,OAIrF,SAAS,CAAC;AAAA,IAElB;AAEO,WAAA;AAAA,EACX;AACJ;AAII,QAAQ,UAAU,WAAW,WAC7B;AACI,SAAO,kCACc,KAAK,WAAW,UACrB,KAAK,OAAO,OAAO,CAAC,YAAY,iBAAiB,GAAG,UAAU,KAAK,YAAY,IAAI,EAAE,CAAC;AAC1G;"}