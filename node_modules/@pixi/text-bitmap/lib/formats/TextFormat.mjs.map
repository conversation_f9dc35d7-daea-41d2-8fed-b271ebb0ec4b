{"version": 3, "file": "TextFormat.mjs", "sources": ["../../src/formats/TextFormat.ts"], "sourcesContent": ["import { BitmapFontData } from '../BitmapFontData';\n\n/**\n * Internal data format used to convert to BitmapFontData.\n * @private\n */\nexport interface IBitmapFontRawData\n{\n    info: {\n        face: string;\n        size: string;\n    }[];\n    common: { lineHeight: string }[];\n    page: {\n        id: string;\n        file: string;\n    }[];\n    chars: {\n        count: number;\n    }[];\n    char: {\n        id: string;\n        page: string;\n        x: string;\n        y: string;\n        width: string;\n        height: string;\n        xoffset: string;\n        yoffset: string;\n        xadvance: string;\n    }[];\n    kernings?: {\n        count: number;\n    }[];\n    kerning?: {\n        first: string;\n        second: string;\n        amount: string;\n    }[];\n    distanceField?: {\n        fieldType: string;\n        distanceRange: string;\n    }[]\n}\n\n/**\n * BitmapFont format that's Text-based.\n * @private\n */\nexport class TextFormat\n{\n    /**\n     * Check if resource refers to txt font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: string | XMLDocument | BitmapFontData): boolean\n    {\n        return typeof data === 'string' && data.startsWith('info face=');\n    }\n\n    /**\n     * Convert text font data to a javascript object.\n     * @param txt - Raw string data to be converted\n     * @returns - Parsed font data\n     */\n    static parse(txt: string): BitmapFontData\n    {\n        // Retrieve data item\n        const items = txt.match(/^[a-z]+\\s+.+$/gm);\n        const rawData: IBitmapFontRawData = {\n            info: [],\n            common: [],\n            page: [],\n            char: [],\n            chars: [],\n            kerning: [],\n            kernings: [],\n            distanceField: [],\n        };\n\n        for (const i in items)\n        {\n            // Extract item name\n            const name = items[i].match(/^[a-z]+/gm)[0] as keyof BitmapFontData;\n\n            // Extract item attribute list as string ex.: \"width=10\"\n            const attributeList = items[i].match(/[a-zA-Z]+=([^\\s\"']+|\"([^\"]*)\")/gm);\n\n            // Convert attribute list into an object\n            const itemData: any = {};\n\n            for (const i in attributeList)\n            {\n                // Split key-value pairs\n                const split = attributeList[i].split('=');\n                const key = split[0];\n\n                // Remove eventual quotes from value\n                const strValue = split[1].replace(/\"/gm, '');\n\n                // Try to convert value into float\n                const floatValue = parseFloat(strValue);\n\n                // Use string value case float value is NaN\n                const value = isNaN(floatValue) ? strValue : floatValue;\n\n                itemData[key] = value;\n            }\n\n            // Push current item to the resulting data\n            rawData[name].push(itemData);\n        }\n\n        const font = new BitmapFontData();\n\n        rawData.info.forEach((info) => font.info.push({\n            face: info.face,\n            size: parseInt(info.size, 10),\n        }));\n\n        rawData.common.forEach((common) => font.common.push({\n            lineHeight: parseInt(common.lineHeight, 10),\n        }));\n\n        rawData.page.forEach((page) => font.page.push({\n            id: parseInt(page.id, 10),\n            file: page.file,\n        }));\n\n        rawData.char.forEach((char) => font.char.push({\n            id: parseInt(char.id, 10),\n            page: parseInt(char.page, 10),\n            x: parseInt(char.x, 10),\n            y: parseInt(char.y, 10),\n            width: parseInt(char.width, 10),\n            height: parseInt(char.height, 10),\n            xoffset: parseInt(char.xoffset, 10),\n            yoffset: parseInt(char.yoffset, 10),\n            xadvance: parseInt(char.xadvance, 10),\n        }));\n\n        rawData.kerning.forEach((kerning) => font.kerning.push({\n            first: parseInt(kerning.first, 10),\n            second: parseInt(kerning.second, 10),\n            amount: parseInt(kerning.amount, 10),\n        }));\n\n        rawData.distanceField.forEach((df) => font.distanceField.push({\n            distanceRange: parseInt(df.distanceRange, 10),\n            fieldType: df.fieldType,\n        }));\n\n        return font;\n    }\n}\n"], "names": ["i"], "mappings": ";AAiDO,MAAM,WACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMI,OAAO,KAAK,MACZ;AACI,WAAO,OAAO,QAAS,YAAY,KAAK,WAAW,YAAY;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM,KACb;AAEI,UAAM,QAAQ,IAAI,MAAM,iBAAiB,GACnC,UAA8B;AAAA,MAChC,MAAM,CAAC;AAAA,MACP,QAAQ,CAAC;AAAA,MACT,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,MACR,SAAS,CAAC;AAAA,MACV,UAAU,CAAC;AAAA,MACX,eAAe,CAAC;AAAA,IAAA;AAGpB,eAAW,KAAK,OAChB;AAEI,YAAM,OAAO,MAAM,CAAC,EAAE,MAAM,WAAW,EAAE,CAAC,GAGpC,gBAAgB,MAAM,CAAC,EAAE,MAAM,kCAAkC,GAGjE,WAAgB;AAEtB,iBAAWA,MAAK,eAChB;AAEU,cAAA,QAAQ,cAAcA,EAAC,EAAE,MAAM,GAAG,GAClC,MAAM,MAAM,CAAC,GAGb,WAAW,MAAM,CAAC,EAAE,QAAQ,OAAO,EAAE,GAGrC,aAAa,WAAW,QAAQ,GAGhC,QAAQ,MAAM,UAAU,IAAI,WAAW;AAE7C,iBAAS,GAAG,IAAI;AAAA,MACpB;AAGQ,cAAA,IAAI,EAAE,KAAK,QAAQ;AAAA,IAC/B;AAEM,UAAA,OAAO,IAAI;AAEjB,WAAA,QAAQ,KAAK,QAAQ,CAAC,SAAS,KAAK,KAAK,KAAK;AAAA,MAC1C,MAAM,KAAK;AAAA,MACX,MAAM,SAAS,KAAK,MAAM,EAAE;AAAA,IAAA,CAC/B,CAAC,GAEF,QAAQ,OAAO,QAAQ,CAAC,WAAW,KAAK,OAAO,KAAK;AAAA,MAChD,YAAY,SAAS,OAAO,YAAY,EAAE;AAAA,IAAA,CAC7C,CAAC,GAEF,QAAQ,KAAK,QAAQ,CAAC,SAAS,KAAK,KAAK,KAAK;AAAA,MAC1C,IAAI,SAAS,KAAK,IAAI,EAAE;AAAA,MACxB,MAAM,KAAK;AAAA,IAAA,CACd,CAAC,GAEF,QAAQ,KAAK,QAAQ,CAAC,SAAS,KAAK,KAAK,KAAK;AAAA,MAC1C,IAAI,SAAS,KAAK,IAAI,EAAE;AAAA,MACxB,MAAM,SAAS,KAAK,MAAM,EAAE;AAAA,MAC5B,GAAG,SAAS,KAAK,GAAG,EAAE;AAAA,MACtB,GAAG,SAAS,KAAK,GAAG,EAAE;AAAA,MACtB,OAAO,SAAS,KAAK,OAAO,EAAE;AAAA,MAC9B,QAAQ,SAAS,KAAK,QAAQ,EAAE;AAAA,MAChC,SAAS,SAAS,KAAK,SAAS,EAAE;AAAA,MAClC,SAAS,SAAS,KAAK,SAAS,EAAE;AAAA,MAClC,UAAU,SAAS,KAAK,UAAU,EAAE;AAAA,IAAA,CACvC,CAAC,GAEF,QAAQ,QAAQ,QAAQ,CAAC,YAAY,KAAK,QAAQ,KAAK;AAAA,MACnD,OAAO,SAAS,QAAQ,OAAO,EAAE;AAAA,MACjC,QAAQ,SAAS,QAAQ,QAAQ,EAAE;AAAA,MACnC,QAAQ,SAAS,QAAQ,QAAQ,EAAE;AAAA,IAAA,CACtC,CAAC,GAEF,QAAQ,cAAc,QAAQ,CAAC,OAAO,KAAK,cAAc,KAAK;AAAA,MAC1D,eAAe,SAAS,GAAG,eAAe,EAAE;AAAA,MAC5C,WAAW,GAAG;AAAA,IAAA,CACjB,CAAC,GAEK;AAAA,EACX;AACJ;"}