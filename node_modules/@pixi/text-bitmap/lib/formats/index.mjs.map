{"version": 3, "file": "index.mjs", "sources": ["../../src/formats/index.ts"], "sourcesContent": ["import { TextFormat } from './TextFormat';\nimport { XMLFormat } from './XMLFormat';\nimport { XMLStringFormat } from './XMLStringFormat';\n\nimport type { BitmapFontData } from '../BitmapFontData';\n\n// Registered formats, maybe make this extensible in the future?\nconst formats = [\n    TextFormat,\n    XMLFormat,\n    XMLStringFormat,\n] as const;\n\n/**\n * Auto-detect BitmapFont parsing format based on data.\n * @private\n * @param {any} data - Data to detect format\n * @returns {any} Format or null\n */\nexport function autoDetectFormat(data: string | XMLDocument | BitmapFontData): typeof formats[number] | null\n{\n    for (let i = 0; i < formats.length; i++)\n    {\n        if (formats[i].test(data))\n        {\n            return formats[i];\n        }\n    }\n\n    return null;\n}\n\nexport type { IBitmapFontRawData } from './TextFormat';\nexport { TextFormat, XMLFormat, XMLStringFormat };\n"], "names": [], "mappings": ";;;AAOA,MAAM,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACJ;AAQO,SAAS,iBAAiB,MACjC;AACI,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAEhC,QAAI,QAAQ,CAAC,EAAE,KAAK,IAAI;AAEpB,aAAO,QAAQ,CAAC;AAIjB,SAAA;AACX;"}