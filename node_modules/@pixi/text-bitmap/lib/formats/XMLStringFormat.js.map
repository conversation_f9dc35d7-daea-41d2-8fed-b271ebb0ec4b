{"version": 3, "file": "XMLStringFormat.js", "sources": ["../../src/formats/XMLStringFormat.ts"], "sourcesContent": ["import { settings } from '@pixi/core';\nimport { XMLFormat } from './XMLFormat';\n\nimport type { BitmapFontData } from '../BitmapFontData';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLStringFormat\n{\n    /**\n     * Check if resource refers to text xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: string | XMLDocument | BitmapFontData): boolean\n    {\n        if (typeof data === 'string' && data.includes('<font>'))\n        {\n            return XMLFormat.test(settings.ADAPTER.parseXML(data));\n        }\n\n        return false;\n    }\n\n    /**\n     * Convert the text XML into BitmapFontData that we can use.\n     * @param xmlTxt\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xmlTxt: string): BitmapFontData\n    {\n        return XMLFormat.parse(settings.ADAPTER.parseXML(xmlTxt));\n    }\n}\n"], "names": ["XMLFormat", "settings"], "mappings": ";;AASO,MAAM,gBACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMI,OAAO,KAAK,MACZ;AACI,WAAI,OAAO,QAAS,YAAY,KAAK,SAAS,QAAQ,IAE3CA,UAAAA,UAAU,KAAKC,KAAAA,SAAS,QAAQ,SAAS,IAAI,CAAC,IAGlD;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM,QACb;AACI,WAAOD,UAAAA,UAAU,MAAMC,KAAA,SAAS,QAAQ,SAAS,MAAM,CAAC;AAAA,EAC5D;AACJ;;"}