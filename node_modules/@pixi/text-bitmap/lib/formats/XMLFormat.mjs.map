{"version": 3, "file": "XMLFormat.mjs", "sources": ["../../src/formats/XMLFormat.ts"], "sourcesContent": ["import { BitmapFontData } from '../BitmapFontData';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLFormat\n{\n    /**\n     * Check if resource refers to xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: string | XMLDocument | BitmapFontData): boolean\n    {\n        const xml = data as Document;\n\n        return typeof data !== 'string'\n            && 'getElementsByTagName' in data\n            && xml.getElementsByTagName('page').length\n            && xml.getElementsByTagName('info')[0].getAttribute('face') !== null;\n    }\n\n    /**\n     * Convert the XML into BitmapFontData that we can use.\n     * @param xml\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xml: Document): BitmapFontData\n    {\n        const data = new BitmapFontData();\n        const info = xml.getElementsByTagName('info');\n        const common = xml.getElementsByTagName('common');\n        const page = xml.getElementsByTagName('page');\n        const char = xml.getElementsByTagName('char');\n        const kerning = xml.getElementsByTagName('kerning');\n        const distanceField = xml.getElementsByTagName('distanceField');\n\n        for (let i = 0; i < info.length; i++)\n        {\n            data.info.push({\n                face: info[i].getAttribute('face'),\n                size: parseInt(info[i].getAttribute('size'), 10),\n            });\n        }\n\n        for (let i = 0; i < common.length; i++)\n        {\n            data.common.push({\n                lineHeight: parseInt(common[i].getAttribute('lineHeight'), 10),\n            });\n        }\n\n        for (let i = 0; i < page.length; i++)\n        {\n            data.page.push({\n                id: parseInt(page[i].getAttribute('id'), 10) || 0,\n                file: page[i].getAttribute('file'),\n            });\n        }\n\n        for (let i = 0; i < char.length; i++)\n        {\n            const letter = char[i];\n\n            data.char.push({\n                id: parseInt(letter.getAttribute('id'), 10),\n                page: parseInt(letter.getAttribute('page'), 10) || 0,\n                x: parseInt(letter.getAttribute('x'), 10),\n                y: parseInt(letter.getAttribute('y'), 10),\n                width: parseInt(letter.getAttribute('width'), 10),\n                height: parseInt(letter.getAttribute('height'), 10),\n                xoffset: parseInt(letter.getAttribute('xoffset'), 10),\n                yoffset: parseInt(letter.getAttribute('yoffset'), 10),\n                xadvance: parseInt(letter.getAttribute('xadvance'), 10),\n            });\n        }\n\n        for (let i = 0; i < kerning.length; i++)\n        {\n            data.kerning.push({\n                first: parseInt(kerning[i].getAttribute('first'), 10),\n                second: parseInt(kerning[i].getAttribute('second'), 10),\n                amount: parseInt(kerning[i].getAttribute('amount'), 10),\n            });\n        }\n\n        for (let i = 0; i < distanceField.length; i++)\n        {\n            data.distanceField.push({\n                fieldType: distanceField[i].getAttribute('fieldType'),\n                distanceRange: parseInt(distanceField[i].getAttribute('distanceRange'), 10),\n            });\n        }\n\n        return data;\n    }\n}\n"], "names": [], "mappings": ";AAMO,MAAM,UACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMI,OAAO,KAAK,MACZ;AACI,UAAM,MAAM;AAEZ,WAAO,OAAO,QAAS,YAChB,0BAA0B,QAC1B,IAAI,qBAAqB,MAAM,EAAE,UACjC,IAAI,qBAAqB,MAAM,EAAE,CAAC,EAAE,aAAa,MAAM,MAAM;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM,KACb;AACI,UAAM,OAAO,IAAI,eAAe,GAC1B,OAAO,IAAI,qBAAqB,MAAM,GACtC,SAAS,IAAI,qBAAqB,QAAQ,GAC1C,OAAO,IAAI,qBAAqB,MAAM,GACtC,OAAO,IAAI,qBAAqB,MAAM,GACtC,UAAU,IAAI,qBAAqB,SAAS,GAC5C,gBAAgB,IAAI,qBAAqB,eAAe;AAE9D,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAE7B,WAAK,KAAK,KAAK;AAAA,QACX,MAAM,KAAK,CAAC,EAAE,aAAa,MAAM;AAAA,QACjC,MAAM,SAAS,KAAK,CAAC,EAAE,aAAa,MAAM,GAAG,EAAE;AAAA,MAAA,CAClD;AAGL,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAE/B,WAAK,OAAO,KAAK;AAAA,QACb,YAAY,SAAS,OAAO,CAAC,EAAE,aAAa,YAAY,GAAG,EAAE;AAAA,MAAA,CAChE;AAGL,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAE7B,WAAK,KAAK,KAAK;AAAA,QACX,IAAI,SAAS,KAAK,CAAC,EAAE,aAAa,IAAI,GAAG,EAAE,KAAK;AAAA,QAChD,MAAM,KAAK,CAAC,EAAE,aAAa,MAAM;AAAA,MAAA,CACpC;AAGL,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KACjC;AACU,YAAA,SAAS,KAAK,CAAC;AAErB,WAAK,KAAK,KAAK;AAAA,QACX,IAAI,SAAS,OAAO,aAAa,IAAI,GAAG,EAAE;AAAA,QAC1C,MAAM,SAAS,OAAO,aAAa,MAAM,GAAG,EAAE,KAAK;AAAA,QACnD,GAAG,SAAS,OAAO,aAAa,GAAG,GAAG,EAAE;AAAA,QACxC,GAAG,SAAS,OAAO,aAAa,GAAG,GAAG,EAAE;AAAA,QACxC,OAAO,SAAS,OAAO,aAAa,OAAO,GAAG,EAAE;AAAA,QAChD,QAAQ,SAAS,OAAO,aAAa,QAAQ,GAAG,EAAE;AAAA,QAClD,SAAS,SAAS,OAAO,aAAa,SAAS,GAAG,EAAE;AAAA,QACpD,SAAS,SAAS,OAAO,aAAa,SAAS,GAAG,EAAE;AAAA,QACpD,UAAU,SAAS,OAAO,aAAa,UAAU,GAAG,EAAE;AAAA,MAAA,CACzD;AAAA,IACL;AAEA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAEhC,WAAK,QAAQ,KAAK;AAAA,QACd,OAAO,SAAS,QAAQ,CAAC,EAAE,aAAa,OAAO,GAAG,EAAE;AAAA,QACpD,QAAQ,SAAS,QAAQ,CAAC,EAAE,aAAa,QAAQ,GAAG,EAAE;AAAA,QACtD,QAAQ,SAAS,QAAQ,CAAC,EAAE,aAAa,QAAQ,GAAG,EAAE;AAAA,MAAA,CACzD;AAGL,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ;AAEtC,WAAK,cAAc,KAAK;AAAA,QACpB,WAAW,cAAc,CAAC,EAAE,aAAa,WAAW;AAAA,QACpD,eAAe,SAAS,cAAc,CAAC,EAAE,aAAa,eAAe,GAAG,EAAE;AAAA,MAAA,CAC7E;AAGE,WAAA;AAAA,EACX;AACJ;"}