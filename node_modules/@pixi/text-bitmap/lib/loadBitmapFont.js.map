{"version": 3, "file": "loadBitmapFont.js", "sources": ["../src/loadBitmapFont.ts"], "sourcesContent": ["import { copySearchParams, LoaderParserPriority } from '@pixi/assets';\nimport { extensions, ExtensionType, settings, utils } from '@pixi/core';\nimport { BitmapFont } from './BitmapFont';\nimport { TextFormat, XMLStringFormat } from './formats';\n\nimport type { Loader, LoaderParser, ResolvedAsset } from '@pixi/assets';\nimport type { Texture } from '@pixi/core';\nimport type { BitmapFontData } from './BitmapFontData';\n\nconst validExtensions = ['.xml', '.fnt'];\n\n/** simple loader plugin for loading in bitmap fonts! */\nexport const loadBitmapFont = {\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.Normal,\n    },\n\n    name: 'loadBitmapFont',\n\n    test(url: string): boolean\n    {\n        return validExtensions.includes(utils.path.extname(url).toLowerCase());\n    },\n\n    async testParse(data: string): Promise<boolean>\n    {\n        return TextFormat.test(data) || XMLStringFormat.test(data);\n    },\n\n    async parse(asset: string, data: ResolvedAsset, loader: Loader): Promise<BitmapFont>\n    {\n        const fontData: BitmapFontData = TextFormat.test(asset)\n            ? TextFormat.parse(asset)\n            : XMLStringFormat.parse(asset);\n\n        const { src } = data;\n        const { page: pages } = fontData;\n        const textureUrls = [];\n\n        for (let i = 0; i < pages.length; ++i)\n        {\n            const pageFile = pages[i].file;\n            let imagePath = utils.path.join(utils.path.dirname(src), pageFile);\n\n            imagePath = copySearchParams(imagePath, src);\n\n            textureUrls.push(imagePath);\n        }\n\n        const loadedTextures = await loader.load<Texture>(textureUrls);\n        const textures = textureUrls.map((url) => loadedTextures[url]);\n\n        return BitmapFont.install(fontData, textures, true);\n    },\n\n    async load(url: string, _options: ResolvedAsset): Promise<string>\n    {\n        const response = await settings.ADAPTER.fetch(url);\n\n        return response.text();\n    },\n\n    unload(bitmapFont: BitmapFont): void\n    {\n        bitmapFont.destroy();\n    }\n} as LoaderParser<BitmapFont | string>;\n\nextensions.add(loadBitmapFont);\n"], "names": ["ExtensionType", "LoaderParserPriority", "utils", "TextFormat", "XMLStringFormat", "copySearchParams", "BitmapFont", "settings", "extensions"], "mappings": ";;;;AASA,MAAM,kBAAkB,CAAC,QAAQ,MAAM,GAG1B,iBAAiB;AAAA,EAC1B,WAAW;AAAA,IACP,MAAMA,KAAc,cAAA;AAAA,IACpB,UAAUC,OAAqB,qBAAA;AAAA,EACnC;AAAA,EAEA,MAAM;AAAA,EAEN,KAAK,KACL;AACW,WAAA,gBAAgB,SAASC,WAAM,KAAK,QAAQ,GAAG,EAAE,aAAa;AAAA,EACzE;AAAA,EAEA,MAAM,UAAU,MAChB;AACI,WAAOC,WAAAA,WAAW,KAAK,IAAI,KAAKC,gBAAAA,gBAAgB,KAAK,IAAI;AAAA,EAC7D;AAAA,EAEA,MAAM,MAAM,OAAe,MAAqB,QAChD;AACU,UAAA,WAA2BD,sBAAW,KAAK,KAAK,IAChDA,WAAAA,WAAW,MAAM,KAAK,IACtBC,gCAAgB,MAAM,KAAK,GAE3B,EAAE,IAAI,IAAI,MACV,EAAE,MAAM,UAAU,UAClB,cAAc;AAEpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GACpC;AACU,YAAA,WAAW,MAAM,CAAC,EAAE;AACtB,UAAA,YAAYF,KAAAA,MAAM,KAAK,KAAKA,KAAAA,MAAM,KAAK,QAAQ,GAAG,GAAG,QAAQ;AAEjE,kBAAYG,wBAAiB,WAAW,GAAG,GAE3C,YAAY,KAAK,SAAS;AAAA,IAC9B;AAEA,UAAM,iBAAiB,MAAM,OAAO,KAAc,WAAW,GACvD,WAAW,YAAY,IAAI,CAAC,QAAQ,eAAe,GAAG,CAAC;AAE7D,WAAOC,WAAAA,WAAW,QAAQ,UAAU,UAAU,EAAI;AAAA,EACtD;AAAA,EAEA,MAAM,KAAK,KAAa,UACxB;AAGI,YAFiB,MAAMC,KAAAA,SAAS,QAAQ,MAAM,GAAG,GAEjC;EACpB;AAAA,EAEA,OAAO,YACP;AACI,eAAW,QAAQ;AAAA,EACvB;AACJ;AAEAC,KAAAA,WAAW,IAAI,cAAc;;"}