{"version": 3, "file": "extractCharCode.js", "sources": ["../../src/utils/extractCharCode.ts"], "sourcesContent": ["/**\n * Ponyfill for IE because it doesn't support `codePointAt`\n * @param str\n * @private\n */\nexport function extractCharCode(str: string): number\n{\n    return str.codePointAt ? str.codePointAt(0) : str.charCodeAt(0);\n}\n"], "names": [], "mappings": ";AAKO,SAAS,gBAAgB,KAChC;AACW,SAAA,IAAI,cAAc,IAAI,YAAY,CAAC,IAAI,IAAI,WAAW,CAAC;AAClE;;"}