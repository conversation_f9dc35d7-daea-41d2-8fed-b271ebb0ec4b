import { drawGlyph } from "./drawGlyph.mjs";
import { extractCharCode } from "./extractCharCode.mjs";
import { generateFillStyle } from "./generateFillStyle.mjs";
import { resolveCharacters } from "./resolveCharacters.mjs";
import { splitTextToCharacters } from "./splitTextToCharacters.mjs";
export {
  drawGlyph,
  extractCharCode,
  generateFillStyle,
  resolveCharacters,
  splitTextToCharacters
};
//# sourceMappingURL=index.mjs.map
