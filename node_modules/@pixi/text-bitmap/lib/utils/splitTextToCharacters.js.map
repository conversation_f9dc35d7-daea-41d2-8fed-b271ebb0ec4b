{"version": 3, "file": "splitTextToCharacters.js", "sources": ["../../src/utils/splitTextToCharacters.ts"], "sourcesContent": ["/**\n * Ponyfill for IE because it doesn't support `Array.from`\n * @param text\n * @private\n */\nexport function splitTextToCharacters(text: string): string[]\n{\n    return Array.from ? Array.from(text) : text.split('');\n}\n"], "names": [], "mappings": ";AAKO,SAAS,sBAAsB,MACtC;AACW,SAAA,MAAM,OAAO,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,EAAE;AACxD;;"}