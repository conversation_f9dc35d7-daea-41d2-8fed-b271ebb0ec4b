{"version": 3, "file": "drawGlyph.mjs", "sources": ["../../src/utils/drawGlyph.ts"], "sourcesContent": ["import { Color } from '@pixi/core';\nimport { generateFillStyle } from './generateFillStyle';\n\nimport type { ICanvas, ICanvasRenderingContext2D } from '@pixi/core';\nimport type { TextMetrics, TextStyle } from '@pixi/text';\n\n// TODO: Prevent code duplication b/w drawGlyph & Text#updateText\n\n/**\n * Draws the glyph `metrics.text` on the given canvas.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {PIXI.ICanvas} canvas\n * @param {PIXI.ICanvasRenderingContext2D} context\n * @param {TextMetrics} metrics\n * @param {number} x\n * @param {number} y\n * @param {number} resolution\n * @param {TextStyle} style\n */\nexport function drawGlyph(\n    canvas: ICanvas,\n    context: ICanvasRenderingContext2D,\n    metrics: TextMetrics,\n    x: number,\n    y: number,\n    resolution: number,\n    style: TextStyle\n): void\n{\n    const char = metrics.text;\n    const fontProperties = metrics.fontProperties;\n\n    context.translate(x, y);\n    context.scale(resolution, resolution);\n\n    const tx = style.strokeThickness / 2;\n    const ty = -(style.strokeThickness / 2);\n\n    context.font = style.toFontString();\n    context.lineWidth = style.strokeThickness;\n    context.textBaseline = style.textBaseline;\n    context.lineJoin = style.lineJoin;\n    context.miterLimit = style.miterLimit;\n\n    // set canvas text styles\n    context.fillStyle = generateFillStyle(canvas, context, style, resolution, [char], metrics);\n    context.strokeStyle = style.stroke as string;\n\n    if (style.dropShadow)\n    {\n        const dropShadowColor = style.dropShadowColor;\n        const dropShadowBlur = style.dropShadowBlur * resolution;\n        const dropShadowDistance = style.dropShadowDistance * resolution;\n\n        context.shadowColor = Color.shared\n            .setValue(dropShadowColor)\n            .setAlpha(style.dropShadowAlpha)\n            .toRgbaString();\n        context.shadowBlur = dropShadowBlur;\n        context.shadowOffsetX = Math.cos(style.dropShadowAngle) * dropShadowDistance;\n        context.shadowOffsetY = Math.sin(style.dropShadowAngle) * dropShadowDistance;\n    }\n    else\n    {\n        context.shadowColor = 'black';\n        context.shadowBlur = 0;\n        context.shadowOffsetX = 0;\n        context.shadowOffsetY = 0;\n    }\n\n    if (style.stroke && style.strokeThickness)\n    {\n        context.strokeText(char, tx, ty + metrics.lineHeight - fontProperties.descent);\n    }\n    if (style.fill)\n    {\n        context.fillText(char, tx, ty + metrics.lineHeight - fontProperties.descent);\n    }\n\n    context.setTransform(1, 0, 0, 1, 0, 0); // defaults needed for older browsers (e.g. Opera 29)\n\n    context.fillStyle = 'rgba(0, 0, 0, 0)';\n}\n"], "names": [], "mappings": ";;AAqBO,SAAS,UACZ,QACA,SACA,SACA,GACA,GACA,YACA,OAEJ;AACI,QAAM,OAAO,QAAQ,MACf,iBAAiB,QAAQ;AAE/B,UAAQ,UAAU,GAAG,CAAC,GACtB,QAAQ,MAAM,YAAY,UAAU;AAEpC,QAAM,KAAK,MAAM,kBAAkB,GAC7B,KAAK,EAAE,MAAM,kBAAkB;AAYrC,MAVA,QAAQ,OAAO,MAAM,aAAa,GAClC,QAAQ,YAAY,MAAM,iBAC1B,QAAQ,eAAe,MAAM,cAC7B,QAAQ,WAAW,MAAM,UACzB,QAAQ,aAAa,MAAM,YAG3B,QAAQ,YAAY,kBAAkB,QAAQ,SAAS,OAAO,YAAY,CAAC,IAAI,GAAG,OAAO,GACzF,QAAQ,cAAc,MAAM,QAExB,MAAM,YACV;AACU,UAAA,kBAAkB,MAAM,iBACxB,iBAAiB,MAAM,iBAAiB,YACxC,qBAAqB,MAAM,qBAAqB;AAEtD,YAAQ,cAAc,MAAM,OACvB,SAAS,eAAe,EACxB,SAAS,MAAM,eAAe,EAC9B,aAAa,GAClB,QAAQ,aAAa,gBACrB,QAAQ,gBAAgB,KAAK,IAAI,MAAM,eAAe,IAAI,oBAC1D,QAAQ,gBAAgB,KAAK,IAAI,MAAM,eAAe,IAAI;AAAA,EAC9D;AAGY,YAAA,cAAc,SACtB,QAAQ,aAAa,GACrB,QAAQ,gBAAgB,GACxB,QAAQ,gBAAgB;AAGxB,QAAM,UAAU,MAAM,mBAEtB,QAAQ,WAAW,MAAM,IAAI,KAAK,QAAQ,aAAa,eAAe,OAAO,GAE7E,MAAM,QAEN,QAAQ,SAAS,MAAM,IAAI,KAAK,QAAQ,aAAa,eAAe,OAAO,GAG/E,QAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAErC,QAAQ,YAAY;AACxB;"}