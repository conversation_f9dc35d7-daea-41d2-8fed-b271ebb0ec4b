{"version": 3, "file": "resolveCharacters.js", "sources": ["../../src/utils/resolveCharacters.ts"], "sourcesContent": ["import { splitTextToCharacters } from './splitTextToCharacters';\n\n/**\n * Processes the passed character set data and returns a flattened array of all the characters.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {string | string[] | string[][] } chars\n * @returns {string[]} the flattened array of characters\n */\nexport function resolveCharacters(chars: string | (string | string[])[]): string[]\n{\n    // Split the chars string into individual characters\n    if (typeof chars === 'string')\n    {\n        chars = [chars];\n    }\n\n    // Handle an array of characters+ranges\n    const result: string[] = [];\n\n    for (let i = 0, j = chars.length; i < j; i++)\n    {\n        const item = chars[i];\n\n        // Handle range delimited by start/end chars\n        if (Array.isArray(item))\n        {\n            if (item.length !== 2)\n            {\n                throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${item.length}.`);\n            }\n\n            const startCode = item[0].charCodeAt(0);\n            const endCode = item[1].charCodeAt(0);\n\n            if (endCode < startCode)\n            {\n                throw new Error('[BitmapFont]: Invalid character range.');\n            }\n\n            for (let i = startCode, j = endCode; i <= j; i++)\n            {\n                result.push(String.fromCharCode(i));\n            }\n        }\n        // Handle a character set string\n        else\n        {\n            result.push(...splitTextToCharacters(item));\n        }\n    }\n\n    if (result.length === 0)\n    {\n        throw new Error('[BitmapFont]: Empty set when resolving characters.');\n    }\n\n    return result;\n}\n"], "names": ["i", "j", "splitTextToCharacters"], "mappings": ";;AAUO,SAAS,kBAAkB,OAClC;AAEQ,SAAO,SAAU,aAEjB,QAAQ,CAAC,KAAK;AAIlB,QAAM,SAAmB,CAAA;AAEzB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KACzC;AACU,UAAA,OAAO,MAAM,CAAC;AAGhB,QAAA,MAAM,QAAQ,IAAI,GACtB;AACI,UAAI,KAAK,WAAW;AAEhB,cAAM,IAAI,MAAM,iEAAiE,KAAK,MAAM,GAAG;AAGnG,YAAM,YAAY,KAAK,CAAC,EAAE,WAAW,CAAC,GAChC,UAAU,KAAK,CAAC,EAAE,WAAW,CAAC;AAEpC,UAAI,UAAU;AAEJ,cAAA,IAAI,MAAM,wCAAwC;AAG5D,eAASA,KAAI,WAAWC,KAAI,SAASD,MAAKC,IAAGD;AAEzC,eAAO,KAAK,OAAO,aAAaA,EAAC,CAAC;AAAA,IAE1C;AAII,aAAO,KAAK,GAAGE,4CAAsB,IAAI,CAAC;AAAA,EAElD;AAEA,MAAI,OAAO,WAAW;AAEZ,UAAA,IAAI,MAAM,oDAAoD;AAGjE,SAAA;AACX;;"}