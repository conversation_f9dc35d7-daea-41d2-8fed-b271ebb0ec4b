{"version": 3, "file": "BitmapFont.mjs", "sources": ["../src/BitmapFont.ts"], "sourcesContent": ["import { ALPHA_MODES, BaseTexture, MIPMAP_MODES, Rectangle, settings, Texture, utils } from '@pixi/core';\nimport { TextMetrics, TextStyle } from '@pixi/text';\nimport { BitmapFontData } from './BitmapFontData';\nimport { autoDetectFormat } from './formats';\nimport { drawGlyph, extractCharCode, resolveCharacters } from './utils';\n\nimport type { IBaseTextureOptions, ICanvas, ICanvasRenderingContext2D, SCALE_MODES } from '@pixi/core';\nimport type { ITextStyle } from '@pixi/text';\n\nexport interface IBitmapFontCharacter\n{\n    xOffset: number;\n    yOffset: number;\n    xAdvance: number;\n    texture: Texture;\n    page: number;\n    kerning: utils.Dict<number>;\n}\n\ntype BaseOptions = Pick<IBaseTextureOptions, 'scaleMode' | 'mipmap' | 'anisotropicLevel' | 'alphaMode'>;\n\n/** @memberof PIXI */\nexport interface IBitmapFontOptions extends BaseOptions\n{\n    /**\n     * Characters included in the font set. You can also use ranges.\n     * For example, `[['a', 'z'], ['A', 'Z'], \"!@#$%^&*()~{}[] \"]`.\n     * Don't forget to include spaces ' ' in your character set!\n     * @default PIXI.BitmapFont.ALPHANUMERIC\n     */\n    chars?: string | (string | string[])[];\n\n    /**\n     * Render resolution for glyphs.\n     * @default 1\n     */\n    resolution?: number;\n\n    /**\n     * Padding between glyphs on texture atlas. Lower values could mean more visual artifacts\n     * and bleeding from other glyphs, larger values increase the space required on the texture.\n     * @default 4\n     */\n    padding?: number;\n\n    /**\n     * Optional width of atlas, smaller values to reduce memory.\n     * @default 512\n     */\n    textureWidth?: number;\n\n    /**\n     * Optional height of atlas, smaller values to reduce memory.\n     * @default 512\n     */\n    textureHeight?: number;\n\n    /**\n     * If mipmapping is enabled for texture. For instance, by default PixiJS only enables mipmapping\n     * on Power-of-Two textures. If your textureWidth or textureHeight are not power-of-two, you\n     * may consider enabling mipmapping to get better quality with lower font sizes. Note:\n     * for MSDF/SDF fonts, mipmapping is not supported.\n     * @default PIXI.BaseTexture.defaultOptions.mipmap\n     */\n    mipmap?: MIPMAP_MODES;\n\n    /**\n     * Anisotropic filtering level of texture.\n     * @default PIXI.BaseTexture.defaultOptions.anisotropicLevel\n     */\n    anisotropicLevel?: number;\n\n    /**\n     * Default scale mode, linear, nearest. Nearest can be helpful for bitmap-style fonts.\n     * @default PIXI.BaseTexture.defaultOptions.scaleMode\n     */\n    scaleMode?: SCALE_MODES;\n\n    /**\n     * Pre multiply the image alpha.  Note: for MSDF/SDF fonts, alphaMode is not supported.\n     * @default PIXI.BaseTexture.defaultOptions.alphaMode\n     */\n    alphaMode?: ALPHA_MODES;\n\n    /**\n     * Skip generation of kerning information for the BitmapFont.\n     * If true, this could potentially increase the performance, but may impact the rendered text appearance.\n     * @default false\n     */\n    skipKerning?: boolean;\n}\n\n/**\n * BitmapFont represents a typeface available for use with the BitmapText class. Use the `install`\n * method for adding a font to be used.\n * @memberof PIXI\n */\nexport class BitmapFont\n{\n    /**\n     * This character set includes all the letters in the alphabet (both lower- and upper- case).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from('ExampleFont', style, { chars: BitmapFont.ALPHA })\n     */\n    public static readonly ALPHA = [['a', 'z'], ['A', 'Z'], ' '];\n\n    /**\n     * This character set includes all decimal digits (from 0 to 9).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from('ExampleFont', style, { chars: BitmapFont.NUMERIC })\n     */\n    public static readonly NUMERIC = [['0', '9']];\n\n    /**\n     * This character set is the union of `BitmapFont.ALPHA` and `BitmapFont.NUMERIC`.\n     * @type {string[][]}\n     */\n    public static readonly ALPHANUMERIC = [['a', 'z'], ['A', 'Z'], ['0', '9'], ' '];\n\n    /**\n     * This character set consists of all the ASCII table.\n     * @member {string[][]}\n     * @see http://www.asciitable.com/\n     */\n    public static readonly ASCII = [[' ', '~']];\n\n    /**\n     * Collection of default options when using `BitmapFont.from`.\n     * @property {number} [resolution=1] -\n     * @property {number} [textureWidth=512] -\n     * @property {number} [textureHeight=512] -\n     * @property {number} [padding=4] -\n     * @property {string|string[]|string[][]} chars = PIXI.BitmapFont.ALPHANUMERIC\n     */\n    public static readonly defaultOptions: IBitmapFontOptions = {\n        resolution: 1,\n        textureWidth: 512,\n        textureHeight: 512,\n        padding: 4,\n        chars: BitmapFont.ALPHANUMERIC,\n    };\n\n    /** Collection of available/installed fonts. */\n    public static readonly available: utils.Dict<BitmapFont> = {};\n\n    /** The name of the font face. */\n    public readonly font: string;\n\n    /** The size of the font face in pixels. */\n    public readonly size: number;\n\n    /** The line-height of the font face in pixels. */\n    public readonly lineHeight: number;\n\n    /** The map of characters by character code. */\n    public readonly chars: utils.Dict<IBitmapFontCharacter>;\n\n    /** The map of base page textures (i.e., sheets of glyphs). */\n    public readonly pageTextures: utils.Dict<Texture>;\n\n    /** The range of the distance field in pixels. */\n    public readonly distanceFieldRange: number;\n\n    /** The kind of distance field for this font or \"none\". */\n    public readonly distanceFieldType: string;\n\n    private _ownsTextures: boolean;\n\n    /**\n     * @param data\n     * @param textures\n     * @param ownsTextures - Setting to `true` will destroy page textures\n     *        when the font is uninstalled.\n     */\n    constructor(data: BitmapFontData, textures: Texture[] | utils.Dict<Texture>, ownsTextures?: boolean)\n    {\n        const [info] = data.info;\n        const [common] = data.common;\n        const [page] = data.page;\n        const [distanceField] = data.distanceField;\n        const res = utils.getResolutionOfUrl(page.file);\n        const pageTextures: utils.Dict<Texture> = {};\n\n        this._ownsTextures = ownsTextures;\n        this.font = info.face;\n        this.size = info.size;\n        this.lineHeight = common.lineHeight / res;\n        this.chars = {};\n        this.pageTextures = pageTextures;\n\n        // Convert the input Texture, Textures or object\n        // into a page Texture lookup by \"id\"\n        for (let i = 0; i < data.page.length; i++)\n        {\n            const { id, file } = data.page[i];\n\n            pageTextures[id] = textures instanceof Array\n                ? textures[i] : textures[file];\n\n            // only MSDF and SDF fonts need no-premultiplied-alpha\n            if (distanceField?.fieldType && distanceField.fieldType !== 'none')\n            {\n                pageTextures[id].baseTexture.alphaMode = ALPHA_MODES.NO_PREMULTIPLIED_ALPHA;\n                pageTextures[id].baseTexture.mipmap = MIPMAP_MODES.OFF;\n            }\n        }\n\n        // parse letters\n        for (let i = 0; i < data.char.length; i++)\n        {\n            const { id, page } = data.char[i];\n            let { x, y, width, height, xoffset, yoffset, xadvance } = data.char[i];\n\n            x /= res;\n            y /= res;\n            width /= res;\n            height /= res;\n            xoffset /= res;\n            yoffset /= res;\n            xadvance /= res;\n\n            const rect = new Rectangle(\n                x + (pageTextures[page].frame.x / res),\n                y + (pageTextures[page].frame.y / res),\n                width,\n                height\n            );\n\n            this.chars[id] = {\n                xOffset: xoffset,\n                yOffset: yoffset,\n                xAdvance: xadvance,\n                kerning: {},\n                texture: new Texture(\n                    pageTextures[page].baseTexture,\n                    rect\n                ),\n                page,\n            };\n        }\n\n        // parse kernings\n        for (let i = 0; i < data.kerning.length; i++)\n        {\n            let { first, second, amount } = data.kerning[i];\n\n            first /= res;\n            second /= res;\n            amount /= res;\n\n            if (this.chars[second])\n            {\n                this.chars[second].kerning[first] = amount;\n            }\n        }\n\n        // Store distance field information\n        this.distanceFieldRange = distanceField?.distanceRange;\n        this.distanceFieldType = distanceField?.fieldType?.toLowerCase() ?? 'none';\n    }\n\n    /** Remove references to created glyph textures. */\n    public destroy(): void\n    {\n        for (const id in this.chars)\n        {\n            this.chars[id].texture.destroy();\n            this.chars[id].texture = null;\n        }\n\n        for (const id in this.pageTextures)\n        {\n            if (this._ownsTextures)\n            {\n                this.pageTextures[id].destroy(true);\n            }\n\n            this.pageTextures[id] = null;\n        }\n\n        // Set readonly null.\n        (this as any).chars = null;\n        (this as any).pageTextures = null;\n    }\n\n    /**\n     * Register a new bitmap font.\n     * @param data - The\n     *        characters map that could be provided as xml or raw string.\n     * @param textures - List of textures for each page.\n     * @param ownsTextures - Set to `true` to destroy page textures\n     *        when the font is uninstalled. By default fonts created with\n     *        `BitmapFont.from` or from the `BitmapFontLoader` are `true`.\n     * @returns {PIXI.BitmapFont} Result font object with font, size, lineHeight\n     *         and char fields.\n     */\n    public static install(\n        data: string | XMLDocument | BitmapFontData,\n        textures: Texture | Texture[] | utils.Dict<Texture>,\n        ownsTextures?: boolean\n    ): BitmapFont\n    {\n        let fontData;\n\n        if (data instanceof BitmapFontData)\n        {\n            fontData = data;\n        }\n        else\n        {\n            const format = autoDetectFormat(data);\n\n            if (!format)\n            {\n                throw new Error('Unrecognized data format for font.');\n            }\n\n            fontData = format.parse(data as any);\n        }\n\n        // Single texture, convert to list\n        if (textures instanceof Texture)\n        {\n            textures = [textures];\n        }\n\n        const font = new BitmapFont(fontData, textures, ownsTextures);\n\n        BitmapFont.available[font.font] = font;\n\n        return font;\n    }\n\n    /**\n     * Remove bitmap font by name.\n     * @param name - Name of the font to uninstall.\n     */\n    public static uninstall(name: string): void\n    {\n        const font = BitmapFont.available[name];\n\n        if (!font)\n        {\n            throw new Error(`No font found named '${name}'`);\n        }\n\n        font.destroy();\n        delete BitmapFont.available[name];\n    }\n\n    /**\n     * Generates a bitmap-font for the given style and character set. This does not support\n     * kernings yet. With `style` properties, only the following non-layout properties are used:\n     *\n     * - {@link PIXI.TextStyle#dropShadow|dropShadow}\n     * - {@link PIXI.TextStyle#dropShadowDistance|dropShadowDistance}\n     * - {@link PIXI.TextStyle#dropShadowColor|dropShadowColor}\n     * - {@link PIXI.TextStyle#dropShadowBlur|dropShadowBlur}\n     * - {@link PIXI.TextStyle#dropShadowAngle|dropShadowAngle}\n     * - {@link PIXI.TextStyle#fill|fill}\n     * - {@link PIXI.TextStyle#fillGradientStops|fillGradientStops}\n     * - {@link PIXI.TextStyle#fillGradientType|fillGradientType}\n     * - {@link PIXI.TextStyle#fontFamily|fontFamily}\n     * - {@link PIXI.TextStyle#fontSize|fontSize}\n     * - {@link PIXI.TextStyle#fontVariant|fontVariant}\n     * - {@link PIXI.TextStyle#fontWeight|fontWeight}\n     * - {@link PIXI.TextStyle#lineJoin|lineJoin}\n     * - {@link PIXI.TextStyle#miterLimit|miterLimit}\n     * - {@link PIXI.TextStyle#stroke|stroke}\n     * - {@link PIXI.TextStyle#strokeThickness|strokeThickness}\n     * - {@link PIXI.TextStyle#textBaseline|textBaseline}\n     * @param name - The name of the custom font to use with BitmapText.\n     * @param textStyle - Style options to render with BitmapFont.\n     * @param options - Setup options for font or name of the font.\n     * @returns Font generated by style options.\n     * @example\n     * import { BitmapFont, BitmapText } from 'pixi.js';\n     *\n     * BitmapFont.from('TitleFont', {\n     *     fontFamily: 'Arial',\n     *     fontSize: 12,\n     *     strokeThickness: 2,\n     *     fill: 'purple',\n     * });\n     *\n     * const title = new BitmapText('This is the title', { fontName: 'TitleFont' });\n     */\n    public static from(name: string, textStyle?: TextStyle | Partial<ITextStyle>, options?: IBitmapFontOptions): BitmapFont\n    {\n        if (!name)\n        {\n            throw new Error('[BitmapFont] Property `name` is required.');\n        }\n\n        const {\n            chars,\n            padding,\n            resolution,\n            textureWidth,\n            textureHeight,\n            ...baseOptions\n        } = Object.assign({}, BitmapFont.defaultOptions, options);\n\n        const charsList = resolveCharacters(chars);\n        const style = textStyle instanceof TextStyle ? textStyle : new TextStyle(textStyle);\n        const lineWidth = textureWidth;\n        const fontData = new BitmapFontData();\n\n        fontData.info[0] = {\n            face: style.fontFamily as string,\n            size: style.fontSize as number,\n        };\n        fontData.common[0] = {\n            lineHeight: style.fontSize as number,\n        };\n\n        let positionX = 0;\n        let positionY = 0;\n\n        let canvas: ICanvas;\n        let context: ICanvasRenderingContext2D;\n        let baseTexture: BaseTexture;\n        let maxCharHeight = 0;\n        const baseTextures: BaseTexture[] = [];\n        const textures: Texture[] = [];\n\n        for (let i = 0; i < charsList.length; i++)\n        {\n            if (!canvas)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n                canvas.width = textureWidth;\n                canvas.height = textureHeight;\n\n                context = canvas.getContext('2d');\n                baseTexture = new BaseTexture(canvas, { resolution, ...baseOptions });\n\n                baseTextures.push(baseTexture);\n                textures.push(new Texture(baseTexture));\n\n                fontData.page.push({\n                    id: textures.length - 1,\n                    file: '',\n                });\n            }\n\n            // Measure glyph dimensions\n            const character = charsList[i];\n            const metrics = TextMetrics.measureText(character, style, false, canvas);\n            const width = metrics.width;\n            const height = Math.ceil(metrics.height);\n\n            // This is ugly - but italics are given more space so they don't overlap\n            const textureGlyphWidth = Math.ceil((style.fontStyle === 'italic' ? 2 : 1) * width);\n\n            // Can't fit char anymore: next canvas please!\n            if (positionY >= textureHeight - (height * resolution))\n            {\n                if (positionY === 0)\n                {\n                    // We don't want user debugging an infinite loop (or do we? :)\n                    throw new Error(`[BitmapFont] textureHeight ${textureHeight}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n\n                // Create new atlas once current has filled up\n                canvas = null;\n                context = null;\n                baseTexture = null;\n                positionY = 0;\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            maxCharHeight = Math.max(height + metrics.fontProperties.descent, maxCharHeight);\n\n            // Wrap line once full row has been rendered\n            if ((textureGlyphWidth * resolution) + positionX >= lineWidth)\n            {\n                if (positionX === 0)\n                {\n                    // Avoid infinite loop (There can be some very wide char like '\\uFDFD'!)\n                    throw new Error(`[BitmapFont] textureWidth ${textureWidth}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n                positionY += maxCharHeight * resolution;\n                positionY = Math.ceil(positionY);\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            drawGlyph(canvas, context, metrics, positionX, positionY, resolution, style);\n\n            // Unique (numeric) ID mapping to this glyph\n            const id = extractCharCode(metrics.text);\n\n            // Create a texture holding just the glyph\n            fontData.char.push({\n                id,\n                page: textures.length - 1,\n                x: positionX / resolution,\n                y: positionY / resolution,\n                width: textureGlyphWidth,\n                height,\n                xoffset: 0,\n                yoffset: 0,\n                xadvance: width\n                        - (style.dropShadow ? style.dropShadowDistance : 0)\n                        - (style.stroke ? style.strokeThickness : 0),\n            });\n\n            positionX += (textureGlyphWidth + (2 * padding)) * resolution;\n            positionX = Math.ceil(positionX);\n        }\n\n        if (!options?.skipKerning)\n        {\n            // Brute-force kerning info, this can be expensive b/c it's an O(n²),\n            // but we're using measureText which is native and fast.\n            for (let i = 0, len = charsList.length; i < len; i++)\n            {\n                const first = charsList[i];\n\n                for (let j = 0; j < len; j++)\n                {\n                    const second = charsList[j];\n                    const c1 = context.measureText(first).width;\n                    const c2 = context.measureText(second).width;\n                    const total = context.measureText(first + second).width;\n                    const amount = total - (c1 + c2);\n\n                    if (amount)\n                    {\n                        fontData.kerning.push({\n                            first: extractCharCode(first),\n                            second: extractCharCode(second),\n                            amount,\n                        });\n                    }\n                }\n            }\n        }\n\n        const font = new BitmapFont(fontData, textures, true);\n\n        // Make it easier to replace a font\n        if (BitmapFont.available[name] !== undefined)\n        {\n            BitmapFont.uninstall(name);\n        }\n\n        BitmapFont.available[name] = font;\n\n        return font;\n    }\n}\n"], "names": ["_BitmapFont", "page"], "mappings": ";;;;;;;;AAiGO,MAAM,cAAN,MAAMA,aACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8EI,YAAY,MAAsB,UAA2C,cAC7E;AACU,UAAA,CAAC,IAAI,IAAI,KAAK,MACd,CAAC,MAAM,IAAI,KAAK,QAChB,CAAC,IAAI,IAAI,KAAK,MACd,CAAC,aAAa,IAAI,KAAK,eACvB,MAAM,MAAM,mBAAmB,KAAK,IAAI,GACxC,eAAoC,CAAA;AAErC,SAAA,gBAAgB,cACrB,KAAK,OAAO,KAAK,MACjB,KAAK,OAAO,KAAK,MACjB,KAAK,aAAa,OAAO,aAAa,KACtC,KAAK,QAAQ,IACb,KAAK,eAAe;AAIpB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KACtC;AACI,YAAM,EAAE,IAAI,KAAA,IAAS,KAAK,KAAK,CAAC;AAEnB,mBAAA,EAAE,IAAI,oBAAoB,QACjC,SAAS,CAAC,IAAI,SAAS,IAAI,GAG7B,eAAe,aAAa,cAAc,cAAc,WAExD,aAAa,EAAE,EAAE,YAAY,YAAY,YAAY,wBACrD,aAAa,EAAE,EAAE,YAAY,SAAS,aAAa;AAAA,IAE3D;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KACtC;AACI,YAAM,EAAE,IAAI,MAAAC,MAAS,IAAA,KAAK,KAAK,CAAC;AAC5B,UAAA,EAAE,GAAG,GAAG,OAAO,QAAQ,SAAS,SAAS,SAAS,IAAI,KAAK,KAAK,CAAC;AAEhE,WAAA,KACL,KAAK,KACL,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY;AAEZ,YAAM,OAAO,IAAI;AAAA,QACb,IAAK,aAAaA,KAAI,EAAE,MAAM,IAAI;AAAA,QAClC,IAAK,aAAaA,KAAI,EAAE,MAAM,IAAI;AAAA,QAClC;AAAA,QACA;AAAA,MAAA;AAGC,WAAA,MAAM,EAAE,IAAI;AAAA,QACb,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS,CAAC;AAAA,QACV,SAAS,IAAI;AAAA,UACT,aAAaA,KAAI,EAAE;AAAA,UACnB;AAAA,QACJ;AAAA,QACA,MAAAA;AAAAA,MAAA;AAAA,IAER;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KACzC;AACI,UAAI,EAAE,OAAO,QAAQ,OAAW,IAAA,KAAK,QAAQ,CAAC;AAE9C,eAAS,KACT,UAAU,KACV,UAAU,KAEN,KAAK,MAAM,MAAM,MAEjB,KAAK,MAAM,MAAM,EAAE,QAAQ,KAAK,IAAI;AAAA,IAE5C;AAGK,SAAA,qBAAqB,eAAe,eACzC,KAAK,oBAAoB,eAAe,WAAW,YAAiB,KAAA;AAAA,EACxE;AAAA;AAAA,EAGO,UACP;AACI,eAAW,MAAM,KAAK;AAEb,WAAA,MAAM,EAAE,EAAE,QAAQ,QAAA,GACvB,KAAK,MAAM,EAAE,EAAE,UAAU;AAG7B,eAAW,MAAM,KAAK;AAEd,WAAK,iBAEL,KAAK,aAAa,EAAE,EAAE,QAAQ,EAAI,GAGtC,KAAK,aAAa,EAAE,IAAI;AAI3B,SAAa,QAAQ,MACrB,KAAa,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAc,QACV,MACA,UACA,cAEJ;AACQ,QAAA;AAEJ,QAAI,gBAAgB;AAEL,iBAAA;AAAA,SAGf;AACU,YAAA,SAAS,iBAAiB,IAAI;AAEpC,UAAI,CAAC;AAEK,cAAA,IAAI,MAAM,oCAAoC;AAG7C,iBAAA,OAAO,MAAM,IAAW;AAAA,IACvC;AAGI,wBAAoB,YAEpB,WAAW,CAAC,QAAQ;AAGxB,UAAM,OAAO,IAAID,aAAW,UAAU,UAAU,YAAY;AAE5D,WAAAA,aAAW,UAAU,KAAK,IAAI,IAAI,MAE3B;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAc,UAAU,MACxB;AACU,UAAA,OAAOA,aAAW,UAAU,IAAI;AAEtC,QAAI,CAAC;AAED,YAAM,IAAI,MAAM,wBAAwB,IAAI,GAAG;AAGnD,SAAK,QAAQ,GACb,OAAOA,aAAW,UAAU,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuCA,OAAc,KAAK,MAAc,WAA6C,SAC9E;AACI,QAAI,CAAC;AAEK,YAAA,IAAI,MAAM,2CAA2C;AAGzD,UAAA;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,IAAI,OAAO,OAAO,CAAC,GAAGA,aAAW,gBAAgB,OAAO,GAElD,YAAY,kBAAkB,KAAK,GACnC,QAAQ,qBAAqB,YAAY,YAAY,IAAI,UAAU,SAAS,GAC5E,YAAY,cACZ,WAAW,IAAI;AAEZ,aAAA,KAAK,CAAC,IAAI;AAAA,MACf,MAAM,MAAM;AAAA,MACZ,MAAM,MAAM;AAAA,IAAA,GAEhB,SAAS,OAAO,CAAC,IAAI;AAAA,MACjB,YAAY,MAAM;AAAA,IAAA;AAGtB,QAAI,YAAY,GACZ,YAAY,GAEZ,QACA,SACA,aACA,gBAAgB;AACpB,UAAM,eAA8B,CAAA,GAC9B,WAAsB;AAE5B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KACtC;AACS,iBAED,SAAS,SAAS,QAAQ,aAAa,GACvC,OAAO,QAAQ,cACf,OAAO,SAAS,eAEhB,UAAU,OAAO,WAAW,IAAI,GAChC,cAAc,IAAI,YAAY,QAAQ,EAAE,YAAY,GAAG,YAAa,CAAA,GAEpE,aAAa,KAAK,WAAW,GAC7B,SAAS,KAAK,IAAI,QAAQ,WAAW,CAAC,GAEtC,SAAS,KAAK,KAAK;AAAA,QACf,IAAI,SAAS,SAAS;AAAA,QACtB,MAAM;AAAA,MACT,CAAA;AAIL,YAAM,YAAY,UAAU,CAAC,GACvB,UAAU,YAAY,YAAY,WAAW,OAAO,IAAO,MAAM,GACjE,QAAQ,QAAQ,OAChB,SAAS,KAAK,KAAK,QAAQ,MAAM,GAGjC,oBAAoB,KAAK,MAAM,MAAM,cAAc,WAAW,IAAI,KAAK,KAAK;AAG9E,UAAA,aAAa,gBAAiB,SAAS,YAC3C;AACI,YAAI,cAAc;AAGd,gBAAM,IAAI,MAAM,8BAA8B,aAAa,iCACpC,MAAM,UAAU,gBAAgB,MAAM,QAAQ,cAAc,SAAS,IAAI;AAGlG,UAAA,GAGF,SAAS,MACT,UAAU,MACV,cAAc,MACd,YAAY,GACZ,YAAY,GACZ,gBAAgB;AAEhB;AAAA,MACJ;AAKA,UAHA,gBAAgB,KAAK,IAAI,SAAS,QAAQ,eAAe,SAAS,aAAa,GAG1E,oBAAoB,aAAc,aAAa,WACpD;AACI,YAAI,cAAc;AAGd,gBAAM,IAAI,MAAM,6BAA6B,YAAY,iCAClC,MAAM,UAAU,gBAAgB,MAAM,QAAQ,cAAc,SAAS,IAAI;AAGlG,UAAA,GACF,aAAa,gBAAgB,YAC7B,YAAY,KAAK,KAAK,SAAS,GAC/B,YAAY,GACZ,gBAAgB;AAEhB;AAAA,MACJ;AAEA,gBAAU,QAAQ,SAAS,SAAS,WAAW,WAAW,YAAY,KAAK;AAGrE,YAAA,KAAK,gBAAgB,QAAQ,IAAI;AAGvC,eAAS,KAAK,KAAK;AAAA,QACf;AAAA,QACA,MAAM,SAAS,SAAS;AAAA,QACxB,GAAG,YAAY;AAAA,QACf,GAAG,YAAY;AAAA,QACf,OAAO;AAAA,QACP;AAAA,QACA,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU,SACC,MAAM,aAAa,MAAM,qBAAqB,MAC9C,MAAM,SAAS,MAAM,kBAAkB;AAAA,MACrD,CAAA,GAED,cAAc,oBAAqB,IAAI,WAAY,YACnD,YAAY,KAAK,KAAK,SAAS;AAAA,IACnC;AAEA,QAAI,CAAC,SAAS;AAIV,eAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KACjD;AACU,cAAA,QAAQ,UAAU,CAAC;AAEzB,iBAAS,IAAI,GAAG,IAAI,KAAK,KACzB;AACU,gBAAA,SAAS,UAAU,CAAC,GACpB,KAAK,QAAQ,YAAY,KAAK,EAAE,OAChC,KAAK,QAAQ,YAAY,MAAM,EAAE,OAEjC,SADQ,QAAQ,YAAY,QAAQ,MAAM,EAAE,SAC1B,KAAK;AAEzB,oBAEA,SAAS,QAAQ,KAAK;AAAA,YAClB,OAAO,gBAAgB,KAAK;AAAA,YAC5B,QAAQ,gBAAgB,MAAM;AAAA,YAC9B;AAAA,UAAA,CACH;AAAA,QAET;AAAA,MACJ;AAGJ,UAAM,OAAO,IAAIA,aAAW,UAAU,UAAU,EAAI;AAGpD,WAAIA,aAAW,UAAU,IAAI,MAAM,UAE/BA,aAAW,UAAU,IAAI,GAG7BA,aAAW,UAAU,IAAI,IAAI,MAEtB;AAAA,EACX;AACJ;AApda,YAQc,QAAQ,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AARlD,YAgBc,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAhBnC,YAsBc,eAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG;AAAA;AAAA;AAAA;AAAA;AAtBrE,YA6Bc,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA7BjC,YAuCc,iBAAqC;AAAA,EACxD,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,SAAS;AAAA,EACT,OAAO,YAAW;AACtB;AA7CS,YAgDc,YAAoC,CAAA;AAhDxD,IAAM,aAAN;"}