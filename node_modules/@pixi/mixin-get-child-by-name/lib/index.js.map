{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/// <reference path=\"../global.d.ts\" />\nimport { Container, DisplayObject } from '@pixi/display';\n\n/**\n * The instance name of the object.\n * @memberof PIXI.DisplayObject#\n * @member {string} name\n */\nDisplayObject.prototype.name = null;\n\n/**\n * Returns the display object in the container.\n *\n * Recursive searches are done in a preorder traversal.\n * @method getChildByName\n * @memberof PIXI.Container#\n * @param {string} name - Instance name.\n * @param {boolean}[deep=false] - Whether to search recursively\n * @returns {PIXI.DisplayObject} The child with the specified name.\n */\nContainer.prototype.getChildByName = function getChildByName<T extends DisplayObject = DisplayObject>(\n    name: string,\n    deep?: boolean,\n): T | null\n{\n    for (let i = 0, j = this.children.length; i < j; i++)\n    {\n        if (this.children[i].name === name)\n        {\n            return this.children[i];\n        }\n    }\n\n    if (deep)\n    {\n        for (let i = 0, j = this.children.length; i < j; i++)\n        {\n            const child = (this.children[i] as Container);\n\n            if (!child.getChildByName)\n            {\n                continue;\n            }\n\n            const target = child.getChildByName<T>(name, true);\n\n            if (target)\n            {\n                return target;\n            }\n        }\n    }\n\n    return null;\n};\n"], "names": ["DisplayObject", "Container"], "mappings": ";;AAQAA,QAAAA,cAAc,UAAU,OAAO;AAY/BC,QAAA,UAAU,UAAU,iBAAiB,SACjC,MACA,MAEJ;AACI,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG;AAE7C,QAAI,KAAK,SAAS,CAAC,EAAE,SAAS;AAEnB,aAAA,KAAK,SAAS,CAAC;AAI1B,MAAA;AAES,aAAA,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG,KACjD;AACU,YAAA,QAAS,KAAK,SAAS,CAAC;AAE9B,UAAI,CAAC,MAAM;AAEP;AAGJ,YAAM,SAAS,MAAM,eAAkB,MAAM,EAAI;AAE7C,UAAA;AAEO,eAAA;AAAA,IAEf;AAGG,SAAA;AACX;"}