{"version": 3, "file": "NoiseFilter.mjs", "sources": ["../src/NoiseFilter.ts"], "sourcesContent": ["import { defaultFilterVertex, Filter } from '@pixi/core';\nimport fragment from './noise.frag';\n\n/**\n * A Noise effect filter.\n *\n * original filter: https://github.com/evanw/glfx.js/blob/master/src/filters/adjust/noise.js\n * @memberof PIXI\n * <AUTHOR> @vicocotea\n */\nexport class NoiseFilter extends Filter\n{\n    /**\n     * @param {number} [noise=0.5] - The noise intensity, should be a normalized value in the range [0, 1].\n     * @param {number} [seed] - A random seed for the noise generation. Default is `Math.random()`.\n     */\n    constructor(noise = 0.5, seed = Math.random())\n    {\n        super(defaultFilterVertex, fragment, {\n            uNoise: 0,\n            uSeed: 0,\n        });\n\n        this.noise = noise;\n        this.seed = seed;\n    }\n\n    /**\n     * The amount of noise to apply, this value should be in the range (0, 1].\n     * @default 0.5\n     */\n    get noise(): number\n    {\n        return this.uniforms.uNoise;\n    }\n\n    set noise(value: number)\n    {\n        this.uniforms.uNoise = value;\n    }\n\n    /** A seed value to apply to the random noise generation. `Math.random()` is a good value to use. */\n    get seed(): number\n    {\n        return this.uniforms.uSeed;\n    }\n\n    set seed(value: number)\n    {\n        this.uniforms.uSeed = value;\n    }\n}\n"], "names": [], "mappings": ";;AAUO,MAAM,oBAAoB,OACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKI,YAAY,QAAQ,KAAK,OAAO,KAAK,UACrC;AACI,UAAM,qBAAqB,UAAU;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA,CACV,GAED,KAAK,QAAQ,OACb,KAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QACJ;AACI,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EAEA,IAAI,MAAM,OACV;AACI,SAAK,SAAS,SAAS;AAAA,EAC3B;AAAA;AAAA,EAGA,IAAI,OACJ;AACI,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EAEA,IAAI,KAAK,OACT;AACI,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;"}