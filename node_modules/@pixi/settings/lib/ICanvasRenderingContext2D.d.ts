import type { ICanvas } from './ICanvas';
/**
 * Common interface for CanvasRenderingContext2D, OffscreenCanvasRenderingContext2D, and other custom canvas 2D context.
 * @memberof PIXI
 */
export interface ICanvasRenderingContext2D extends CanvasState, CanvasTransform, CanvasCompositing, CanvasImageSmoothing, CanvasFillStrokeStyles, CanvasShadowStyles, CanvasFilters, CanvasRect, CanvasDrawPath, CanvasText, CanvasDrawImage, CanvasImageData, CanvasPathDrawingStyles, Omit<CanvasTextDrawingStyles, 'letterSpacing'>, CanvasPath {
    createPattern(image: CanvasImageSource | ICanvas | VideoFrame, repetition: string | null): CanvasPattern | null;
    drawImage(image: CanvasImageSource | ICanvas | VideoFrame, dx: number, dy: number): void;
    drawImage(image: CanvasImageSource | ICanvas | VideoFrame, dx: number, dy: number, dw: number, dh: number): void;
    drawImage(image: CanvasImageSource | ICanvas | VideoFrame, sx: number, sy: number, sw: number, sh: number, dx: number, dy: number, dw: number, dh: number): void;
    letterSpacing?: string;
    textLetterSpacing?: string;
}
