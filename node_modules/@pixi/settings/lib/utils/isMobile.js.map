{"version": 3, "file": "isMobile.js", "sources": ["../../src/utils/isMobile.ts"], "sourcesContent": ["import isMobileJs from 'ismobilejs';\n\n// ismobilejs have different import behavior for CJS and ESM, so here is the hack\ntype isMobileJsType = typeof isMobileJs & { default?: typeof isMobileJs };\nconst isMobileCall = (isMobileJs as isMobileJsType).default ?? isMobileJs;\n\nexport type isMobileResult = {\n    apple: {\n        phone: boolean;\n        ipod: boolean;\n        tablet: boolean;\n        universal: boolean;\n        device: boolean;\n    };\n    amazon: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    android: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    windows: {\n        phone: boolean;\n        tablet: boolean;\n        device: boolean;\n    };\n    other: {\n        blackberry: boolean;\n        blackberry10: boolean;\n        opera: boolean;\n        firefox: boolean;\n        chrome: boolean;\n        device: boolean;\n    };\n    phone: boolean;\n    tablet: boolean;\n    any: boolean;\n};\n\nexport const isMobile: isMobileResult = isMobileCall(globalThis.navigator);\n"], "names": [], "mappings": ";;AAIA,MAAM,eAAgB,WAA8B,WAAW,YAsClD,WAA2B,aAAa,WAAW,SAAS;;"}