{"version": 3, "file": "adapter.js", "sources": ["../src/adapter.ts"], "sourcesContent": ["import type { ICanvas } from './ICanvas';\nimport type { ICanvasRenderingContext2D } from './ICanvasRenderingContext2D';\n\n/**\n * This interface describes all the DOM dependent calls that <PERSON><PERSON> makes throughout its codebase.\n * Implementations of this interface can be used to make sure <PERSON><PERSON> will work in any environment,\n * such as browser, Web Workers, and Node.js.\n * @memberof PIXI\n */\nexport interface IAdapter\n{\n    /** Returns a canvas object that can be used to create a webgl context. */\n    createCanvas: (width?: number, height?: number) => ICanvas;\n    /** Returns a 2D rendering context. */\n    getCanvasRenderingContext2D: () => { prototype: ICanvasRenderingContext2D; };\n    /** Returns a WebGL rendering context. */\n    getWebGLRenderingContext: () => typeof WebGLRenderingContext;\n    /** Returns a partial implementation of the browsers window.navigator */\n    getNavigator: () => { userAgent: string };\n    /** Returns the current base URL For browser environments this is either the document.baseURI or window.location.href */\n    getBaseUrl: () => string;\n    getFontFaceSet: () => FontFaceSet | null;\n    fetch: (url: RequestInfo, options?: RequestInit) => Promise<Response>;\n    parseXML: (xml: string) => Document;\n}\n\nexport const BrowserAdapter = {\n    /**\n     * Creates a canvas element of the given size.\n     * This canvas is created using the browser's native canvas element.\n     * @param width - width of the canvas\n     * @param height - height of the canvas\n     */\n    createCanvas: (width: number, height: number): HTMLCanvasElement =>\n    {\n        const canvas = document.createElement('canvas');\n\n        canvas.width = width;\n        canvas.height = height;\n\n        return canvas;\n    },\n    getCanvasRenderingContext2D: () => CanvasRenderingContext2D,\n    getWebGLRenderingContext: () => WebGLRenderingContext,\n    getNavigator: () => navigator,\n    getBaseUrl: () => (document.baseURI ?? window.location.href),\n    getFontFaceSet: () => document.fonts,\n    fetch: (url: RequestInfo, options?: RequestInit) => fetch(url, options),\n    parseXML: (xml: string) =>\n    {\n        const parser = new DOMParser();\n\n        return parser.parseFromString(xml, 'text/xml');\n    },\n} as IAdapter;\n"], "names": [], "mappings": ";AA0BO,MAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1B,cAAc,CAAC,OAAe,WAC9B;AACU,UAAA,SAAS,SAAS,cAAc,QAAQ;AAE9C,WAAA,OAAO,QAAQ,OACf,OAAO,SAAS,QAET;AAAA,EACX;AAAA,EACA,6BAA6B,MAAM;AAAA,EACnC,0BAA0B,MAAM;AAAA,EAChC,cAAc,MAAM;AAAA,EACpB,YAAY,MAAO,SAAS,WAAW,OAAO,SAAS;AAAA,EACvD,gBAAgB,MAAM,SAAS;AAAA,EAC/B,OAAO,CAAC,KAAkB,YAA0B,MAAM,KAAK,OAAO;AAAA,EACtE,UAAU,CAAC,QAEQ,IAAI,UAEL,EAAA,gBAAgB,KAAK,UAAU;AAErD;;"}