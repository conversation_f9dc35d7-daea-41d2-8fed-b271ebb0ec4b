{"version": 3, "file": "Sprite.js", "sources": ["../src/Sprite.ts"], "sourcesContent": ["import { BLEND_MODES, Color, ObservablePoint, Point, Rectangle, settings, Texture, utils } from '@pixi/core';\nimport { Bounds, Container } from '@pixi/display';\n\nimport type { ColorSource, IBaseTextureOptions, IPointData, Renderer, TextureSource } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\nconst tempPoint = new Point();\nconst indices = new Uint16Array([0, 1, 2, 0, 2, 3]);\n\nexport type SpriteSource = TextureSource | Texture;\n\nexport interface Sprite extends GlobalMixins.Sprite, Container {}\n\n/**\n * The Sprite object is the base for all textured objects that are rendered to the screen\n *\n * A sprite can be created directly from an image like this:\n *\n * ```js\n * import { Sprite } from 'pixi.js';\n *\n * const sprite = Sprite.from('assets/image.png');\n * ```\n *\n * The more efficient way to create sprites is using a {@link PIXI.Spritesheet},\n * as swapping base textures when rendering to the screen is inefficient.\n *\n * ```js\n * import { Assets, Sprite } from 'pixi.js';\n *\n * const sheet = await Assets.load('assets/spritesheet.json');\n * const sprite = new Sprite(sheet.textures['image.png']);\n * ```\n * @memberof PIXI\n */\nexport class Sprite extends Container\n{\n    /**\n     * The blend mode to be applied to the sprite. Apply a value of `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public blendMode: BLEND_MODES;\n    public indices: Uint16Array;\n\n    /**\n     * Plugin that is responsible for rendering this element.\n     * Allows to customize the rendering process without overriding '_render' & '_renderCanvas' methods.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    /**\n     * The width of the sprite (this is initially set by the texture).\n     * @protected\n     */\n    _width: number;\n\n    /**\n     * The height of the sprite (this is initially set by the texture)\n     * @protected\n     */\n    _height: number;\n\n    /**\n     * The texture that the sprite is using.\n     * @private\n     */\n    _texture: Texture;\n    _textureID: number;\n\n    /**\n     * Cached tint value so we can tell when the tint is changed.\n     * Value is used for 2d CanvasRenderer.\n     * @protected\n     * @default 0xFFFFFF\n     */\n    _cachedTint: number;\n    protected _textureTrimmedID: number;\n\n    /**\n     * This is used to store the uvs data of the sprite, assigned at the same time\n     * as the vertexData in calculateVertices().\n     * @member {Float32Array}\n     */\n    protected uvs: Float32Array;\n\n    /**\n     * The anchor point defines the normalized coordinates\n     * in the texture that map to the position of this\n     * sprite.\n     *\n     * By default, this is `(0,0)` (or `texture.defaultAnchor`\n     * if you have modified that), which means the position\n     * `(x,y)` of this `Sprite` will be the top-left corner.\n     *\n     * Note: Updating `texture.defaultAnchor` after\n     * constructing a `Sprite` does _not_ update its anchor.\n     *\n     * {@link https://docs.cocos2d-x.org/cocos2d-x/en/sprites/manipulation.html}\n     * @default `this.texture.defaultAnchor`\n     */\n    protected _anchor: ObservablePoint;\n\n    /**\n     * This is used to store the vertex data of the sprite (basically a quad).\n     * @member {Float32Array}\n     */\n    protected vertexData: Float32Array;\n\n    /**\n     * This is used to calculate the bounds of the object IF it is a trimmed sprite.\n     * @member {Float32Array}\n     */\n    private vertexTrimmedData: Float32Array;\n\n    /**\n     * Internal roundPixels field\n     * @private\n     */\n    private _roundPixels: boolean;\n    private _transformID: number;\n    private _transformTrimmedID: number;\n\n    /**\n     * The tint applied to the sprite. This is a hex value. A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    private _tintColor: Color;\n\n    // Internal-only properties\n    /**\n     * The tint applied to the sprite. This is a RGB value. A value of 0xFFFFFF will remove any tint effect.\n     * @private\n     * @default 16777215\n     */\n    _tintRGB: number;\n\n    /** @param texture - The texture for this sprite. */\n    constructor(texture?: Texture)\n    {\n        super();\n\n        this._anchor = new ObservablePoint(\n            this._onAnchorUpdate,\n            this,\n            (texture ? texture.defaultAnchor.x : 0),\n            (texture ? texture.defaultAnchor.y : 0)\n        );\n\n        this._texture = null;\n\n        this._width = 0;\n        this._height = 0;\n        this._tintColor = new Color(0xFFFFFF);\n        this._tintRGB = null;\n\n        this.tint = 0xFFFFFF;\n        this.blendMode = BLEND_MODES.NORMAL;\n        this._cachedTint = 0xFFFFFF;\n        this.uvs = null;\n\n        // call texture setter\n        this.texture = texture || Texture.EMPTY;\n        this.vertexData = new Float32Array(8);\n        this.vertexTrimmedData = null;\n\n        this._transformID = -1;\n        this._textureID = -1;\n\n        this._transformTrimmedID = -1;\n        this._textureTrimmedID = -1;\n\n        // Batchable stuff..\n        // TODO could make this a mixin?\n        this.indices = indices;\n\n        this.pluginName = 'batch';\n\n        /**\n         * Used to fast check if a sprite is.. a sprite!\n         * @member {boolean}\n         */\n        this.isSprite = true;\n        this._roundPixels = settings.ROUND_PIXELS;\n    }\n\n    /** When the texture is updated, this event will fire to update the scale and frame. */\n    protected _onTextureUpdate(): void\n    {\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n        this._cachedTint = 0xFFFFFF;\n\n        // so if _width is 0 then width was not set..\n        if (this._width)\n        {\n            this.scale.x = utils.sign(this.scale.x) * this._width / this._texture.orig.width;\n        }\n\n        if (this._height)\n        {\n            this.scale.y = utils.sign(this.scale.y) * this._height / this._texture.orig.height;\n        }\n    }\n\n    /** Called when the anchor position updates. */\n    private _onAnchorUpdate(): void\n    {\n        this._transformID = -1;\n        this._transformTrimmedID = -1;\n    }\n\n    /** Calculates worldTransform * vertices, store it in vertexData. */\n    public calculateVertices(): void\n    {\n        const texture = this._texture;\n\n        if (this._transformID === this.transform._worldID && this._textureID === texture._updateID)\n        {\n            return;\n        }\n\n        // update texture UV here, because base texture can be changed without calling `_onTextureUpdate`\n        if (this._textureID !== texture._updateID)\n        {\n            this.uvs = this._texture._uvs.uvsFloat32;\n        }\n\n        this._transformID = this.transform._worldID;\n        this._textureID = texture._updateID;\n\n        // set the vertex data\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n        const vertexData = this.vertexData;\n        const trim = texture.trim;\n        const orig = texture.orig;\n        const anchor = this._anchor;\n\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        if (trim)\n        {\n            // if the sprite is trimmed and is not a tilingsprite then we need to add the extra\n            // space before transforming the sprite coords.\n            w1 = trim.x - (anchor._x * orig.width);\n            w0 = w1 + trim.width;\n\n            h1 = trim.y - (anchor._y * orig.height);\n            h0 = h1 + trim.height;\n        }\n        else\n        {\n            w1 = -anchor._x * orig.width;\n            w0 = w1 + orig.width;\n\n            h1 = -anchor._y * orig.height;\n            h0 = h1 + orig.height;\n        }\n\n        // xy\n        vertexData[0] = (a * w1) + (c * h1) + tx;\n        vertexData[1] = (d * h1) + (b * w1) + ty;\n\n        // xy\n        vertexData[2] = (a * w0) + (c * h1) + tx;\n        vertexData[3] = (d * h1) + (b * w0) + ty;\n\n        // xy\n        vertexData[4] = (a * w0) + (c * h0) + tx;\n        vertexData[5] = (d * h0) + (b * w0) + ty;\n\n        // xy\n        vertexData[6] = (a * w1) + (c * h0) + tx;\n        vertexData[7] = (d * h0) + (b * w1) + ty;\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round(vertexData[i] * resolution) / resolution;\n            }\n        }\n    }\n\n    /**\n     * Calculates worldTransform * vertices for a non texture with a trim. store it in vertexTrimmedData.\n     *\n     * This is used to ensure that the true width and height of a trimmed texture is respected.\n     */\n    public calculateTrimmedVertices(): void\n    {\n        if (!this.vertexTrimmedData)\n        {\n            this.vertexTrimmedData = new Float32Array(8);\n        }\n        else if (this._transformTrimmedID === this.transform._worldID && this._textureTrimmedID === this._texture._updateID)\n        {\n            return;\n        }\n\n        this._transformTrimmedID = this.transform._worldID;\n        this._textureTrimmedID = this._texture._updateID;\n\n        // lets do some special trim code!\n        const texture = this._texture;\n        const vertexData = this.vertexTrimmedData;\n        const orig = texture.orig;\n        const anchor = this._anchor;\n\n        // lets calculate the new untrimmed bounds..\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const w1 = -anchor._x * orig.width;\n        const w0 = w1 + orig.width;\n\n        const h1 = -anchor._y * orig.height;\n        const h0 = h1 + orig.height;\n\n        // xy\n        vertexData[0] = (a * w1) + (c * h1) + tx;\n        vertexData[1] = (d * h1) + (b * w1) + ty;\n\n        // xy\n        vertexData[2] = (a * w0) + (c * h1) + tx;\n        vertexData[3] = (d * h1) + (b * w0) + ty;\n\n        // xy\n        vertexData[4] = (a * w0) + (c * h0) + tx;\n        vertexData[5] = (d * h0) + (b * w0) + ty;\n\n        // xy\n        vertexData[6] = (a * w1) + (c * h0) + tx;\n        vertexData[7] = (d * h0) + (b * w1) + ty;\n\n        if (this._roundPixels)\n        {\n            const resolution = settings.RESOLUTION;\n\n            for (let i = 0; i < vertexData.length; ++i)\n            {\n                vertexData[i] = Math.round(vertexData[i] * resolution) / resolution;\n            }\n        }\n    }\n\n    /**\n     *\n     * Renders the object using the WebGL renderer\n     * @param renderer - The webgl renderer to use.\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.calculateVertices();\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n        renderer.plugins[this.pluginName].render(this);\n    }\n\n    /** Updates the bounds of the sprite. */\n    protected _calculateBounds(): void\n    {\n        const trim = this._texture.trim;\n        const orig = this._texture.orig;\n\n        // First lets check to see if the current texture has a trim..\n        if (!trim || (trim.width === orig.width && trim.height === orig.height))\n        {\n            // no trim! lets use the usual calculations..\n            this.calculateVertices();\n            this._bounds.addQuad(this.vertexData);\n        }\n        else\n        {\n            // lets calculate a special trimmed bounds...\n            this.calculateTrimmedVertices();\n            this._bounds.addQuad(this.vertexTrimmedData);\n        }\n    }\n\n    /**\n     * Gets the local bounds of the sprite object.\n     * @param rect - Optional output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        // we can do a fast local bounds if the sprite has no children!\n        if (this.children.length === 0)\n        {\n            if (!this._localBounds)\n            {\n                this._localBounds = new Bounds();\n            }\n\n            this._localBounds.minX = this._texture.orig.width * -this._anchor._x;\n            this._localBounds.minY = this._texture.orig.height * -this._anchor._y;\n            this._localBounds.maxX = this._texture.orig.width * (1 - this._anchor._x);\n            this._localBounds.maxY = this._texture.orig.height * (1 - this._anchor._y);\n\n            if (!rect)\n            {\n                if (!this._localBoundsRect)\n                {\n                    this._localBoundsRect = new Rectangle();\n                }\n\n                rect = this._localBoundsRect;\n            }\n\n            return this._localBounds.getRectangle(rect);\n        }\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /**\n     * Tests if a point is inside this sprite\n     * @param point - the point to test\n     * @returns The result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const width = this._texture.orig.width;\n        const height = this._texture.orig.height;\n        const x1 = -width * this.anchor.x;\n        let y1 = 0;\n\n        if (tempPoint.x >= x1 && tempPoint.x < x1 + width)\n        {\n            y1 = -height * this.anchor.y;\n\n            if (tempPoint.y >= y1 && tempPoint.y < y1 + height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Destroys this sprite and optionally its texture and children.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param [options.children=false] - if set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param [options.texture=false] - Should it destroy the current texture of the sprite as well\n     * @param [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this._texture.off('update', this._onTextureUpdate, this);\n\n        this._anchor = null;\n\n        const destroyTexture = typeof options === 'boolean' ? options : options?.texture;\n\n        if (destroyTexture)\n        {\n            const destroyBaseTexture = typeof options === 'boolean' ? options : options?.baseTexture;\n\n            this._texture.destroy(!!destroyBaseTexture);\n        }\n\n        this._texture = null;\n    }\n\n    // some helper functions..\n\n    /**\n     * Helper function that creates a new sprite based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @param {string|PIXI.Texture|HTMLImageElement|HTMLVideoElement|ImageBitmap|PIXI.ICanvas} source\n     *     - Source to create texture from\n     * @param {object} [options] - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @returns The newly created sprite\n     */\n    static from(source: SpriteSource, options?: IBaseTextureOptions): Sprite\n    {\n        const texture = (source instanceof Texture)\n            ? source\n            : Texture.from(source, options);\n\n        return new Sprite(texture);\n    }\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     *\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     *\n     * To set the global default, change {@link PIXI.settings.ROUND_PIXELS}.\n     * @default false\n     */\n    set roundPixels(value: boolean)\n    {\n        if (this._roundPixels !== value)\n        {\n            this._transformID = -1;\n            this._transformTrimmedID = -1;\n        }\n        this._roundPixels = value;\n    }\n\n    get roundPixels(): boolean\n    {\n        return this._roundPixels;\n    }\n\n    /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return Math.abs(this.scale.x) * this._texture.orig.width;\n    }\n\n    set width(value: number)\n    {\n        const s = utils.sign(this.scale.x) || 1;\n\n        this.scale.x = s * value / this._texture.orig.width;\n        this._width = value;\n    }\n\n    /** The height of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return Math.abs(this.scale.y) * this._texture.orig.height;\n    }\n\n    set height(value: number)\n    {\n        const s = utils.sign(this.scale.y) || 1;\n\n        this.scale.y = s * value / this._texture.orig.height;\n        this._height = value;\n    }\n\n    /**\n     * The anchor sets the origin point of the sprite. The default value is taken from the {@link PIXI.Texture|Texture}\n     * and passed to the constructor.\n     *\n     * The default is `(0,0)`, this means the sprite's origin is the top left.\n     *\n     * Setting the anchor to `(0.5,0.5)` means the sprite's origin is centered.\n     *\n     * Setting the anchor to `(1,1)` would mean the sprite's origin point will be the bottom right corner.\n     *\n     * If you pass only single parameter, it will set both x and y to the same value as shown in the example below.\n     * @example\n     * import { Sprite } from 'pixi.js';\n     *\n     * const sprite = new Sprite(Texture.WHITE);\n     * sprite.anchor.set(0.5); // This will set the origin to center. (0.5) is same as (0.5, 0.5).\n     */\n    get anchor(): ObservablePoint\n    {\n        return this._anchor;\n    }\n\n    set anchor(value: ObservablePoint)\n    {\n        this._anchor.copyFrom(value);\n    }\n\n    /**\n     * The tint applied to the sprite. This is a hex value.\n     *\n     * A value of 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    get tint(): ColorSource\n    {\n        return this._tintColor.value;\n    }\n\n    set tint(value: ColorSource)\n    {\n        this._tintColor.setValue(value);\n        this._tintRGB = this._tintColor.toLittleEndianNumber();\n    }\n\n    /**\n     * Get the tint as a RGB integer.\n     * @ignore\n     */\n    get tintValue(): number\n    {\n        return this._tintColor.toNumber();\n    }\n\n    /** The texture that the sprite is using. */\n    get texture(): Texture\n    {\n        return this._texture;\n    }\n\n    set texture(value: Texture)\n    {\n        if (this._texture === value)\n        {\n            return;\n        }\n\n        if (this._texture)\n        {\n            this._texture.off('update', this._onTextureUpdate, this);\n        }\n\n        this._texture = value || Texture.EMPTY;\n        this._cachedTint = 0xFFFFFF;\n\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n\n        if (value)\n        {\n            // wait for the texture to load\n            if (value.baseTexture.valid)\n            {\n                this._onTextureUpdate();\n            }\n            else\n            {\n                value.once('update', this._onTextureUpdate, this);\n            }\n        }\n    }\n}\n"], "names": ["Point", "Container", "ObservablePoint", "Color", "BLEND_MODES", "Texture", "settings", "utils", "Bounds", "Rectangle"], "mappings": ";;AAMA,MAAM,YAAY,IAAIA,KAAM,MAAA,GACtB,UAAU,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AA4B3C,MAAM,eAAeC,QAAAA,UAC5B;AAAA;AAAA,EAsGI,YAAY,SACZ;AACU,aAEN,KAAK,UAAU,IAAIC,KAAA;AAAA,MACf,KAAK;AAAA,MACL;AAAA,MACC,UAAU,QAAQ,cAAc,IAAI;AAAA,MACpC,UAAU,QAAQ,cAAc,IAAI;AAAA,IAAA,GAGzC,KAAK,WAAW,MAEhB,KAAK,SAAS,GACd,KAAK,UAAU,GACf,KAAK,aAAa,IAAIC,KAAAA,MAAM,QAAQ,GACpC,KAAK,WAAW,MAEhB,KAAK,OAAO,UACZ,KAAK,YAAYC,KAAA,YAAY,QAC7B,KAAK,cAAc,UACnB,KAAK,MAAM,MAGX,KAAK,UAAU,WAAWC,aAAQ,OAClC,KAAK,aAAa,IAAI,aAAa,CAAC,GACpC,KAAK,oBAAoB,MAEzB,KAAK,eAAe,IACpB,KAAK,aAAa,IAElB,KAAK,sBAAsB,IAC3B,KAAK,oBAAoB,IAIzB,KAAK,UAAU,SAEf,KAAK,aAAa,SAMlB,KAAK,WAAW,IAChB,KAAK,eAAeC,KAAS,SAAA;AAAA,EACjC;AAAA;AAAA,EAGU,mBACV;AACI,SAAK,aAAa,IAClB,KAAK,oBAAoB,IACzB,KAAK,cAAc,UAGf,KAAK,WAEL,KAAK,MAAM,IAAIC,KAAAA,MAAM,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,QAG3E,KAAK,YAEL,KAAK,MAAM,IAAIA,KAAA,MAAM,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAAA,EAEpF;AAAA;AAAA,EAGQ,kBACR;AACS,SAAA,eAAe,IACpB,KAAK,sBAAsB;AAAA,EAC/B;AAAA;AAAA,EAGO,oBACP;AACI,UAAM,UAAU,KAAK;AAErB,QAAI,KAAK,iBAAiB,KAAK,UAAU,YAAY,KAAK,eAAe,QAAQ;AAE7E;AAIA,SAAK,eAAe,QAAQ,cAE5B,KAAK,MAAM,KAAK,SAAS,KAAK,aAGlC,KAAK,eAAe,KAAK,UAAU,UACnC,KAAK,aAAa,QAAQ;AAI1B,UAAM,KAAK,KAAK,UAAU,gBACpB,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,KAAK,GAAG,IACR,KAAK,GAAG,IACR,aAAa,KAAK,YAClB,OAAO,QAAQ,MACf,OAAO,QAAQ,MACf,SAAS,KAAK;AAEpB,QAAI,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK;AAEL,QAAA,QAIA,KAAK,KAAK,IAAK,OAAO,KAAK,KAAK,OAChC,KAAK,KAAK,KAAK,OAEf,KAAK,KAAK,IAAK,OAAO,KAAK,KAAK,QAChC,KAAK,KAAK,KAAK,WAIf,KAAK,CAAC,OAAO,KAAK,KAAK,OACvB,KAAK,KAAK,KAAK,OAEf,KAAK,CAAC,OAAO,KAAK,KAAK,QACvB,KAAK,KAAK,KAAK,SAInB,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAGtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAGtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAGtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAElC,KAAK,cACT;AACI,YAAM,aAAaD,KAAS,SAAA;AAE5B,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AAE1B,mBAAA,CAAC,IAAI,KAAK,MAAM,WAAW,CAAC,IAAI,UAAU,IAAI;AAAA,IAEjE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,2BACP;AACI,QAAI,CAAC,KAAK;AAED,WAAA,oBAAoB,IAAI,aAAa,CAAC;AAAA,aAEtC,KAAK,wBAAwB,KAAK,UAAU,YAAY,KAAK,sBAAsB,KAAK,SAAS;AAEtG;AAGJ,SAAK,sBAAsB,KAAK,UAAU,UAC1C,KAAK,oBAAoB,KAAK,SAAS;AAGjC,UAAA,UAAU,KAAK,UACf,aAAa,KAAK,mBAClB,OAAO,QAAQ,MACf,SAAS,KAAK,SAGd,KAAK,KAAK,UAAU,gBACpB,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,IAAI,GAAG,GACP,KAAK,GAAG,IACR,KAAK,GAAG,IAER,KAAK,CAAC,OAAO,KAAK,KAAK,OACvB,KAAK,KAAK,KAAK,OAEf,KAAK,CAAC,OAAO,KAAK,KAAK,QACvB,KAAK,KAAK,KAAK;AAkBrB,QAfA,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAGtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAGtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAGtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IACtC,WAAW,CAAC,IAAK,IAAI,KAAO,IAAI,KAAM,IAElC,KAAK,cACT;AACI,YAAM,aAAaA,KAAS,SAAA;AAE5B,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE;AAE1B,mBAAA,CAAC,IAAI,KAAK,MAAM,WAAW,CAAC,IAAI,UAAU,IAAI;AAAA,IAEjE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,QAAQ,UAClB;AACI,SAAK,qBAEL,SAAS,MAAM,kBAAkB,SAAS,QAAQ,KAAK,UAAU,CAAC,GAClE,SAAS,QAAQ,KAAK,UAAU,EAAE,OAAO,IAAI;AAAA,EACjD;AAAA;AAAA,EAGU,mBACV;AACI,UAAM,OAAO,KAAK,SAAS,MACrB,OAAO,KAAK,SAAS;AAGvB,KAAC,QAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,UAG5D,KAAK,kBAAkB,GACvB,KAAK,QAAQ,QAAQ,KAAK,UAAU,MAKpC,KAAK,yBAAyB,GAC9B,KAAK,QAAQ,QAAQ,KAAK,iBAAiB;AAAA,EAEnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,MACtB;AAEI,WAAI,KAAK,SAAS,WAAW,KAEpB,KAAK,iBAEN,KAAK,eAAe,IAAIE,QAAAA,OAG5B,IAAA,KAAK,aAAa,OAAO,KAAK,SAAS,KAAK,QAAQ,CAAC,KAAK,QAAQ,IAClE,KAAK,aAAa,OAAO,KAAK,SAAS,KAAK,SAAS,CAAC,KAAK,QAAQ,IACnE,KAAK,aAAa,OAAO,KAAK,SAAS,KAAK,SAAS,IAAI,KAAK,QAAQ,KACtE,KAAK,aAAa,OAAO,KAAK,SAAS,KAAK,UAAU,IAAI,KAAK,QAAQ,KAElE,SAEI,KAAK,qBAEN,KAAK,mBAAmB,IAAIC,KAAA,UAAA,IAGhC,OAAO,KAAK,mBAGT,KAAK,aAAa,aAAa,IAAI,KAGvC,MAAM,eAAe,KAAK,MAAM,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OACrB;AACS,SAAA,eAAe,aAAa,OAAO,SAAS;AAEjD,UAAM,QAAQ,KAAK,SAAS,KAAK,OAC3B,SAAS,KAAK,SAAS,KAAK,QAC5B,KAAK,CAAC,QAAQ,KAAK,OAAO;AAChC,QAAI,KAAK;AAET,WAAI,UAAU,KAAK,MAAM,UAAU,IAAI,KAAK,UAExC,KAAK,CAAC,SAAS,KAAK,OAAO,GAEvB,UAAU,KAAK,MAAM,UAAU,IAAI,KAAK;AAAA,EAOpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,QAAQ,SACf;AACI,QAAA,MAAM,QAAQ,OAAO,GAErB,KAAK,SAAS,IAAI,UAAU,KAAK,kBAAkB,IAAI,GAEvD,KAAK,UAAU,MAEQ,OAAO,WAAY,YAAY,UAAU,SAAS,SAGzE;AACI,YAAM,qBAAqB,OAAO,WAAY,YAAY,UAAU,SAAS;AAE7E,WAAK,SAAS,QAAQ,CAAC,CAAC,kBAAkB;AAAA,IAC9C;AAEA,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,KAAK,QAAsB,SAClC;AACI,UAAM,UAAW,kBAAkBJ,eAC7B,SACAA,KAAAA,QAAQ,KAAK,QAAQ,OAAO;AAE3B,WAAA,IAAI,OAAO,OAAO;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,YAAY,OAChB;AACQ,SAAK,iBAAiB,UAEtB,KAAK,eAAe,IACpB,KAAK,sBAAsB,KAE/B,KAAK,eAAe;AAAA,EACxB;AAAA,EAEA,IAAI,cACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAGA,IAAI,QACJ;AACW,WAAA,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,KAAK;AAAA,EACvD;AAAA,EAEA,IAAI,MAAM,OACV;AACI,UAAM,IAAIE,KAAAA,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK;AAEjC,SAAA,MAAM,IAAI,IAAI,QAAQ,KAAK,SAAS,KAAK,OAC9C,KAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,SACJ;AACW,WAAA,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,SAAS,KAAK;AAAA,EACvD;AAAA,EAEA,IAAI,OAAO,OACX;AACI,UAAM,IAAIA,KAAAA,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK;AAEjC,SAAA,MAAM,IAAI,IAAI,QAAQ,KAAK,SAAS,KAAK,QAC9C,KAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,IAAI,SACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,OAAO,OACX;AACS,SAAA,QAAQ,SAAS,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OACJ;AACI,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA,EAEA,IAAI,KAAK,OACT;AACS,SAAA,WAAW,SAAS,KAAK,GAC9B,KAAK,WAAW,KAAK,WAAW;EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YACJ;AACW,WAAA,KAAK,WAAW;EAC3B;AAAA;AAAA,EAGA,IAAI,UACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,QAAQ,OACZ;AACQ,SAAK,aAAa,UAKlB,KAAK,YAEL,KAAK,SAAS,IAAI,UAAU,KAAK,kBAAkB,IAAI,GAG3D,KAAK,WAAW,SAASF,KAAAA,QAAQ,OACjC,KAAK,cAAc,UAEnB,KAAK,aAAa,IAClB,KAAK,oBAAoB,IAErB,UAGI,MAAM,YAAY,QAElB,KAAK,qBAIL,MAAM,KAAK,UAAU,KAAK,kBAAkB,IAAI;AAAA,EAG5D;AACJ;;"}