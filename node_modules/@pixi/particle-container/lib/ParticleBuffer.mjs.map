{"version": 3, "file": "ParticleBuffer.mjs", "sources": ["../src/ParticleBuffer.ts"], "sourcesContent": ["import { Buffer, Geometry, TYPES, utils } from '@pixi/core';\n\nimport type { Sprite } from '@pixi/sprite';\nimport type { IParticleRendererProperty } from './ParticleRenderer';\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that\n * they now share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleBuffer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleBuffer.java\n */\n\n/**\n * The particle buffer manages the static and dynamic buffers for a particle container.\n * @private\n * @memberof PIXI\n */\nexport class ParticleBuffer\n{\n    public geometry: Geometry;\n    public staticStride: number;\n    public staticBuffer: Buffer;\n    public staticData: Float32Array;\n    public staticDataUint32: Uint32Array;\n    public dynamicStride: number;\n    public dynamicBuffer: Buffer;\n    public dynamicData: Float32Array;\n    public dynamicDataUint32: Uint32Array;\n    public _updateID: number;\n\n    /** Holds the indices of the geometry (quads) to draw. */\n    indexBuffer: Buffer;\n\n    /** The number of particles the buffer can hold. */\n    private size: number;\n\n    /** A list of the properties that are dynamic. */\n    private dynamicProperties: IParticleRendererProperty[];\n\n    /** A list of the properties that are static. */\n    private staticProperties: IParticleRendererProperty[];\n\n    /**\n     * @param {object} properties - The properties to upload.\n     * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.\n     * @param {number} size - The size of the batch.\n     */\n    constructor(properties: IParticleRendererProperty[], dynamicPropertyFlags: boolean[], size: number)\n    {\n        this.geometry = new Geometry();\n\n        this.indexBuffer = null;\n\n        this.size = size;\n        this.dynamicProperties = [];\n        this.staticProperties = [];\n\n        for (let i = 0; i < properties.length; ++i)\n        {\n            let property = properties[i];\n\n            // Make copy of properties object so that when we edit the offset it doesn't\n            // change all other instances of the object literal\n            property = {\n                attributeName: property.attributeName,\n                size: property.size,\n                uploadFunction: property.uploadFunction,\n                type: property.type || TYPES.FLOAT,\n                offset: property.offset,\n            };\n\n            if (dynamicPropertyFlags[i])\n            {\n                this.dynamicProperties.push(property);\n            }\n            else\n            {\n                this.staticProperties.push(property);\n            }\n        }\n\n        this.staticStride = 0;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n\n        this.dynamicStride = 0;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this._updateID = 0;\n\n        this.initBuffers();\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    private initBuffers(): void\n    {\n        const geometry = this.geometry;\n\n        let dynamicOffset = 0;\n\n        this.indexBuffer = new Buffer(utils.createIndicesForQuads(this.size), true, true);\n        geometry.addIndex(this.indexBuffer);\n\n        this.dynamicStride = 0;\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.offset = dynamicOffset;\n            dynamicOffset += property.size;\n            this.dynamicStride += property.size;\n        }\n\n        const dynBuffer = new ArrayBuffer(this.size * this.dynamicStride * 4 * 4);\n\n        this.dynamicData = new Float32Array(dynBuffer);\n        this.dynamicDataUint32 = new Uint32Array(dynBuffer);\n        this.dynamicBuffer = new Buffer(this.dynamicData, false, false);\n\n        // static //\n        let staticOffset = 0;\n\n        this.staticStride = 0;\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            property.offset = staticOffset;\n            staticOffset += property.size;\n            this.staticStride += property.size;\n        }\n\n        const statBuffer = new ArrayBuffer(this.size * this.staticStride * 4 * 4);\n\n        this.staticData = new Float32Array(statBuffer);\n        this.staticDataUint32 = new Uint32Array(statBuffer);\n        this.staticBuffer = new Buffer(this.staticData, true, false);\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.dynamicBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.dynamicStride * 4,\n                property.offset * 4\n            );\n        }\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.staticBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.staticStride * 4,\n                property.offset * 4\n            );\n        }\n    }\n\n    /**\n     * Uploads the dynamic properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadDynamic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.dynamicProperties.length; i++)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.dynamicDataUint32 : this.dynamicData,\n                this.dynamicStride, property.offset);\n        }\n\n        this.dynamicBuffer._updateID++;\n    }\n\n    /**\n     * Uploads the static properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadStatic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.staticProperties.length; i++)\n        {\n            const property = this.staticProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.staticDataUint32 : this.staticData,\n                this.staticStride, property.offset);\n        }\n\n        this.staticBuffer._updateID++;\n    }\n\n    /** Destroys the ParticleBuffer. */\n    destroy(): void\n    {\n        this.indexBuffer = null;\n\n        this.dynamicProperties = null;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this.staticProperties = null;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n        // all buffers are destroyed inside geometry\n        this.geometry.destroy();\n    }\n}\n"], "names": [], "mappings": ";AAsBO,MAAM,eACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BI,YAAY,YAAyC,sBAAiC,MACtF;AACI,SAAK,WAAW,IAAI,SAEpB,GAAA,KAAK,cAAc,MAEnB,KAAK,OAAO,MACZ,KAAK,oBAAoB,CACzB,GAAA,KAAK,mBAAmB;AAExB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GACzC;AACQ,UAAA,WAAW,WAAW,CAAC;AAIhB,iBAAA;AAAA,QACP,eAAe,SAAS;AAAA,QACxB,MAAM,SAAS;AAAA,QACf,gBAAgB,SAAS;AAAA,QACzB,MAAM,SAAS,QAAQ,MAAM;AAAA,QAC7B,QAAQ,SAAS;AAAA,MAGjB,GAAA,qBAAqB,CAAC,IAEtB,KAAK,kBAAkB,KAAK,QAAQ,IAIpC,KAAK,iBAAiB,KAAK,QAAQ;AAAA,IAE3C;AAEK,SAAA,eAAe,GACpB,KAAK,eAAe,MACpB,KAAK,aAAa,MAClB,KAAK,mBAAmB,MAExB,KAAK,gBAAgB,GACrB,KAAK,gBAAgB,MACrB,KAAK,cAAc,MACnB,KAAK,oBAAoB,MAEzB,KAAK,YAAY,GAEjB,KAAK,YAAY;AAAA,EACrB;AAAA;AAAA,EAGQ,cACR;AACI,UAAM,WAAW,KAAK;AAEtB,QAAI,gBAAgB;AAEpB,SAAK,cAAc,IAAI,OAAO,MAAM,sBAAsB,KAAK,IAAI,GAAG,IAAM,EAAI,GAChF,SAAS,SAAS,KAAK,WAAW,GAElC,KAAK,gBAAgB;AAErB,aAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,EAAE,GACrD;AACU,YAAA,WAAW,KAAK,kBAAkB,CAAC;AAEzC,eAAS,SAAS,eAClB,iBAAiB,SAAS,MAC1B,KAAK,iBAAiB,SAAS;AAAA,IACnC;AAEM,UAAA,YAAY,IAAI,YAAY,KAAK,OAAO,KAAK,gBAAgB,IAAI,CAAC;AAExE,SAAK,cAAc,IAAI,aAAa,SAAS,GAC7C,KAAK,oBAAoB,IAAI,YAAY,SAAS,GAClD,KAAK,gBAAgB,IAAI,OAAO,KAAK,aAAa,IAAO,EAAK;AAG9D,QAAI,eAAe;AAEnB,SAAK,eAAe;AAEpB,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,EAAE,GACpD;AACU,YAAA,WAAW,KAAK,iBAAiB,CAAC;AAExC,eAAS,SAAS,cAClB,gBAAgB,SAAS,MACzB,KAAK,gBAAgB,SAAS;AAAA,IAClC;AAEM,UAAA,aAAa,IAAI,YAAY,KAAK,OAAO,KAAK,eAAe,IAAI,CAAC;AAExE,SAAK,aAAa,IAAI,aAAa,UAAU,GAC7C,KAAK,mBAAmB,IAAI,YAAY,UAAU,GAClD,KAAK,eAAe,IAAI,OAAO,KAAK,YAAY,IAAM,EAAK;AAE3D,aAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,EAAE,GACrD;AACU,YAAA,WAAW,KAAK,kBAAkB,CAAC;AAEhC,eAAA;AAAA,QACL,SAAS;AAAA,QACT,KAAK;AAAA,QACL;AAAA,QACA,SAAS,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,QACT,KAAK,gBAAgB;AAAA,QACrB,SAAS,SAAS;AAAA,MAAA;AAAA,IAE1B;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,EAAE,GACpD;AACU,YAAA,WAAW,KAAK,iBAAiB,CAAC;AAE/B,eAAA;AAAA,QACL,SAAS;AAAA,QACT,KAAK;AAAA,QACL;AAAA,QACA,SAAS,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,QACT,KAAK,eAAe;AAAA,QACpB,SAAS,SAAS;AAAA,MAAA;AAAA,IAE1B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,UAAoB,YAAoB,QACtD;AACI,aAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,KACnD;AACU,YAAA,WAAW,KAAK,kBAAkB,CAAC;AAEhC,eAAA;AAAA,QAAe;AAAA,QAAU;AAAA,QAAY;AAAA,QAC1C,SAAS,SAAS,MAAM,gBAAgB,KAAK,oBAAoB,KAAK;AAAA,QACtE,KAAK;AAAA,QAAe,SAAS;AAAA,MAAA;AAAA,IACrC;AAEA,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,UAAoB,YAAoB,QACrD;AACI,aAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,KAClD;AACU,YAAA,WAAW,KAAK,iBAAiB,CAAC;AAE/B,eAAA;AAAA,QAAe;AAAA,QAAU;AAAA,QAAY;AAAA,QAC1C,SAAS,SAAS,MAAM,gBAAgB,KAAK,mBAAmB,KAAK;AAAA,QACrE,KAAK;AAAA,QAAc,SAAS;AAAA,MAAA;AAAA,IACpC;AAEA,SAAK,aAAa;AAAA,EACtB;AAAA;AAAA,EAGA,UACA;AACI,SAAK,cAAc,MAEnB,KAAK,oBAAoB,MACzB,KAAK,gBAAgB,MACrB,KAAK,cAAc,MACnB,KAAK,oBAAoB,MAEzB,KAAK,mBAAmB,MACxB,KAAK,eAAe,MACpB,KAAK,aAAa,MAClB,KAAK,mBAAmB,MAExB,KAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;"}