{"version": 3, "file": "ParticleRenderer.js", "sources": ["../src/ParticleRenderer.ts"], "sourcesContent": ["import { Color, extensions, ExtensionType, Matrix, ObjectRenderer, Shader, State, TYPES, utils } from '@pixi/core';\nimport { ParticleBuffer } from './ParticleBuffer';\nimport fragment from './particles.frag';\nimport vertex from './particles.vert';\n\nimport type { ExtensionMetadata, Renderer } from '@pixi/core';\nimport type { Sprite } from '@pixi/sprite';\nimport type { ParticleContainer } from './ParticleContainer';\n\nexport interface IParticleRendererProperty\n{\n    attributeName: string;\n    size: number;\n    type?: TYPES;\n    uploadFunction: (...params: any[]) => any;\n    offset: number;\n}\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that they now\n * share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleRenderer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleRenderer.java\n */\n\n/**\n * Renderer for Particles that is designer for speed over feature set.\n * @memberof PIXI\n */\nexport class ParticleRenderer extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'particle',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /** The WebGL state in which this renderer will work. */\n    public readonly state: State;\n\n    /** The default shader that is used if a sprite doesn't have a more specific one. */\n    public shader: Shader;\n    public tempMatrix: Matrix;\n    public properties: IParticleRendererProperty[];\n\n    /**\n     * @param renderer - The renderer this sprite batch works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        // and max number of element in the index buffer is 16384 * 6 = 98304\n        // Creating a full index buffer, overhead is 98304 * 2 = 196Ko\n        // let numIndices = 98304;\n\n        this.shader = null;\n\n        this.properties = null;\n\n        this.tempMatrix = new Matrix();\n\n        this.properties = [\n            // verticesData\n            {\n                attributeName: 'aVertexPosition',\n                size: 2,\n                uploadFunction: this.uploadVertices,\n                offset: 0,\n            },\n            // positionData\n            {\n                attributeName: 'aPositionCoord',\n                size: 2,\n                uploadFunction: this.uploadPosition,\n                offset: 0,\n            },\n            // rotationData\n            {\n                attributeName: 'aRotation',\n                size: 1,\n                uploadFunction: this.uploadRotation,\n                offset: 0,\n            },\n            // uvsData\n            {\n                attributeName: 'aTextureCoord',\n                size: 2,\n                uploadFunction: this.uploadUvs,\n                offset: 0,\n            },\n            // tintData\n            {\n                attributeName: 'aColor',\n                size: 1,\n                type: TYPES.UNSIGNED_BYTE,\n                uploadFunction: this.uploadTint,\n                offset: 0,\n            },\n        ];\n\n        this.shader = Shader.from(vertex, fragment, {});\n        this.state = State.for2d();\n    }\n\n    /**\n     * Renders the particle container object.\n     * @param container - The container to render using this ParticleRenderer.\n     */\n    public render(container: ParticleContainer): void\n    {\n        const children = container.children;\n        const maxSize = container._maxSize;\n        const batchSize = container._batchSize;\n        const renderer = this.renderer;\n        let totalChildren = children.length;\n\n        if (totalChildren === 0)\n        {\n            return;\n        }\n        else if (totalChildren > maxSize && !container.autoResize)\n        {\n            totalChildren = maxSize;\n        }\n\n        let buffers = container._buffers;\n\n        if (!buffers)\n        {\n            buffers = container._buffers = this.generateBuffers(container);\n        }\n\n        const baseTexture = children[0]._texture.baseTexture;\n        const premultiplied = baseTexture.alphaMode > 0;\n\n        // if the uvs have not updated then no point rendering just yet!\n        this.state.blendMode = utils.correctBlendMode(container.blendMode, premultiplied);\n        renderer.state.set(this.state);\n\n        const gl = renderer.gl;\n\n        const m = container.worldTransform.copyTo(this.tempMatrix);\n\n        m.prepend(renderer.globalUniforms.uniforms.projectionMatrix);\n\n        this.shader.uniforms.translationMatrix = m.toArray(true);\n\n        this.shader.uniforms.uColor = Color.shared\n            .setValue(container.tintRgb)\n            .premultiply(container.worldAlpha, premultiplied)\n            .toArray(this.shader.uniforms.uColor);\n\n        this.shader.uniforms.uSampler = baseTexture;\n\n        this.renderer.shader.bind(this.shader);\n\n        let updateStatic = false;\n\n        // now lets upload and render the buffers..\n        for (let i = 0, j = 0; i < totalChildren; i += batchSize, j += 1)\n        {\n            let amount = (totalChildren - i);\n\n            if (amount > batchSize)\n            {\n                amount = batchSize;\n            }\n\n            if (j >= buffers.length)\n            {\n                buffers.push(this._generateOneMoreBuffer(container));\n            }\n\n            const buffer = buffers[j];\n\n            // we always upload the dynamic\n            buffer.uploadDynamic(children, i, amount);\n\n            const bid = container._bufferUpdateIDs[j] || 0;\n\n            updateStatic = updateStatic || (buffer._updateID < bid);\n            // we only upload the static content when we have to!\n            if (updateStatic)\n            {\n                buffer._updateID = container._updateID;\n                buffer.uploadStatic(children, i, amount);\n            }\n\n            // bind the buffer\n            renderer.geometry.bind(buffer.geometry);\n            gl.drawElements(gl.TRIANGLES, amount * 6, gl.UNSIGNED_SHORT, 0);\n        }\n    }\n\n    /**\n     * Creates one particle buffer for each child in the container we want to render and updates internal properties.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The buffers\n     */\n    private generateBuffers(container: ParticleContainer): ParticleBuffer[]\n    {\n        const buffers = [];\n        const size = container._maxSize;\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        for (let i = 0; i < size; i += batchSize)\n        {\n            buffers.push(new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize));\n        }\n\n        return buffers;\n    }\n\n    /**\n     * Creates one more particle buffer, because container has autoResize feature.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The generated buffer\n     */\n    private _generateOneMoreBuffer(container: ParticleContainer): ParticleBuffer\n    {\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        return new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize);\n    }\n\n    /**\n     * Uploads the vertices.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their vertices uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadVertices(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const texture = sprite._texture;\n            const sx = sprite.scale.x;\n            const sy = sprite.scale.y;\n            const trim = texture.trim;\n            const orig = texture.orig;\n\n            if (trim)\n            {\n                // if the sprite is trimmed and is not a tilingsprite then we need to add the\n                // extra space before transforming the sprite coords..\n                w1 = trim.x - (sprite.anchor.x * orig.width);\n                w0 = w1 + trim.width;\n\n                h1 = trim.y - (sprite.anchor.y * orig.height);\n                h0 = h1 + trim.height;\n            }\n            else\n            {\n                w0 = (orig.width) * (1 - sprite.anchor.x);\n                w1 = (orig.width) * -sprite.anchor.x;\n\n                h0 = orig.height * (1 - sprite.anchor.y);\n                h1 = orig.height * -sprite.anchor.y;\n            }\n\n            array[offset] = w1 * sx;\n            array[offset + 1] = h1 * sy;\n\n            array[offset + stride] = w0 * sx;\n            array[offset + stride + 1] = h1 * sy;\n\n            array[offset + (stride * 2)] = w0 * sx;\n            array[offset + (stride * 2) + 1] = h0 * sy;\n\n            array[offset + (stride * 3)] = w1 * sx;\n            array[offset + (stride * 3) + 1] = h0 * sy;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the position.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their positions uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadPosition(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spritePosition = children[startIndex + i].position;\n\n            array[offset] = spritePosition.x;\n            array[offset + 1] = spritePosition.y;\n\n            array[offset + stride] = spritePosition.x;\n            array[offset + stride + 1] = spritePosition.y;\n\n            array[offset + (stride * 2)] = spritePosition.x;\n            array[offset + (stride * 2) + 1] = spritePosition.y;\n\n            array[offset + (stride * 3)] = spritePosition.x;\n            array[offset + (stride * 3) + 1] = spritePosition.y;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the rotation.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadRotation(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spriteRotation = children[startIndex + i].rotation;\n\n            array[offset] = spriteRotation;\n            array[offset + stride] = spriteRotation;\n            array[offset + (stride * 2)] = spriteRotation;\n            array[offset + (stride * 3)] = spriteRotation;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the UVs.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadUvs(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const textureUvs = children[startIndex + i]._texture._uvs;\n\n            if (textureUvs)\n            {\n                array[offset] = textureUvs.x0;\n                array[offset + 1] = textureUvs.y0;\n\n                array[offset + stride] = textureUvs.x1;\n                array[offset + stride + 1] = textureUvs.y1;\n\n                array[offset + (stride * 2)] = textureUvs.x2;\n                array[offset + (stride * 2) + 1] = textureUvs.y2;\n\n                array[offset + (stride * 3)] = textureUvs.x3;\n                array[offset + (stride * 3) + 1] = textureUvs.y3;\n\n                offset += stride * 4;\n            }\n            else\n            {\n                // TODO you know this can be easier!\n                array[offset] = 0;\n                array[offset + 1] = 0;\n\n                array[offset + stride] = 0;\n                array[offset + stride + 1] = 0;\n\n                array[offset + (stride * 2)] = 0;\n                array[offset + (stride * 2) + 1] = 0;\n\n                array[offset + (stride * 3)] = 0;\n                array[offset + (stride * 3) + 1] = 0;\n\n                offset += stride * 4;\n            }\n        }\n    }\n\n    /**\n     * Uploads the tint.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadTint(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const result = Color.shared\n                .setValue(sprite._tintRGB)\n                .toPremultiplied(sprite.alpha, sprite.texture.baseTexture.alphaMode > 0);\n\n            array[offset] = result;\n            array[offset + stride] = result;\n            array[offset + (stride * 2)] = result;\n            array[offset + (stride * 3)] = result;\n\n            offset += stride * 4;\n        }\n    }\n\n    /** Destroys the ParticleRenderer. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        if (this.shader)\n        {\n            this.shader.destroy();\n            this.shader = null;\n        }\n\n        this.tempMatrix = null;\n    }\n}\n\nextensions.add(ParticleRenderer);\n"], "names": ["O<PERSON><PERSON><PERSON><PERSON>", "Matrix", "TYPES", "Shader", "vertex", "fragment", "State", "utils", "Color", "ParticleBuffer", "ExtensionType", "extensions"], "mappings": ";;AAkCO,MAAM,yBAAyBA,KAAAA,eACtC;AAAA;AAAA;AAAA;AAAA,EAkBI,YAAY,UACZ;AACI,UAAM,QAAQ,GAQT,KAAA,SAAS,MAEd,KAAK,aAAa,MAElB,KAAK,aAAa,IAAIC,KAAAA,UAEtB,KAAK,aAAa;AAAA;AAAA,MAEd;AAAA,QACI,eAAe;AAAA,QACf,MAAM;AAAA,QACN,gBAAgB,KAAK;AAAA,QACrB,QAAQ;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACI,eAAe;AAAA,QACf,MAAM;AAAA,QACN,gBAAgB,KAAK;AAAA,QACrB,QAAQ;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACI,eAAe;AAAA,QACf,MAAM;AAAA,QACN,gBAAgB,KAAK;AAAA,QACrB,QAAQ;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACI,eAAe;AAAA,QACf,MAAM;AAAA,QACN,gBAAgB,KAAK;AAAA,QACrB,QAAQ;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACI,eAAe;AAAA,QACf,MAAM;AAAA,QACN,MAAMC,KAAM,MAAA;AAAA,QACZ,gBAAgB,KAAK;AAAA,QACrB,QAAQ;AAAA,MACZ;AAAA,IAGJ,GAAA,KAAK,SAASC,YAAO,KAAKC,UAAAA,SAAQC,YAAAA,SAAU,CAAE,CAAA,GAC9C,KAAK,QAAQC,WAAM,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,OAAO,WACd;AACU,UAAA,WAAW,UAAU,UACrB,UAAU,UAAU,UACpB,YAAY,UAAU,YACtB,WAAW,KAAK;AACtB,QAAI,gBAAgB,SAAS;AAE7B,QAAI,kBAAkB;AAElB;AAEK,oBAAgB,WAAW,CAAC,UAAU,eAE3C,gBAAgB;AAGpB,QAAI,UAAU,UAAU;AAEnB,gBAED,UAAU,UAAU,WAAW,KAAK,gBAAgB,SAAS;AAG3D,UAAA,cAAc,SAAS,CAAC,EAAE,SAAS,aACnC,gBAAgB,YAAY,YAAY;AAG9C,SAAK,MAAM,YAAYC,KAAAA,MAAM,iBAAiB,UAAU,WAAW,aAAa,GAChF,SAAS,MAAM,IAAI,KAAK,KAAK;AAEvB,UAAA,KAAK,SAAS,IAEd,IAAI,UAAU,eAAe,OAAO,KAAK,UAAU;AAEvD,MAAA,QAAQ,SAAS,eAAe,SAAS,gBAAgB,GAE3D,KAAK,OAAO,SAAS,oBAAoB,EAAE,QAAQ,EAAI,GAEvD,KAAK,OAAO,SAAS,SAASC,KAAA,MAAM,OAC/B,SAAS,UAAU,OAAO,EAC1B,YAAY,UAAU,YAAY,aAAa,EAC/C,QAAQ,KAAK,OAAO,SAAS,MAAM,GAExC,KAAK,OAAO,SAAS,WAAW,aAEhC,KAAK,SAAS,OAAO,KAAK,KAAK,MAAM;AAErC,QAAI,eAAe;AAGV,aAAA,IAAI,GAAG,IAAI,GAAG,IAAI,eAAe,KAAK,WAAW,KAAK,GAC/D;AACI,UAAI,SAAU,gBAAgB;AAE1B,eAAS,cAET,SAAS,YAGT,KAAK,QAAQ,UAEb,QAAQ,KAAK,KAAK,uBAAuB,SAAS,CAAC;AAGjD,YAAA,SAAS,QAAQ,CAAC;AAGjB,aAAA,cAAc,UAAU,GAAG,MAAM;AAExC,YAAM,MAAM,UAAU,iBAAiB,CAAC,KAAK;AAE7C,qBAAe,gBAAiB,OAAO,YAAY,KAE/C,iBAEA,OAAO,YAAY,UAAU,WAC7B,OAAO,aAAa,UAAU,GAAG,MAAM,IAI3C,SAAS,SAAS,KAAK,OAAO,QAAQ,GACtC,GAAG,aAAa,GAAG,WAAW,SAAS,GAAG,GAAG,gBAAgB,CAAC;AAAA,IAClE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,gBAAgB,WACxB;AACU,UAAA,UAAU,CAAA,GACV,OAAO,UAAU,UACjB,YAAY,UAAU,YACtB,uBAAuB,UAAU;AAEvC,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAE3B,cAAQ,KAAK,IAAIC,8BAAe,KAAK,YAAY,sBAAsB,SAAS,CAAC;AAG9E,WAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,uBAAuB,WAC/B;AACI,UAAM,YAAY,UAAU,YACtB,uBAAuB,UAAU;AAEvC,WAAO,IAAIA,eAAA,eAAe,KAAK,YAAY,sBAAsB,SAAS;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,eACH,UAAoB,YAAoB,QACxC,OAAiB,QAAgB,QAErC;AACI,QAAI,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK;AAET,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAC9B;AACU,YAAA,SAAS,SAAS,aAAa,CAAC,GAChC,UAAU,OAAO,UACjB,KAAK,OAAO,MAAM,GAClB,KAAK,OAAO,MAAM,GAClB,OAAO,QAAQ,MACf,OAAO,QAAQ;AAEjB,cAIA,KAAK,KAAK,IAAK,OAAO,OAAO,IAAI,KAAK,OACtC,KAAK,KAAK,KAAK,OAEf,KAAK,KAAK,IAAK,OAAO,OAAO,IAAI,KAAK,QACtC,KAAK,KAAK,KAAK,WAIf,KAAM,KAAK,SAAU,IAAI,OAAO,OAAO,IACvC,KAAM,KAAK,QAAS,CAAC,OAAO,OAAO,GAEnC,KAAK,KAAK,UAAU,IAAI,OAAO,OAAO,IACtC,KAAK,KAAK,SAAS,CAAC,OAAO,OAAO,IAGtC,MAAM,MAAM,IAAI,KAAK,IACrB,MAAM,SAAS,CAAC,IAAI,KAAK,IAEzB,MAAM,SAAS,MAAM,IAAI,KAAK,IAC9B,MAAM,SAAS,SAAS,CAAC,IAAI,KAAK,IAElC,MAAM,SAAU,SAAS,CAAE,IAAI,KAAK,IACpC,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,KAAK,IAExC,MAAM,SAAU,SAAS,CAAE,IAAI,KAAK,IACpC,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,KAAK,IAExC,UAAU,SAAS;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,eACH,UAAoB,YAAoB,QACxC,OAAiB,QAAgB,QAErC;AACI,aAAS,IAAI,GAAG,IAAI,QAAQ,KAC5B;AACI,YAAM,iBAAiB,SAAS,aAAa,CAAC,EAAE;AAE1C,YAAA,MAAM,IAAI,eAAe,GAC/B,MAAM,SAAS,CAAC,IAAI,eAAe,GAEnC,MAAM,SAAS,MAAM,IAAI,eAAe,GACxC,MAAM,SAAS,SAAS,CAAC,IAAI,eAAe,GAE5C,MAAM,SAAU,SAAS,CAAE,IAAI,eAAe,GAC9C,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,eAAe,GAElD,MAAM,SAAU,SAAS,CAAE,IAAI,eAAe,GAC9C,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,eAAe,GAElD,UAAU,SAAS;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,eACH,UAAoB,YAAoB,QACxC,OAAiB,QAAgB,QAErC;AACI,aAAS,IAAI,GAAG,IAAI,QAAQ,KAC5B;AACI,YAAM,iBAAiB,SAAS,aAAa,CAAC,EAAE;AAE1C,YAAA,MAAM,IAAI,gBAChB,MAAM,SAAS,MAAM,IAAI,gBACzB,MAAM,SAAU,SAAS,CAAE,IAAI,gBAC/B,MAAM,SAAU,SAAS,CAAE,IAAI,gBAE/B,UAAU,SAAS;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,UACH,UAAoB,YAAoB,QACxC,OAAiB,QAAgB,QAErC;AACI,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAC9B;AACI,YAAM,aAAa,SAAS,aAAa,CAAC,EAAE,SAAS;AAEjD,oBAEA,MAAM,MAAM,IAAI,WAAW,IAC3B,MAAM,SAAS,CAAC,IAAI,WAAW,IAE/B,MAAM,SAAS,MAAM,IAAI,WAAW,IACpC,MAAM,SAAS,SAAS,CAAC,IAAI,WAAW,IAExC,MAAM,SAAU,SAAS,CAAE,IAAI,WAAW,IAC1C,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,WAAW,IAE9C,MAAM,SAAU,SAAS,CAAE,IAAI,WAAW,IAC1C,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,WAAW,IAE9C,UAAU,SAAS,MAKnB,MAAM,MAAM,IAAI,GAChB,MAAM,SAAS,CAAC,IAAI,GAEpB,MAAM,SAAS,MAAM,IAAI,GACzB,MAAM,SAAS,SAAS,CAAC,IAAI,GAE7B,MAAM,SAAU,SAAS,CAAE,IAAI,GAC/B,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,GAEnC,MAAM,SAAU,SAAS,CAAE,IAAI,GAC/B,MAAM,SAAU,SAAS,IAAK,CAAC,IAAI,GAEnC,UAAU,SAAS;AAAA,IAE3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,WACH,UAAoB,YAAoB,QACxC,OAAiB,QAAgB,QAErC;AACI,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAC9B;AACI,YAAM,SAAS,SAAS,aAAa,CAAC,GAChC,SAASD,KAAAA,MAAM,OAChB,SAAS,OAAO,QAAQ,EACxB,gBAAgB,OAAO,OAAO,OAAO,QAAQ,YAAY,YAAY,CAAC;AAErE,YAAA,MAAM,IAAI,QAChB,MAAM,SAAS,MAAM,IAAI,QACzB,MAAM,SAAU,SAAS,CAAE,IAAI,QAC/B,MAAM,SAAU,SAAS,CAAE,IAAI,QAE/B,UAAU,SAAS;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA,EAGO,UACP;AACI,UAAM,QAAQ,GAEV,KAAK,WAEL,KAAK,OAAO,QAAQ,GACpB,KAAK,SAAS,OAGlB,KAAK,aAAa;AAAA,EACtB;AACJ;AAtaa,iBAGF,YAA+B;AAAA,EAClC,MAAM;AAAA,EACN,MAAME,KAAc,cAAA;AACxB;AAkaJC,KAAAA,WAAW,IAAI,gBAAgB;;"}