{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/// <reference path=\"../global.d.ts\" />\nimport { BaseTexture, Matrix, Rectangle, RenderTexture, Texture, utils } from '@pixi/core';\nimport { DisplayObject } from '@pixi/display';\nimport { Sprite } from '@pixi/sprite';\n\nimport type {\n    ICanvasRenderingContext2D,\n    IPointData,\n    IRenderer,\n    MaskData,\n    MSAA_QUALITY,\n    Renderer,\n} from '@pixi/core';\nimport type { Container, IDestroyOptions } from '@pixi/display';\n\n// Don't import CanvasRender to remove dependency on this optional package\n// this type should satisify these requirements for cacheAsBitmap types\ninterface CanvasRenderer extends IRenderer\n{\n    canvasContext: {\n        activeContext: ICanvasRenderingContext2D;\n    }\n}\n\nconst _tempMatrix = new Matrix();\n\nDisplayObject.prototype._cacheAsBitmap = false;\nDisplayObject.prototype._cacheData = null;\nDisplayObject.prototype._cacheAsBitmapResolution = null;\nDisplayObject.prototype._cacheAsBitmapMultisample = null;\n\n// figured there's no point adding ALL the extra variables to prototype.\n// this model can hold the information needed. This can also be generated on demand as\n// most objects are not cached as bitmaps.\n/**\n * @class\n * @ignore\n * @private\n */\nexport class CacheData\n{\n    public textureCacheId: string;\n    public originalRender: (renderer: Renderer) => void;\n    public originalRenderCanvas: (renderer: IRenderer) => void;\n    public originalCalculateBounds: () => void;\n    public originalGetLocalBounds: (rect?: Rectangle) => Rectangle;\n    public originalUpdateTransform: () => void;\n    public originalDestroy: (options?: IDestroyOptions | boolean) => void;\n    public originalMask: Container | MaskData;\n    public originalFilterArea: Rectangle;\n    public originalContainsPoint: (point: IPointData) => boolean;\n    public sprite: Sprite;\n\n    constructor()\n    {\n        this.textureCacheId = null;\n\n        this.originalRender = null;\n        this.originalRenderCanvas = null;\n        this.originalCalculateBounds = null;\n        this.originalGetLocalBounds = null;\n\n        this.originalUpdateTransform = null;\n        this.originalDestroy = null;\n        this.originalMask = null;\n        this.originalFilterArea = null;\n        this.originalContainsPoint = null;\n        this.sprite = null;\n    }\n}\n\nObject.defineProperties(DisplayObject.prototype, {\n    /**\n     * The resolution to use for cacheAsBitmap. By default this will use the renderer's resolution\n     * but can be overriden for performance. Lower values will reduce memory usage at the expense\n     * of render quality. A falsey value of `null` or `0` will default to the renderer's resolution.\n     * If `cacheAsBitmap` is set to `true`, this will re-render with the new resolution.\n     * @member {number|null} cacheAsBitmapResolution\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     */\n    cacheAsBitmapResolution: {\n        get(): number | null\n        {\n            return this._cacheAsBitmapResolution;\n        },\n        set(resolution: number | null): void\n        {\n            if (resolution === this._cacheAsBitmapResolution)\n            {\n                return;\n            }\n\n            this._cacheAsBitmapResolution = resolution;\n\n            if (this.cacheAsBitmap)\n            {\n                // Toggle to re-render at the new resolution\n                this.cacheAsBitmap = false;\n                this.cacheAsBitmap = true;\n            }\n        },\n    },\n\n    /**\n     * The number of samples to use for cacheAsBitmap. If set to `null`, the renderer's\n     * sample count is used.\n     * If `cacheAsBitmap` is set to `true`, this will re-render with the new number of samples.\n     * @member {number|null} cacheAsBitmapMultisample\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     */\n    cacheAsBitmapMultisample: {\n        get(): MSAA_QUALITY | null\n        {\n            return this._cacheAsBitmapMultisample;\n        },\n        set(multisample: MSAA_QUALITY | null): void\n        {\n            if (multisample === this._cacheAsBitmapMultisample)\n            {\n                return;\n            }\n\n            this._cacheAsBitmapMultisample = multisample;\n\n            if (this.cacheAsBitmap)\n            {\n                // Toggle to re-render with new multisample\n                this.cacheAsBitmap = false;\n                this.cacheAsBitmap = true;\n            }\n        },\n    },\n\n    /**\n     * Set this to true if you want this display object to be cached as a bitmap.\n     * This basically takes a snapshot of the display object as it is at that moment. It can\n     * provide a performance benefit for complex static displayObjects.\n     * To remove simply set this property to `false`\n     *\n     * IMPORTANT GOTCHA - Make sure that all your textures are preloaded BEFORE setting this property to true\n     * as it will take a snapshot of what is currently there. If the textures have not loaded then they will not appear.\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     */\n    cacheAsBitmap: {\n        get(): CacheData\n        {\n            return this._cacheAsBitmap;\n        },\n        set(value: CacheData): void\n        {\n            if (this._cacheAsBitmap === value)\n            {\n                return;\n            }\n\n            this._cacheAsBitmap = value;\n\n            let data: CacheData;\n\n            if (value)\n            {\n                if (!this._cacheData)\n                {\n                    this._cacheData = new CacheData();\n                }\n\n                data = this._cacheData;\n\n                data.originalRender = this.render;\n                data.originalRenderCanvas = this.renderCanvas;\n\n                data.originalUpdateTransform = this.updateTransform;\n                data.originalCalculateBounds = this.calculateBounds;\n                data.originalGetLocalBounds = this.getLocalBounds;\n\n                data.originalDestroy = this.destroy;\n\n                data.originalContainsPoint = this.containsPoint;\n\n                data.originalMask = this._mask;\n                data.originalFilterArea = this.filterArea;\n\n                this.render = this._renderCached;\n                this.renderCanvas = this._renderCachedCanvas;\n\n                this.destroy = this._cacheAsBitmapDestroy;\n            }\n            else\n            {\n                data = this._cacheData;\n\n                if (data.sprite)\n                {\n                    this._destroyCachedDisplayObject();\n                }\n\n                this.render = data.originalRender;\n                this.renderCanvas = data.originalRenderCanvas;\n                this.calculateBounds = data.originalCalculateBounds;\n                this.getLocalBounds = data.originalGetLocalBounds;\n\n                this.destroy = data.originalDestroy;\n\n                this.updateTransform = data.originalUpdateTransform;\n                this.containsPoint = data.originalContainsPoint;\n\n                this._mask = data.originalMask;\n                this.filterArea = data.originalFilterArea;\n            }\n        },\n    },\n});\n\n/**\n * Renders a cached version of the sprite with WebGL\n * @private\n * @method _renderCached\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Renderer} renderer - the WebGL renderer\n */\nDisplayObject.prototype._renderCached = function _renderCached(renderer: Renderer): void\n{\n    if (!this.visible || this.worldAlpha <= 0 || !this.renderable)\n    {\n        return;\n    }\n\n    this._initCachedDisplayObject(renderer);\n\n    this._cacheData.sprite.transform._worldID = this.transform._worldID;\n    this._cacheData.sprite.worldAlpha = this.worldAlpha;\n    (this._cacheData.sprite as any)._render(renderer);\n};\n\n/**\n * Prepares the WebGL renderer to cache the sprite\n * @private\n * @method _initCachedDisplayObject\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.Renderer} renderer - the WebGL renderer\n */\nDisplayObject.prototype._initCachedDisplayObject = function _initCachedDisplayObject(renderer: Renderer): void\n{\n    if (this._cacheData?.sprite)\n    {\n        return;\n    }\n\n    // make sure alpha is set to 1 otherwise it will get rendered as invisible!\n    const cacheAlpha = this.alpha;\n\n    this.alpha = 1;\n\n    // first we flush anything left in the renderer (otherwise it would get rendered to the cached texture)\n    renderer.batch.flush();\n    // this.filters= [];\n\n    // next we find the dimensions of the untransformed object\n    // this function also calls updatetransform on all its children as part of the measuring.\n    // This means we don't need to update the transform again in this function\n    // TODO pass an object to clone too? saves having to create a new one each time!\n    const bounds = (this as Container).getLocalBounds(new Rectangle(), true);\n\n    // add some padding!\n    if (this.filters?.length)\n    {\n        const padding = this.filters[0].padding;\n\n        bounds.pad(padding);\n    }\n\n    const resolution = this.cacheAsBitmapResolution || renderer.resolution;\n\n    bounds.ceil(resolution);\n    bounds.width = Math.max(bounds.width, 1 / resolution);\n    bounds.height = Math.max(bounds.height, 1 / resolution);\n\n    // for now we cache the current renderTarget that the WebGL renderer is currently using.\n    // this could be more elegant..\n    const cachedRenderTexture = renderer.renderTexture.current;\n    const cachedSourceFrame = renderer.renderTexture.sourceFrame.clone();\n    const cachedDestinationFrame = renderer.renderTexture.destinationFrame.clone();\n    const cachedProjectionTransform = renderer.projection.transform;\n\n    // We also store the filter stack - I will definitely look to change how this works a little later down the line.\n    // const stack = renderer.filterManager.filterStack;\n\n    // this renderTexture will be used to store the cached DisplayObject\n    const renderTexture = RenderTexture.create({\n        width: bounds.width,\n        height: bounds.height,\n        resolution,\n        multisample: this.cacheAsBitmapMultisample ?? renderer.multisample,\n    });\n\n    const textureCacheId = `cacheAsBitmap_${utils.uid()}`;\n\n    this._cacheData.textureCacheId = textureCacheId;\n\n    BaseTexture.addToCache(renderTexture.baseTexture, textureCacheId);\n    Texture.addToCache(renderTexture, textureCacheId);\n\n    // need to set //\n    const m = this.transform.localTransform.copyTo(_tempMatrix).invert().translate(-bounds.x, -bounds.y);\n\n    // set all properties to there original so we can render to a texture\n    this.render = this._cacheData.originalRender;\n\n    renderer.render(this, { renderTexture, clear: true, transform: m, skipUpdateTransform: false });\n    renderer.framebuffer.blit();\n\n    // now restore the state be setting the new properties\n    renderer.projection.transform = cachedProjectionTransform;\n    renderer.renderTexture.bind(cachedRenderTexture, cachedSourceFrame, cachedDestinationFrame);\n\n    // renderer.filterManager.filterStack = stack;\n\n    this.render = this._renderCached;\n    // the rest is the same as for Canvas\n    this.updateTransform = this.displayObjectUpdateTransform;\n    this.calculateBounds = this._calculateCachedBounds;\n    this.getLocalBounds = this._getCachedLocalBounds;\n\n    this._mask = null;\n    this.filterArea = null;\n    this.alpha = cacheAlpha;\n\n    // create our cached sprite\n    const cachedSprite = new Sprite(renderTexture);\n\n    cachedSprite.transform.worldTransform = this.transform.worldTransform;\n    cachedSprite.anchor.x = -(bounds.x / bounds.width);\n    cachedSprite.anchor.y = -(bounds.y / bounds.height);\n    cachedSprite.alpha = cacheAlpha;\n    cachedSprite._bounds = this._bounds;\n\n    this._cacheData.sprite = cachedSprite;\n\n    this.transform._parentID = -1;\n    // restore the transform of the cached sprite to avoid the nasty flicker..\n    if (!this.parent)\n    {\n        this.enableTempParent();\n        this.updateTransform();\n        this.disableTempParent(null);\n    }\n    else\n    {\n        this.updateTransform();\n    }\n\n    // map the hit test..\n    (this as Sprite).containsPoint = cachedSprite.containsPoint.bind(cachedSprite);\n};\n\n/**\n * Renders a cached version of the sprite with canvas\n * @private\n * @method _renderCachedCanvas\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.CanvasRenderer} renderer - The canvas renderer\n */\nDisplayObject.prototype._renderCachedCanvas = function _renderCachedCanvas(renderer: IRenderer): void\n{\n    if (!this.visible || this.worldAlpha <= 0 || !this.renderable)\n    {\n        return;\n    }\n\n    this._initCachedDisplayObjectCanvas(renderer);\n\n    this._cacheData.sprite.worldAlpha = this.worldAlpha;\n    (this._cacheData.sprite as any)._renderCanvas(renderer);\n};\n\n// TODO this can be the same as the WebGL version.. will need to do a little tweaking first though..\n/**\n * Prepares the Canvas renderer to cache the sprite\n * @private\n * @method _initCachedDisplayObjectCanvas\n * @memberof PIXI.DisplayObject#\n * @param {PIXI.CanvasRenderer} renderer - The canvas renderer\n */\nDisplayObject.prototype._initCachedDisplayObjectCanvas = function _initCachedDisplayObjectCanvas(\n    renderer: CanvasRenderer\n): void\n{\n    if (this._cacheData?.sprite)\n    {\n        return;\n    }\n\n    // get bounds actually transforms the object for us already!\n    const bounds = (this as Container).getLocalBounds(new Rectangle(), true);\n\n    const cacheAlpha = this.alpha;\n\n    this.alpha = 1;\n\n    const cachedRenderTarget = renderer.canvasContext.activeContext;\n    const cachedProjectionTransform = (renderer as any)._projTransform;\n\n    const resolution = this.cacheAsBitmapResolution || renderer.resolution;\n\n    bounds.ceil(resolution);\n    bounds.width = Math.max(bounds.width, 1 / resolution);\n    bounds.height = Math.max(bounds.height, 1 / resolution);\n\n    const renderTexture = RenderTexture.create({\n        width: bounds.width,\n        height: bounds.height,\n        resolution\n    });\n\n    const textureCacheId = `cacheAsBitmap_${utils.uid()}`;\n\n    this._cacheData.textureCacheId = textureCacheId;\n\n    BaseTexture.addToCache(renderTexture.baseTexture, textureCacheId);\n    Texture.addToCache(renderTexture, textureCacheId);\n\n    // need to set //\n    const m = _tempMatrix;\n\n    this.transform.localTransform.copyTo(m);\n    m.invert();\n\n    m.tx -= bounds.x;\n    m.ty -= bounds.y;\n\n    // m.append(this.transform.worldTransform.)\n    // set all properties to there original so we can render to a texture\n    this.renderCanvas = this._cacheData.originalRenderCanvas;\n\n    renderer.render(this, { renderTexture, clear: true, transform: m, skipUpdateTransform: false });\n    // now restore the state be setting the new properties\n    renderer.canvasContext.activeContext = cachedRenderTarget;\n    (renderer as any)._projTransform = cachedProjectionTransform;\n\n    this.renderCanvas = this._renderCachedCanvas;\n    // the rest is the same as for WebGL\n    this.updateTransform = this.displayObjectUpdateTransform;\n    this.calculateBounds = this._calculateCachedBounds;\n    this.getLocalBounds = this._getCachedLocalBounds;\n\n    this._mask = null;\n    this.filterArea = null;\n    this.alpha = cacheAlpha;\n\n    // create our cached sprite\n    const cachedSprite = new Sprite(renderTexture);\n\n    cachedSprite.transform.worldTransform = this.transform.worldTransform;\n    cachedSprite.anchor.x = -(bounds.x / bounds.width);\n    cachedSprite.anchor.y = -(bounds.y / bounds.height);\n    cachedSprite.alpha = cacheAlpha;\n    cachedSprite._bounds = this._bounds;\n\n    this._cacheData.sprite = cachedSprite;\n\n    this.transform._parentID = -1;\n    // restore the transform of the cached sprite to avoid the nasty flicker..\n    if (!this.parent)\n    {\n        this.parent = (renderer as any)._tempDisplayObjectParent;\n        this.updateTransform();\n        this.parent = null;\n    }\n    else\n    {\n        this.updateTransform();\n    }\n\n    // map the hit test..\n    (this as Sprite).containsPoint = cachedSprite.containsPoint.bind(cachedSprite);\n};\n\n/**\n * Calculates the bounds of the cached sprite\n * @private\n * @method\n */\nDisplayObject.prototype._calculateCachedBounds = function _calculateCachedBounds(): void\n{\n    this._bounds.clear();\n    this._cacheData.sprite.transform._worldID = this.transform._worldID;\n    (this._cacheData.sprite as any)._calculateBounds();\n    this._bounds.updateID = (this as any)._boundsID;\n};\n\n/**\n * Gets the bounds of the cached sprite.\n * @private\n * @method\n * @returns {Rectangle} The local bounds.\n */\nDisplayObject.prototype._getCachedLocalBounds = function _getCachedLocalBounds(): Rectangle\n{\n    return this._cacheData.sprite.getLocalBounds(null);\n};\n\n/**\n * Destroys the cached sprite.\n * @private\n * @method\n */\nDisplayObject.prototype._destroyCachedDisplayObject = function _destroyCachedDisplayObject(): void\n{\n    this._cacheData.sprite._texture.destroy(true);\n    this._cacheData.sprite = null;\n\n    BaseTexture.removeFromCache(this._cacheData.textureCacheId);\n    Texture.removeFromCache(this._cacheData.textureCacheId);\n\n    this._cacheData.textureCacheId = null;\n};\n\n/**\n * Destroys the cached object.\n * @private\n * @method\n * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n *  have been set to that value.\n *  Used when destroying containers, see the Container.destroy method.\n */\nDisplayObject.prototype._cacheAsBitmapDestroy = function _cacheAsBitmapDestroy(options?: IDestroyOptions | boolean): void\n{\n    this.cacheAsBitmap = false;\n    this.destroy(options);\n};\n"], "names": ["Matrix", "DisplayObject", "Rectangle", "RenderTexture", "utils", "BaseTexture", "Texture", "Sprite"], "mappings": ";;AAwBA,MAAM,cAAc,IAAIA,KAAAA;AAExBC,QAAAA,cAAc,UAAU,iBAAiB;AACzCA,QAAAA,cAAc,UAAU,aAAa;AACrCA,QAAAA,cAAc,UAAU,2BAA2B;AACnDA,QAAAA,cAAc,UAAU,4BAA4B;AAU7C,MAAM,UACb;AAAA,EAaI,cACA;AACI,SAAK,iBAAiB,MAEtB,KAAK,iBAAiB,MACtB,KAAK,uBAAuB,MAC5B,KAAK,0BAA0B,MAC/B,KAAK,yBAAyB,MAE9B,KAAK,0BAA0B,MAC/B,KAAK,kBAAkB,MACvB,KAAK,eAAe,MACpB,KAAK,qBAAqB,MAC1B,KAAK,wBAAwB,MAC7B,KAAK,SAAS;AAAA,EAClB;AACJ;AAEA,OAAO,iBAAiBA,sBAAc,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU7C,yBAAyB;AAAA,IACrB,MACA;AACI,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,YACJ;AACQ,qBAAe,KAAK,6BAKxB,KAAK,2BAA2B,YAE5B,KAAK,kBAGL,KAAK,gBAAgB,IACrB,KAAK,gBAAgB;AAAA,IAE7B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B;AAAA,IACtB,MACA;AACI,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,aACJ;AACQ,sBAAgB,KAAK,8BAKzB,KAAK,4BAA4B,aAE7B,KAAK,kBAGL,KAAK,gBAAgB,IACrB,KAAK,gBAAgB;AAAA,IAE7B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,eAAe;AAAA,IACX,MACA;AACI,aAAO,KAAK;AAAA,IAChB;AAAA,IACA,IAAI,OACJ;AACI,UAAI,KAAK,mBAAmB;AAExB;AAGJ,WAAK,iBAAiB;AAElB,UAAA;AAEA,eAEK,KAAK,eAEN,KAAK,aAAa,IAAI,UAG1B,IAAA,OAAO,KAAK,YAEZ,KAAK,iBAAiB,KAAK,QAC3B,KAAK,uBAAuB,KAAK,cAEjC,KAAK,0BAA0B,KAAK,iBACpC,KAAK,0BAA0B,KAAK,iBACpC,KAAK,yBAAyB,KAAK,gBAEnC,KAAK,kBAAkB,KAAK,SAE5B,KAAK,wBAAwB,KAAK,eAElC,KAAK,eAAe,KAAK,OACzB,KAAK,qBAAqB,KAAK,YAE/B,KAAK,SAAS,KAAK,eACnB,KAAK,eAAe,KAAK,qBAEzB,KAAK,UAAU,KAAK,0BAIpB,OAAO,KAAK,YAER,KAAK,UAEL,KAAK,4BAAA,GAGT,KAAK,SAAS,KAAK,gBACnB,KAAK,eAAe,KAAK,sBACzB,KAAK,kBAAkB,KAAK,yBAC5B,KAAK,iBAAiB,KAAK,wBAE3B,KAAK,UAAU,KAAK,iBAEpB,KAAK,kBAAkB,KAAK,yBAC5B,KAAK,gBAAgB,KAAK,uBAE1B,KAAK,QAAQ,KAAK,cAClB,KAAK,aAAa,KAAK;AAAA,IAE/B;AAAA,EACJ;AACJ,CAAC;AASDA,QAAAA,cAAc,UAAU,gBAAgB,SAAuB,UAC/D;AACQ,GAAC,KAAK,WAAW,KAAK,cAAc,KAAK,CAAC,KAAK,eAKnD,KAAK,yBAAyB,QAAQ,GAEtC,KAAK,WAAW,OAAO,UAAU,WAAW,KAAK,UAAU,UAC3D,KAAK,WAAW,OAAO,aAAa,KAAK,YACxC,KAAK,WAAW,OAAe,QAAQ,QAAQ;AACpD;AASAA,QAAAA,cAAc,UAAU,2BAA2B,SAAkC,UACrF;AACI,MAAI,KAAK,YAAY;AAEjB;AAIJ,QAAM,aAAa,KAAK;AAExB,OAAK,QAAQ,GAGb,SAAS,MAAM,MAAM;AAOrB,QAAM,SAAU,KAAmB,eAAe,IAAIC,KAAAA,aAAa,EAAI;AAGnE,MAAA,KAAK,SAAS,QAClB;AACI,UAAM,UAAU,KAAK,QAAQ,CAAC,EAAE;AAEhC,WAAO,IAAI,OAAO;AAAA,EACtB;AAEM,QAAA,aAAa,KAAK,2BAA2B,SAAS;AAE5D,SAAO,KAAK,UAAU,GACtB,OAAO,QAAQ,KAAK,IAAI,OAAO,OAAO,IAAI,UAAU,GACpD,OAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,IAAI,UAAU;AAIhD,QAAA,sBAAsB,SAAS,cAAc,SAC7C,oBAAoB,SAAS,cAAc,YAAY,MAAA,GACvD,yBAAyB,SAAS,cAAc,iBAAiB,MACjE,GAAA,4BAA4B,SAAS,WAAW,WAMhD,gBAAgBC,KAAA,cAAc,OAAO;AAAA,IACvC,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,IACf;AAAA,IACA,aAAa,KAAK,4BAA4B,SAAS;AAAA,EAAA,CAC1D,GAEK,iBAAiB,iBAAiBC,KAAAA,MAAM,IAAK,CAAA;AAEnD,OAAK,WAAW,iBAAiB,gBAEjCC,KAAAA,YAAY,WAAW,cAAc,aAAa,cAAc,GAChEC,KAAA,QAAQ,WAAW,eAAe,cAAc;AAGhD,QAAM,IAAI,KAAK,UAAU,eAAe,OAAO,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC;AAG9F,OAAA,SAAS,KAAK,WAAW,gBAE9B,SAAS,OAAO,MAAM,EAAE,eAAe,OAAO,IAAM,WAAW,GAAG,qBAAqB,GAAO,CAAA,GAC9F,SAAS,YAAY,KAAK,GAG1B,SAAS,WAAW,YAAY,2BAChC,SAAS,cAAc,KAAK,qBAAqB,mBAAmB,sBAAsB,GAI1F,KAAK,SAAS,KAAK,eAEnB,KAAK,kBAAkB,KAAK,8BAC5B,KAAK,kBAAkB,KAAK,wBAC5B,KAAK,iBAAiB,KAAK,uBAE3B,KAAK,QAAQ,MACb,KAAK,aAAa,MAClB,KAAK,QAAQ;AAGP,QAAA,eAAe,IAAIC,cAAO,aAAa;AAEhC,eAAA,UAAU,iBAAiB,KAAK,UAAU,gBACvD,aAAa,OAAO,IAAI,EAAE,OAAO,IAAI,OAAO,QAC5C,aAAa,OAAO,IAAI,EAAE,OAAO,IAAI,OAAO,SAC5C,aAAa,QAAQ,YACrB,aAAa,UAAU,KAAK,SAE5B,KAAK,WAAW,SAAS,cAEzB,KAAK,UAAU,YAAY,IAEtB,KAAK,SAQN,KAAK,gBAAgB,KANrB,KAAK,oBACL,KAAK,gBAAA,GACL,KAAK,kBAAkB,IAAI,IAQ9B,KAAgB,gBAAgB,aAAa,cAAc,KAAK,YAAY;AACjF;AASAN,QAAAA,cAAc,UAAU,sBAAsB,SAA6B,UAC3E;AACQ,GAAC,KAAK,WAAW,KAAK,cAAc,KAAK,CAAC,KAAK,eAKnD,KAAK,+BAA+B,QAAQ,GAE5C,KAAK,WAAW,OAAO,aAAa,KAAK,YACxC,KAAK,WAAW,OAAe,cAAc,QAAQ;AAC1D;AAUAA,QAAAA,cAAc,UAAU,iCAAiC,SACrD,UAEJ;AACI,MAAI,KAAK,YAAY;AAEjB;AAIE,QAAA,SAAU,KAAmB,eAAe,IAAIC,kBAAa,EAAI,GAEjE,aAAa,KAAK;AAExB,OAAK,QAAQ;AAEP,QAAA,qBAAqB,SAAS,cAAc,eAC5C,4BAA6B,SAAiB,gBAE9C,aAAa,KAAK,2BAA2B,SAAS;AAE5D,SAAO,KAAK,UAAU,GACtB,OAAO,QAAQ,KAAK,IAAI,OAAO,OAAO,IAAI,UAAU,GACpD,OAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,IAAI,UAAU;AAEhD,QAAA,gBAAgBC,mBAAc,OAAO;AAAA,IACvC,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,IACf;AAAA,EAAA,CACH,GAEK,iBAAiB,iBAAiBC,KAAAA,MAAM,IAAK,CAAA;AAEnD,OAAK,WAAW,iBAAiB,gBAEjCC,KAAAA,YAAY,WAAW,cAAc,aAAa,cAAc,GAChEC,KAAA,QAAQ,WAAW,eAAe,cAAc;AAGhD,QAAM,IAAI;AAEV,OAAK,UAAU,eAAe,OAAO,CAAC,GACtC,EAAE,OAAO,GAET,EAAE,MAAM,OAAO,GACf,EAAE,MAAM,OAAO,GAIf,KAAK,eAAe,KAAK,WAAW,sBAEpC,SAAS,OAAO,MAAM,EAAE,eAAe,OAAO,IAAM,WAAW,GAAG,qBAAqB,GAAA,CAAO,GAE9F,SAAS,cAAc,gBAAgB,oBACtC,SAAiB,iBAAiB,2BAEnC,KAAK,eAAe,KAAK,qBAEzB,KAAK,kBAAkB,KAAK,8BAC5B,KAAK,kBAAkB,KAAK,wBAC5B,KAAK,iBAAiB,KAAK,uBAE3B,KAAK,QAAQ,MACb,KAAK,aAAa,MAClB,KAAK,QAAQ;AAGP,QAAA,eAAe,IAAIC,cAAO,aAAa;AAEhC,eAAA,UAAU,iBAAiB,KAAK,UAAU,gBACvD,aAAa,OAAO,IAAI,EAAE,OAAO,IAAI,OAAO,QAC5C,aAAa,OAAO,IAAI,EAAE,OAAO,IAAI,OAAO,SAC5C,aAAa,QAAQ,YACrB,aAAa,UAAU,KAAK,SAE5B,KAAK,WAAW,SAAS,cAEzB,KAAK,UAAU,YAAY,IAEtB,KAAK,SAQN,KAAK,gBAAgB,KANrB,KAAK,SAAU,SAAiB,0BAChC,KAAK,gBAAgB,GACrB,KAAK,SAAS,OAQjB,KAAgB,gBAAgB,aAAa,cAAc,KAAK,YAAY;AACjF;AAOAN,QAAAA,cAAc,UAAU,yBAAyB,WACjD;AACI,OAAK,QAAQ,MAAM,GACnB,KAAK,WAAW,OAAO,UAAU,WAAW,KAAK,UAAU,UAC1D,KAAK,WAAW,OAAe,iBAAA,GAChC,KAAK,QAAQ,WAAY,KAAa;AAC1C;AAQAA,QAAAA,cAAc,UAAU,wBAAwB,WAChD;AACI,SAAO,KAAK,WAAW,OAAO,eAAe,IAAI;AACrD;AAOAA,QAAAA,cAAc,UAAU,8BAA8B,WACtD;AACS,OAAA,WAAW,OAAO,SAAS,QAAQ,EAAI,GAC5C,KAAK,WAAW,SAAS,MAEzBI,KAAAA,YAAY,gBAAgB,KAAK,WAAW,cAAc,GAC1DC,KAAA,QAAQ,gBAAgB,KAAK,WAAW,cAAc,GAEtD,KAAK,WAAW,iBAAiB;AACrC;AAUAL,QAAAA,cAAc,UAAU,wBAAwB,SAA+B,SAC/E;AACI,OAAK,gBAAgB,IACrB,KAAK,QAAQ,OAAO;AACxB;;"}