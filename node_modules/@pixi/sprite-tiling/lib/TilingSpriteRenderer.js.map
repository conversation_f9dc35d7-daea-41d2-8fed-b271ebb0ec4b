{"version": 3, "file": "TilingSpriteRenderer.js", "sources": ["../src/TilingSpriteRenderer.ts"], "sourcesContent": ["import {\n    Color,\n    extensions,\n    ExtensionType,\n    Matrix,\n    ObjectRenderer,\n    QuadUv,\n    Shader,\n    State,\n    utils,\n    WRAP_MODES\n} from '@pixi/core';\nimport gl2FragmentSrc from './sprite-tiling.frag';\nimport gl2VertexSrc from './sprite-tiling.vert';\nimport gl1FragmentSrc from './sprite-tiling-fallback.frag';\nimport gl1VertexSrc from './sprite-tiling-fallback.vert';\nimport fragmentSimpleSrc from './sprite-tiling-simple.frag';\n\nimport type { ExtensionMetadata, Renderer } from '@pixi/core';\nimport type { TilingSprite } from './TilingSprite';\n\nconst tempMat = new Matrix();\n\n/**\n * WebGL renderer plugin for tiling sprites\n * @class\n * @memberof PIXI\n * @extends PIXI.ObjectRenderer\n */\nexport class TilingSpriteRenderer extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'tilingSprite',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    public shader: Shader;\n    public simpleShader: Shader;\n    public quad: QuadUv;\n    public readonly state: State;\n\n    /**\n     * constructor for renderer\n     * @param {PIXI.Renderer} renderer - The renderer this tiling awesomeness works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // WebGL version is not available during initialization!\n        renderer.runners.contextChange.add(this);\n\n        this.quad = new QuadUv();\n\n        /**\n         * The WebGL state in which this renderer will work.\n         * @member {PIXI.State}\n         * @readonly\n         */\n        this.state = State.for2d();\n    }\n\n    /** Creates shaders when context is initialized. */\n    contextChange(): void\n    {\n        const renderer = this.renderer;\n        const uniforms = { globals: renderer.globalUniforms };\n\n        this.simpleShader = Shader.from(gl1VertexSrc, fragmentSimpleSrc, uniforms);\n        this.shader = renderer.context.webGLVersion > 1\n            ? Shader.from(gl2VertexSrc, gl2FragmentSrc, uniforms)\n            : Shader.from(gl1VertexSrc, gl1FragmentSrc, uniforms);\n    }\n\n    /**\n     * @param {PIXI.TilingSprite} ts - tilingSprite to be rendered\n     */\n    public render(ts: TilingSprite): void\n    {\n        const renderer = this.renderer;\n        const quad = this.quad;\n\n        let vertices = quad.vertices;\n\n        vertices[0] = vertices[6] = (ts._width) * -ts.anchor.x;\n        vertices[1] = vertices[3] = ts._height * -ts.anchor.y;\n\n        vertices[2] = vertices[4] = (ts._width) * (1.0 - ts.anchor.x);\n        vertices[5] = vertices[7] = ts._height * (1.0 - ts.anchor.y);\n\n        const anchorX = ts.uvRespectAnchor ? ts.anchor.x : 0;\n        const anchorY = ts.uvRespectAnchor ? ts.anchor.y : 0;\n\n        vertices = quad.uvs;\n\n        vertices[0] = vertices[6] = -anchorX;\n        vertices[1] = vertices[3] = -anchorY;\n\n        vertices[2] = vertices[4] = 1.0 - anchorX;\n        vertices[5] = vertices[7] = 1.0 - anchorY;\n\n        quad.invalidate();\n\n        const tex = ts._texture;\n        const baseTex = tex.baseTexture;\n        const premultiplied = baseTex.alphaMode > 0;\n        const lt = ts.tileTransform.localTransform;\n        const uv = ts.uvMatrix;\n        let isSimple = baseTex.isPowerOfTwo\n            && tex.frame.width === baseTex.width && tex.frame.height === baseTex.height;\n\n        // auto, force repeat wrapMode for big tiling textures\n        if (isSimple)\n        {\n            if (!baseTex._glTextures[renderer.CONTEXT_UID])\n            {\n                if (baseTex.wrapMode === WRAP_MODES.CLAMP)\n                {\n                    baseTex.wrapMode = WRAP_MODES.REPEAT;\n                }\n            }\n            else\n            {\n                isSimple = baseTex.wrapMode !== WRAP_MODES.CLAMP;\n            }\n        }\n\n        const shader = isSimple ? this.simpleShader : this.shader;\n\n        const w = tex.width;\n        const h = tex.height;\n        const W = ts._width;\n        const H = ts._height;\n\n        tempMat.set(lt.a * w / W,\n            lt.b * w / H,\n            lt.c * h / W,\n            lt.d * h / H,\n            lt.tx / W,\n            lt.ty / H);\n\n        // that part is the same as above:\n        // tempMat.identity();\n        // tempMat.scale(tex.width, tex.height);\n        // tempMat.prepend(lt);\n        // tempMat.scale(1.0 / ts._width, 1.0 / ts._height);\n\n        tempMat.invert();\n        if (isSimple)\n        {\n            tempMat.prepend(uv.mapCoord);\n        }\n        else\n        {\n            shader.uniforms.uMapCoord = uv.mapCoord.toArray(true);\n            shader.uniforms.uClampFrame = uv.uClampFrame;\n            shader.uniforms.uClampOffset = uv.uClampOffset;\n        }\n\n        shader.uniforms.uTransform = tempMat.toArray(true);\n        shader.uniforms.uColor = Color.shared\n            .setValue(ts.tint)\n            .premultiply(ts.worldAlpha, premultiplied)\n            .toArray(shader.uniforms.uColor);\n\n        shader.uniforms.translationMatrix = ts.transform.worldTransform.toArray(true);\n        shader.uniforms.uSampler = tex;\n\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(quad);\n\n        this.state.blendMode = utils.correctBlendMode(ts.blendMode, premultiplied);\n        renderer.state.set(this.state);\n        renderer.geometry.draw(this.renderer.gl.TRIANGLES, 6, 0);\n    }\n}\n\nextensions.add(TilingSpriteRenderer);\n"], "names": ["Matrix", "O<PERSON><PERSON><PERSON><PERSON>", "QuadUv", "State", "Shader", "gl1VertexSrc", "fragmentSimpleSrc", "gl2VertexSrc", "gl2FragmentSrc", "gl1FragmentSrc", "WRAP_MODES", "Color", "utils", "ExtensionType", "extensions"], "mappings": ";;AAqBA,MAAM,UAAU,IAAIA,KAAAA;AAQb,MAAM,6BAA6BC,KAAAA,eAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBI,YAAY,UACZ;AACI,UAAM,QAAQ,GAGd,SAAS,QAAQ,cAAc,IAAI,IAAI,GAEvC,KAAK,OAAO,IAAIC,KAAAA,OAAO,GAOvB,KAAK,QAAQC,WAAM,MAAM;AAAA,EAC7B;AAAA;AAAA,EAGA,gBACA;AACI,UAAM,WAAW,KAAK,UAChB,WAAW,EAAE,SAAS,SAAS;AAEhC,SAAA,eAAeC,YAAO,KAAKC,qBAAAA,SAAcC,mBAAAA,SAAmB,QAAQ,GACzE,KAAK,SAAS,SAAS,QAAQ,eAAe,IACxCF,YAAO,KAAKG,aAAA,SAAcC,eAAgB,SAAA,QAAQ,IAClDJ,KAAO,OAAA,KAAKC,qBAAAA,SAAcI,uBAAA,SAAgB,QAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKO,OAAO,IACd;AACI,UAAM,WAAW,KAAK,UAChB,OAAO,KAAK;AAElB,QAAI,WAAW,KAAK;AAEX,aAAA,CAAC,IAAI,SAAS,CAAC,IAAK,GAAG,SAAU,CAAC,GAAG,OAAO,GACrD,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,OAAO,GAEpD,SAAS,CAAC,IAAI,SAAS,CAAC,IAAK,GAAG,UAAW,IAAM,GAAG,OAAO,IAC3D,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,WAAW,IAAM,GAAG,OAAO;AAE1D,UAAM,UAAU,GAAG,kBAAkB,GAAG,OAAO,IAAI,GAC7C,UAAU,GAAG,kBAAkB,GAAG,OAAO,IAAI;AAEnD,eAAW,KAAK,KAEhB,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,SAC7B,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,SAE7B,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,IAAM,SAClC,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,IAAM,SAElC,KAAK;AAEL,UAAM,MAAM,GAAG,UACT,UAAU,IAAI,aACd,gBAAgB,QAAQ,YAAY,GACpC,KAAK,GAAG,cAAc,gBACtB,KAAK,GAAG;AACV,QAAA,WAAW,QAAQ,gBAChB,IAAI,MAAM,UAAU,QAAQ,SAAS,IAAI,MAAM,WAAW,QAAQ;AAGrE,iBAEK,QAAQ,YAAY,SAAS,WAAW,IASzC,WAAW,QAAQ,aAAaC,KAAA,WAAW,QAPvC,QAAQ,aAAaA,KAAAA,WAAW,UAEhC,QAAQ,WAAWA,KAAAA,WAAW;AAS1C,UAAM,SAAS,WAAW,KAAK,eAAe,KAAK,QAE7C,IAAI,IAAI,OACR,IAAI,IAAI,QACR,IAAI,GAAG,QACP,IAAI,GAAG;AAEL,YAAA;AAAA,MAAI,GAAG,IAAI,IAAI;AAAA,MACnB,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IAAC,GAQb,QAAQ,UACJ,WAEA,QAAQ,QAAQ,GAAG,QAAQ,KAI3B,OAAO,SAAS,YAAY,GAAG,SAAS,QAAQ,EAAI,GACpD,OAAO,SAAS,cAAc,GAAG,aACjC,OAAO,SAAS,eAAe,GAAG,eAGtC,OAAO,SAAS,aAAa,QAAQ,QAAQ,EAAI,GACjD,OAAO,SAAS,SAASC,WAAM,OAC1B,SAAS,GAAG,IAAI,EAChB,YAAY,GAAG,YAAY,aAAa,EACxC,QAAQ,OAAO,SAAS,MAAM,GAEnC,OAAO,SAAS,oBAAoB,GAAG,UAAU,eAAe,QAAQ,EAAI,GAC5E,OAAO,SAAS,WAAW,KAE3B,SAAS,OAAO,KAAK,MAAM,GAC3B,SAAS,SAAS,KAAK,IAAI,GAE3B,KAAK,MAAM,YAAYC,KAAAA,MAAM,iBAAiB,GAAG,WAAW,aAAa,GACzE,SAAS,MAAM,IAAI,KAAK,KAAK,GAC7B,SAAS,SAAS,KAAK,KAAK,SAAS,GAAG,WAAW,GAAG,CAAC;AAAA,EAC3D;AACJ;AAnJa,qBAGF,YAA+B;AAAA,EAClC,MAAM;AAAA,EACN,MAAMC,KAAc,cAAA;AACxB;AA+IJC,KAAAA,WAAW,IAAI,oBAAoB;;"}