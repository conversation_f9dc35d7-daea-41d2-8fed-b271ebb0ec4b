{"version": 3, "file": "TilingSprite.js", "sources": ["../src/TilingSprite.ts"], "sourcesContent": ["import { Point, Rectangle, Texture, TextureMatrix, Transform } from '@pixi/core';\nimport { Sprite } from '@pixi/sprite';\n\nimport type { IBaseTextureOptions, IPoint, IPointData, ISize, ObservablePoint, Renderer, TextureSource } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\nconst tempPoint = new Point();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface TilingSprite extends GlobalMixins.TilingSprite {}\n\n/**\n * A tiling sprite is a fast way of rendering a tiling image.\n * @memberof PIXI\n */\nexport class TilingSprite extends Sprite\n{\n    /** Tile transform */\n    public tileTransform: Transform;\n\n    /** Matrix that is applied to UV to get the coords in Texture normalized space to coords in BaseTexture space. */\n    public uvMatrix: TextureMatrix;\n\n    /**\n     * Flags whether the tiling pattern should originate from the origin instead of the top-left corner in\n     * local space.\n     *\n     * This will make the texture coordinates assigned to each vertex dependent on the value of the anchor. Without\n     * this, the top-left corner always gets the (0, 0) texture coordinate.\n     * @default false\n     */\n    public uvRespectAnchor: boolean;\n\n    /**\n     * Note: The wrap mode of the texture is forced to REPEAT on render if the size of the texture\n     * is a power of two, the texture's wrap mode is CLAMP, and the texture hasn't been bound yet.\n     * @param texture - The texture of the tiling sprite.\n     * @param width - The width of the tiling sprite.\n     * @param height - The height of the tiling sprite.\n     */\n    constructor(texture: Texture, width = 100, height = 100)\n    {\n        super(texture);\n\n        this.tileTransform = new Transform();\n\n        // The width of the tiling sprite\n        this._width = width;\n\n        // The height of the tiling sprite\n        this._height = height;\n\n        this.uvMatrix = this.texture.uvMatrix || new TextureMatrix(texture);\n\n        /**\n         * Plugin that is responsible for rendering this element.\n         * Allows to customize the rendering process without overriding '_render' method.\n         * @default 'tilingSprite'\n         */\n        this.pluginName = 'tilingSprite';\n\n        this.uvRespectAnchor = false;\n    }\n    /**\n     * Changes frame clamping in corresponding textureTransform, shortcut\n     * Change to -0.5 to add a pixel to the edge, recommended for transparent trimmed textures in atlas\n     * @default 0.5\n     * @member {number}\n     */\n    get clampMargin(): number\n    {\n        return this.uvMatrix.clampMargin;\n    }\n\n    set clampMargin(value: number)\n    {\n        this.uvMatrix.clampMargin = value;\n        this.uvMatrix.update(true);\n    }\n\n    /** The scaling of the image that is being tiled. */\n    get tileScale(): ObservablePoint\n    {\n        return this.tileTransform.scale;\n    }\n\n    set tileScale(value: IPointData)\n    {\n        this.tileTransform.scale.copyFrom(value as IPoint);\n    }\n\n    /** The offset of the image that is being tiled. */\n    get tilePosition(): ObservablePoint\n    {\n        return this.tileTransform.position;\n    }\n\n    set tilePosition(value: ObservablePoint)\n    {\n        this.tileTransform.position.copyFrom(value as IPoint);\n    }\n\n    /**\n     * @protected\n     */\n    protected _onTextureUpdate(): void\n    {\n        if (this.uvMatrix)\n        {\n            this.uvMatrix.texture = this._texture;\n        }\n        this._cachedTint = 0xFFFFFF;\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // tweak our texture temporarily..\n        const texture = this._texture;\n\n        if (!texture || !texture.valid)\n        {\n            return;\n        }\n\n        this.tileTransform.updateLocalTransform();\n        this.uvMatrix.update();\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n        renderer.plugins[this.pluginName].render(this);\n    }\n\n    /** Updates the bounds of the tiling sprite. */\n    protected _calculateBounds(): void\n    {\n        const minX = this._width * -this._anchor._x;\n        const minY = this._height * -this._anchor._y;\n        const maxX = this._width * (1 - this._anchor._x);\n        const maxY = this._height * (1 - this._anchor._y);\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Gets the local bounds of the sprite object.\n     * @param rect - Optional output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        // we can do a fast local bounds if the sprite has no children!\n        if (this.children.length === 0)\n        {\n            this._bounds.minX = this._width * -this._anchor._x;\n            this._bounds.minY = this._height * -this._anchor._y;\n            this._bounds.maxX = this._width * (1 - this._anchor._x);\n            this._bounds.maxY = this._height * (1 - this._anchor._y);\n\n            if (!rect)\n            {\n                if (!this._localBoundsRect)\n                {\n                    this._localBoundsRect = new Rectangle();\n                }\n\n                rect = this._localBoundsRect;\n            }\n\n            return this._bounds.getRectangle(rect);\n        }\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /**\n     * Checks if a point is inside this tiling sprite.\n     * @param point - The point to check.\n     * @returns Whether or not the sprite contains the point.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const width = this._width;\n        const height = this._height;\n        const x1 = -width * this.anchor._x;\n\n        if (tempPoint.x >= x1 && tempPoint.x < x1 + width)\n        {\n            const y1 = -height * this.anchor._y;\n\n            if (tempPoint.y >= y1 && tempPoint.y < y1 + height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Destroys this sprite and optionally its texture and children\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.tileTransform = null;\n        this.uvMatrix = null;\n    }\n\n    /**\n     * Helper function that creates a new tiling sprite based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @static\n     * @param {string|PIXI.Texture|HTMLCanvasElement|HTMLVideoElement} source - Source to create texture from\n     * @param {object} options - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @param {number} options.width - required width of the tiling sprite\n     * @param {number} options.height - required height of the tiling sprite\n     * @returns {PIXI.TilingSprite} The newly created texture\n     */\n    static from(source: TextureSource | Texture, options: ISize & IBaseTextureOptions): TilingSprite\n    {\n        const texture = (source instanceof Texture)\n            ? source\n            : Texture.from(source, options);\n\n        return new TilingSprite(\n            texture,\n            options.width,\n            options.height\n        );\n    }\n\n    /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n    }\n\n    /** The height of the TilingSprite, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n    }\n}\n"], "names": ["Point", "Sprite", "Transform", "TextureMatrix", "Rectangle", "Texture"], "mappings": ";;AAMA,MAAM,YAAY,IAAIA,KAAAA;AASf,MAAM,qBAAqBC,OAAAA,OAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBI,YAAY,SAAkB,QAAQ,KAAK,SAAS,KACpD;AACI,UAAM,OAAO,GAER,KAAA,gBAAgB,IAAIC,KAAU,UAAA,GAGnC,KAAK,SAAS,OAGd,KAAK,UAAU,QAEf,KAAK,WAAW,KAAK,QAAQ,YAAY,IAAIC,KAAc,cAAA,OAAO,GAOlE,KAAK,aAAa,gBAElB,KAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,cACJ;AACI,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EAEA,IAAI,YAAY,OAChB;AACI,SAAK,SAAS,cAAc,OAC5B,KAAK,SAAS,OAAO,EAAI;AAAA,EAC7B;AAAA;AAAA,EAGA,IAAI,YACJ;AACI,WAAO,KAAK,cAAc;AAAA,EAC9B;AAAA,EAEA,IAAI,UAAU,OACd;AACS,SAAA,cAAc,MAAM,SAAS,KAAe;AAAA,EACrD;AAAA;AAAA,EAGA,IAAI,eACJ;AACI,WAAO,KAAK,cAAc;AAAA,EAC9B;AAAA,EAEA,IAAI,aAAa,OACjB;AACS,SAAA,cAAc,SAAS,SAAS,KAAe;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAKU,mBACV;AACQ,SAAK,aAEL,KAAK,SAAS,UAAU,KAAK,WAEjC,KAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,QAAQ,UAClB;AAEI,UAAM,UAAU,KAAK;AAEjB,KAAC,WAAW,CAAC,QAAQ,UAKzB,KAAK,cAAc,qBACnB,GAAA,KAAK,SAAS,OAEd,GAAA,SAAS,MAAM,kBAAkB,SAAS,QAAQ,KAAK,UAAU,CAAC,GAClE,SAAS,QAAQ,KAAK,UAAU,EAAE,OAAO,IAAI;AAAA,EACjD;AAAA;AAAA,EAGU,mBACV;AACU,UAAA,OAAO,KAAK,SAAS,CAAC,KAAK,QAAQ,IACnC,OAAO,KAAK,UAAU,CAAC,KAAK,QAAQ,IACpC,OAAO,KAAK,UAAU,IAAI,KAAK,QAAQ,KACvC,OAAO,KAAK,WAAW,IAAI,KAAK,QAAQ;AAE9C,SAAK,QAAQ,SAAS,KAAK,WAAW,MAAM,MAAM,MAAM,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,MACtB;AAEI,WAAI,KAAK,SAAS,WAAW,KAEzB,KAAK,QAAQ,OAAO,KAAK,SAAS,CAAC,KAAK,QAAQ,IAChD,KAAK,QAAQ,OAAO,KAAK,UAAU,CAAC,KAAK,QAAQ,IACjD,KAAK,QAAQ,OAAO,KAAK,UAAU,IAAI,KAAK,QAAQ,KACpD,KAAK,QAAQ,OAAO,KAAK,WAAW,IAAI,KAAK,QAAQ,KAEhD,SAEI,KAAK,qBAEN,KAAK,mBAAmB,IAAIC,KAAU,UAAA,IAG1C,OAAO,KAAK,mBAGT,KAAK,QAAQ,aAAa,IAAI,KAGlC,MAAM,eAAe,KAAK,MAAM,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OACrB;AACS,SAAA,eAAe,aAAa,OAAO,SAAS;AAE3C,UAAA,QAAQ,KAAK,QACb,SAAS,KAAK,SACd,KAAK,CAAC,QAAQ,KAAK,OAAO;AAEhC,QAAI,UAAU,KAAK,MAAM,UAAU,IAAI,KAAK,OAC5C;AACI,YAAM,KAAK,CAAC,SAAS,KAAK,OAAO;AAEjC,UAAI,UAAU,KAAK,MAAM,UAAU,IAAI,KAAK;AAEjC,eAAA;AAAA,IAEf;AAEO,WAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,QAAQ,SACf;AACI,UAAM,QAAQ,OAAO,GAErB,KAAK,gBAAgB,MACrB,KAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,KAAK,QAAiC,SAC7C;AACI,UAAM,UAAW,kBAAkBC,eAC7B,SACAA,KAAAA,QAAQ,KAAK,QAAQ,OAAO;AAElC,WAAO,IAAI;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IAAA;AAAA,EAEhB;AAAA;AAAA,EAGA,IAAI,QACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,MAAM,OACV;AACI,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,SACJ;AACI,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,OAAO,OACX;AACI,SAAK,UAAU;AAAA,EACnB;AACJ;;"}