// Generate realistic dummy network data for testing

export function generateNetworkData(nodeCount = 30, edgeRatio = 0.15) {
    const nodes = [];
    const edges = [];
    
    // Node categories/groups for realistic clustering
    const nodeTypes = [
        { name: 'Core', group: 0, count: Math.floor(nodeCount * 0.2) },
        { name: 'Service', group: 1, count: Math.floor(nodeCount * 0.3) },
        { name: 'Data', group: 2, count: Math.floor(nodeCount * 0.25) },
        { name: 'Client', group: 3, count: Math.floor(nodeCount * 0.25) }
    ];
    
    // Generate nodes
    let nodeId = 0;
    nodeTypes.forEach(type => {
        for (let i = 0; i < type.count; i++) {
            nodes.push({
                id: nodeId++,
                label: `${type.name}-${i + 1}`,
                group: type.group,
                type: type.name.toLowerCase()
            });
        }
    });
    
    // Fill remaining nodes if needed
    while (nodes.length < nodeCount) {
        const randomType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
        nodes.push({
            id: nodeId++,
            label: `${randomType.name}-${nodes.filter(n => n.group === randomType.group).length + 1}`,
            group: randomType.group,
            type: randomType.name.toLowerCase()
        });
    }
    
    // Generate edges with realistic patterns
    const maxEdges = Math.floor(nodeCount * nodeCount * edgeRatio);
    const edgeSet = new Set(); // Prevent duplicate edges
    
    // Create some hub nodes (highly connected)
    const hubNodes = nodes.filter(n => n.group === 0); // Core nodes as hubs
    
    // Connect hubs to many other nodes
    hubNodes.forEach(hub => {
        const connectionCount = Math.floor(Math.random() * 8) + 5; // 5-12 connections
        const shuffledNodes = [...nodes].sort(() => Math.random() - 0.5);
        
        let connected = 0;
        for (const target of shuffledNodes) {
            if (target.id !== hub.id && connected < connectionCount) {
                const edgeKey = `${Math.min(hub.id, target.id)}-${Math.max(hub.id, target.id)}`;
                if (!edgeSet.has(edgeKey)) {
                    edges.push({
                        source: hub.id,
                        target: target.id,
                        weight: Math.random() * 0.5 + 0.5
                    });
                    edgeSet.add(edgeKey);
                    connected++;
                }
            }
        }
    });
    
    // Create cluster connections (nodes in same group connect more often)
    nodes.forEach(node => {
        const sameGroupNodes = nodes.filter(n => n.group === node.group && n.id !== node.id);
        const connectionProbability = 0.4; // 40% chance to connect to same group
        
        sameGroupNodes.forEach(target => {
            if (Math.random() < connectionProbability) {
                const edgeKey = `${Math.min(node.id, target.id)}-${Math.max(node.id, target.id)}`;
                if (!edgeSet.has(edgeKey)) {
                    edges.push({
                        source: node.id,
                        target: target.id,
                        weight: Math.random() * 0.3 + 0.7 // Higher weight for same group
                    });
                    edgeSet.add(edgeKey);
                }
            }
        });
    });
    
    // Add some random cross-group connections
    while (edges.length < maxEdges) {
        const source = nodes[Math.floor(Math.random() * nodes.length)];
        const target = nodes[Math.floor(Math.random() * nodes.length)];
        
        if (source.id !== target.id) {
            const edgeKey = `${Math.min(source.id, target.id)}-${Math.max(source.id, target.id)}`;
            if (!edgeSet.has(edgeKey)) {
                edges.push({
                    source: source.id,
                    target: target.id,
                    weight: Math.random() * 0.5 + 0.2
                });
                edgeSet.add(edgeKey);
            }
        }
    }
    
    return { nodes, edges };
}

// Generate specific network scenarios
export function generateSocialNetwork() {
    return generateNetworkData(25, 0.12);
}

export function generateTechNetwork() {
    const data = generateNetworkData(35, 0.18);
    
    // Rename groups to be more tech-focused
    data.nodes.forEach(node => {
        switch (node.group) {
            case 0:
                node.label = node.label.replace('Core', 'Server');
                break;
            case 1:
                node.label = node.label.replace('Service', 'API');
                break;
            case 2:
                node.label = node.label.replace('Data', 'Database');
                break;
            case 3:
                node.label = node.label.replace('Client', 'Frontend');
                break;
        }
    });
    
    return data;
}

export function generateBiologicalNetwork() {
    const data = generateNetworkData(40, 0.1);
    
    // Rename groups to be more biological
    data.nodes.forEach(node => {
        switch (node.group) {
            case 0:
                node.label = node.label.replace('Core', 'Protein');
                break;
            case 1:
                node.label = node.label.replace('Service', 'Gene');
                break;
            case 2:
                node.label = node.label.replace('Data', 'Metabolite');
                break;
            case 3:
                node.label = node.label.replace('Client', 'Pathway');
                break;
        }
    });
    
    return data;
}
