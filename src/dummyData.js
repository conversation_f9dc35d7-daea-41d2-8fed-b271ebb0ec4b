// Generate realistic dummy network data for testing

// Random words for tooltips
const randomWords = [
    'Algorithm', 'Database', 'Server', 'Client', 'Network', 'Protocol', 'Interface', 'Framework',
    'Library', 'Module', 'Component', 'Service', 'Gateway', 'Router', 'Cache', 'Queue',
    'Stream', 'Buffer', 'Token', 'Session', '<PERSON>ie', 'Header', 'Payload', 'Schema',
    'Index', 'Query', 'Transaction', 'Connection', 'Socket', 'Thread', 'Process', 'Memory',
    'Storage', 'Backup', 'Security', 'Authentication', 'Authorization', 'Encryption', 'Hash', 'Key',
    'Certificate', 'Firewall', 'Proxy', 'Load Balancer', 'Container', 'Virtual Machine', 'Cloud', 'Edge'
];

export function generateNetworkData(nodeCount = 30) {
    const nodes = [];
    const edges = [];

    // Create groups of 5-10 nodes each
    const groups = [];
    let nodeId = 0;
    let groupId = 0;

    while (nodeId < nodeCount) {
        const groupSize = Math.floor(Math.random() * 6) + 5; // 5-10 nodes per group
        const remainingNodes = nodeCount - nodeId;
        const actualGroupSize = Math.min(groupSize, remainingNodes);

        const group = {
            id: groupId,
            nodes: [],
            name: `Group-${groupId + 1}`
        };

        // Create nodes for this group
        for (let i = 0; i < actualGroupSize; i++) {
            const node = {
                id: nodeId++,
                label: `${group.name}-${i + 1}`,
                group: groupId,
                type: 'node',
                tooltip: randomWords[Math.floor(Math.random() * randomWords.length)]
            };
            nodes.push(node);
            group.nodes.push(node);
        }

        groups.push(group);
        groupId++;
    }

    // Generate edges - only connect nodes within the same group
    const edgeSet = new Set(); // Prevent duplicate edges

    groups.forEach(group => {
        const groupNodes = group.nodes;

        // Connect each node to 2-4 other nodes in the same group
        groupNodes.forEach(node => {
            const connectionCount = Math.min(
                Math.floor(Math.random() * 3) + 2, // 2-4 connections
                groupNodes.length - 1 // Can't exceed group size - 1
            );

            // Get other nodes in the group
            const otherNodes = groupNodes.filter(n => n.id !== node.id);

            // Shuffle and take the first connectionCount nodes
            const shuffled = [...otherNodes];
            shuffled.sort(() => Math.random() - 0.5);
            const targets = shuffled.slice(0, connectionCount);

            targets.forEach(target => {
                const edgeKey = `${Math.min(node.id, target.id)}-${Math.max(node.id, target.id)}`;
                if (!edgeSet.has(edgeKey)) {
                    edges.push({
                        source: node.id,
                        target: target.id,
                        weight: Math.random() * 0.5 + 0.5
                    });
                    edgeSet.add(edgeKey);
                }
            });
        });
    });
    
    return { nodes, edges };
}

// Generate specific network scenarios
export function generateSocialNetwork() {
    return generateNetworkData(25);
}

export function generateTechNetwork() {
    const data = generateNetworkData(35);

    // Rename groups to be more tech-focused
    data.nodes.forEach(node => {
        node.label = node.label.replace('Group', 'Team');
    });

    return data;
}

export function generateBiologicalNetwork() {
    const data = generateNetworkData(40);

    // Rename groups to be more biological
    data.nodes.forEach(node => {
        node.label = node.label.replace('Group', 'Cluster');
    });

    return data;
}
