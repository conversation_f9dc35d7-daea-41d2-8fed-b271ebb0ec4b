import * as PIXI from 'pixi.js';

export class NetworkGraph {
    constructor(container) {
        this.container = container;
        this.app = null;
        this.viewport = null;
        this.nodes = [];
        this.edges = [];
        this.nodeGraphics = new Map();
        this.edgeGraphics = new Map();
        
        // Physics simulation parameters - ultra stable with shape preservation
        this.simulation = {
            running: false,
            damping: 0.99, // Ultra high damping
            repulsion: 15, // Very low repulsion
            attraction: 0.001, // Minimal attraction
            centerForce: 0.0001, // Almost no center force
            minDistance: 8,
            velocityLimit: 0.2, // Extremely low velocity limit
            stabilityThreshold: 0.0001,
            equilibriumReached: false,
            frameCount: 0
        };
        
        // Visual parameters
        this.nodeRadius = 3; // Small points instead of circles
        this.nodeColors = {
            default: 0x4A90E2,
            hover: 0x7BB3F0,
            selected: 0xF5A623
        };
        this.edgeColor = 0x444444; // Fainter connections
        this.backgroundColor = 0x1a1a1a;
        
        this.init();
    }

    init() {
        // Create PIXI application
        this.app = new PIXI.Application({
            width: this.container.clientWidth,
            height: this.container.clientHeight,
            backgroundColor: this.backgroundColor,
            antialias: true,
            resolution: window.devicePixelRatio || 1,
            autoDensity: true
        });
        
        this.container.appendChild(this.app.view);
        
        // Create main container for graph elements
        this.graphContainer = new PIXI.Container();
        this.app.stage.addChild(this.graphContainer);

        // Center the container immediately
        this.graphContainer.x = this.app.screen.width / 2;
        this.graphContainer.y = this.app.screen.height / 2;

        // Create separate containers for edges and nodes (edges behind nodes)
        this.edgeContainer = new PIXI.Container();
        this.nodeContainer = new PIXI.Container();
        this.graphContainer.addChild(this.edgeContainer);
        this.graphContainer.addChild(this.nodeContainer);
        
        // Set up interaction
        this.setupInteraction();
        
        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());

        // Setup search functionality
        this.setupSearch();

        // Start animation loop
        this.app.ticker.add(() => this.update());
    }
    
    setupInteraction() {
        // Enable interaction on the stage
        this.app.stage.eventMode = 'static';
        this.app.stage.hitArea = new PIXI.Rectangle(0, 0, this.app.screen.width, this.app.screen.height);
        
        // Pan and zoom variables
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.lastPanPoint = { x: 0, y: 0 };

        // Node dragging variables
        this.draggedNode = null;
        this.dragOffset = { x: 0, y: 0 };

        // Tooltip
        this.tooltip = document.getElementById('tooltip');

        // Search functionality
        this.searchField = document.getElementById('search-field');
        this.nodeLabels = new Map(); // Store label elements for each node
        this.searchMatches = new Set(); // Track which nodes match current search
        
        // Mouse/touch events for panning
        this.app.stage.on('pointerdown', (event) => {
            if (event.target === this.app.stage) {
                this.isDragging = true;
                this.dragStart = { x: event.global.x, y: event.global.y };
                this.lastPanPoint = { x: this.graphContainer.x, y: this.graphContainer.y };
            }
        });
        
        this.app.stage.on('pointermove', (event) => {
            if (this.draggedNode) {
                // Handle node dragging
                const localPos = event.data.getLocalPosition(this.graphContainer);
                this.draggedNode.fx = localPos.x;
                this.draggedNode.fy = localPos.y;
                this.draggedNode.x = localPos.x;
                this.draggedNode.y = localPos.y;
            } else if (this.isDragging) {
                // Handle panning
                const dx = event.global.x - this.dragStart.x;
                const dy = event.global.y - this.dragStart.y;
                this.graphContainer.x = this.lastPanPoint.x + dx;
                this.graphContainer.y = this.lastPanPoint.y + dy;
            }
        });

        this.app.stage.on('pointerup', () => {
            if (this.draggedNode) {
                // Release the node and let it spring back
                this.draggedNode.fx = null;
                this.draggedNode.fy = null;
                this.draggedNode.isReturning = true; // Flag for spring-back behavior
                this.draggedNode = null;
            }
            this.isDragging = false;
        });

        this.app.stage.on('pointerupoutside', () => {
            if (this.draggedNode) {
                // Release the node and let it spring back
                this.draggedNode.fx = null;
                this.draggedNode.fy = null;
                this.draggedNode.isReturning = true; // Flag for spring-back behavior
                this.draggedNode = null;
            }
            this.isDragging = false;
        });
        
        // Zoom with mouse wheel
        this.app.view.addEventListener('wheel', (event) => {
            event.preventDefault();
            this.handleZoom(event.deltaY > 0 ? 0.9 : 1.1, event.clientX, event.clientY);
        });

        // Touch support for pinch-to-zoom
        this.setupTouchControls();
    }

    setupTouchControls() {
        let lastTouchDistance = 0;

        this.app.view.addEventListener('touchstart', (event) => {
            if (event.touches.length === 2) {
                event.preventDefault();
                const touch1 = event.touches[0];
                const touch2 = event.touches[1];

                lastTouchDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
            }
        });

        this.app.view.addEventListener('touchmove', (event) => {
            if (event.touches.length === 2) {
                event.preventDefault();
                const touch1 = event.touches[0];
                const touch2 = event.touches[1];

                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );

                const currentCenter = {
                    x: (touch1.clientX + touch2.clientX) / 2,
                    y: (touch1.clientY + touch2.clientY) / 2
                };

                if (lastTouchDistance > 0) {
                    const scaleFactor = currentDistance / lastTouchDistance;
                    this.handleZoom(scaleFactor, currentCenter.x, currentCenter.y);
                }

                lastTouchDistance = currentDistance;
            }
        });
    }

    setupSearch() {
        if (!this.searchField) return;

        this.searchField.addEventListener('input', (event) => {
            this.handleSearch(event.target.value);
        });

        this.searchField.addEventListener('keydown', (event) => {
            // Prevent space from triggering pan when typing
            if (event.code === 'Space') {
                event.stopPropagation();
            }
        });
    }

    handleSearch(searchTerm) {
        const term = searchTerm.toLowerCase().trim();

        // Clear previous matches
        this.searchMatches.clear();

        if (term === '') {
            // No search term - hide all labels and reset node appearance
            this.hideAllLabels();
            this.resetAllNodeAppearance();
            return;
        }

        // Find matching nodes
        this.nodes.forEach(node => {
            if (node.tooltip?.toLowerCase().includes(term)) {
                this.searchMatches.add(node);
            }
        });

        // Update visual state
        this.updateSearchResults();
    }

    hideAllLabels() {
        this.nodeLabels.forEach(label => {
            label?.parentNode?.removeChild(label);
        });
        this.nodeLabels.clear();
    }

    resetAllNodeAppearance() {
        this.nodes.forEach(node => {
            const graphic = this.nodeGraphics.get(node);
            if (graphic) {
                const color = this.getNodeColor(node);
                graphic.clear();
                graphic.beginFill(color);
                graphic.drawCircle(0, 0, this.nodeRadius);
                graphic.endFill();
            }
        });
    }

    updateSearchResults() {
        // Hide all labels first
        this.hideAllLabels();

        // Update all nodes based on search results
        this.nodes.forEach(node => {
            const graphic = this.nodeGraphics.get(node);
            if (!graphic) return;

            const baseColor = this.getNodeColor(node);

            if (this.searchMatches.has(node)) {
                // Highlight matching nodes - brighter version of original color
                const brightColor = this.brightenColor(baseColor);
                graphic.clear();
                graphic.beginFill(brightColor);
                graphic.drawCircle(0, 0, this.nodeRadius * 1.2); // Slightly larger
                graphic.endFill();

                // Show the label with highlighted text
                this.showNodeLabel(node);
            } else {
                // Dim non-matching nodes
                const dimColor = this.dimColor(baseColor);
                graphic.clear();
                graphic.beginFill(dimColor);
                graphic.drawCircle(0, 0, this.nodeRadius);
                graphic.endFill();
            }
        });
    }

    showNodeLabel(node) {
        const label = document.createElement('div');
        label.className = 'node-label';

        // Get the current search term
        const searchTerm = this.searchField?.value.toLowerCase().trim() || '';

        if (searchTerm && node.tooltip) {
            // Create HTML with bold matching text and normal weight non-matching text
            const tooltip = node.tooltip;
            const lowerTooltip = tooltip.toLowerCase();
            const index = lowerTooltip.indexOf(searchTerm);

            if (index !== -1) {
                const before = tooltip.substring(0, index);
                const match = tooltip.substring(index, index + searchTerm.length);
                const after = tooltip.substring(index + searchTerm.length);

                // Bold matches, dimmed normal weight for non-matching parts
                label.innerHTML = `<span style="font-weight: normal; opacity: 0.6;">${before}</span><strong>${match}</strong><span style="font-weight: normal; opacity: 0.6;">${after}</span>`;
            } else {
                // No match found, show normal weight
                label.innerHTML = `<span style="font-weight: normal;">${tooltip}</span>`;
            }
        } else {
            // No search term, show normal weight
            label.innerHTML = `<span style="font-weight: normal;">${node.tooltip || ''}</span>`;
        }

        document.body.appendChild(label);
        this.nodeLabels.set(node, label);

        // Position will be updated in updateVisuals
        this.updateNodeLabelPosition(node, label);
    }

    updateNodeLabelPosition(node, label) {
        if (!label) return;

        // Convert node position to screen coordinates
        const screenPos = this.nodeToScreenPosition(node);

        label.style.left = screenPos.x + 'px';
        label.style.top = (screenPos.y - 25) + 'px'; // Position above the node
    }

    nodeToScreenPosition(node) {
        // Transform node coordinates to screen coordinates
        const worldX = node.x * this.graphContainer.scale.x + this.graphContainer.x;
        const worldY = node.y * this.graphContainer.scale.y + this.graphContainer.y;

        return { x: worldX, y: worldY };
    }

    handleZoom(scaleFactor, clientX, clientY) {
        const newScale = this.graphContainer.scale.x * scaleFactor;

        // Limit zoom levels
        if (newScale < 0.1 || newScale > 3) return;

        // Get mouse/touch position relative to the container
        const rect = this.app.view.getBoundingClientRect();
        const pointX = clientX - rect.left;
        const pointY = clientY - rect.top;

        // Calculate zoom point
        const worldPos = {
            x: (pointX - this.graphContainer.x) / this.graphContainer.scale.x,
            y: (pointY - this.graphContainer.y) / this.graphContainer.scale.y
        };

        // Apply zoom
        this.graphContainer.scale.set(newScale);

        // Adjust position to zoom towards point
        this.graphContainer.x = pointX - worldPos.x * newScale;
        this.graphContainer.y = pointY - worldPos.y * newScale;
    }
    
    setData(nodes, edges) {
        console.log('setData called with', nodes.length, 'nodes');
        // Stop current simulation
        this.stopSimulation();

        // Clear existing data
        this.nodes = [];
        this.edges = [];

        // Process new data
        this.nodes = nodes.map((node, index) => ({
            ...node,
            id: node.id || index,
            x: 0,
            y: 0,
            vx: 0,
            vy: 0,
            fx: null, // fixed position x
            fy: null, // fixed position y
            originalX: 0, // store original position for spring-back
            originalY: 0,
            group: node.group || 0
        }));

        this.edges = edges.map(edge => ({
            ...edge,
            source: this.nodes.find(n => n.id === edge.source),
            target: this.nodes.find(n => n.id === edge.target)
        })).filter(edge => edge.source && edge.target);

        // Initialize layout and visuals
        this.initializeLayout();
        this.createVisuals();

        // Start simulation immediately - no delay
        console.log('Auto-starting simulation...');
        this.startSimulation();
        console.log('Simulation started immediately:', this.simulation.running);
    }
    
    initializeLayout() {
        // Group nodes by their group property
        const groups = {};
        this.nodes.forEach(node => {
            if (!groups[node.group]) {
                groups[node.group] = [];
            }
            groups[node.group].push(node);
        });

        const groupIds = Object.keys(groups);
        const numGroups = groupIds.length;
        const baseRadius = Math.min(this.app.screen.width, this.app.screen.height) * 0.15;

        // Arrange groups in a circle
        groupIds.forEach((groupId, groupIndex) => {
            const groupNodes = groups[groupId];
            const groupAngle = (groupIndex / numGroups) * Math.PI * 2;
            const groupCenterX = Math.cos(groupAngle) * baseRadius * 2;
            const groupCenterY = Math.sin(groupAngle) * baseRadius * 2;

            // Arrange nodes within each group in a circular cloud
            const groupRadius = Math.min(baseRadius * 0.8, 60); // Smaller radius for group clusters

            groupNodes.forEach((node, nodeIndex) => {
                if (groupNodes.length === 1) {
                    // Single node, place at group center
                    node.x = groupCenterX;
                    node.y = groupCenterY;
                    node.originalX = node.x;
                    node.originalY = node.y;
                } else {
                    // Multiple nodes, arrange in a cloud around the group center
                    const angle = (nodeIndex / groupNodes.length) * Math.PI * 2;
                    const distance = Math.random() * groupRadius * 0.7 + groupRadius * 0.3; // Random distance within group
                    const angleVariation = (Math.random() - 0.5) * 0.5; // Add some angle variation

                    node.x = groupCenterX + Math.cos(angle + angleVariation) * distance;
                    node.y = groupCenterY + Math.sin(angle + angleVariation) * distance;
                }

                // Store original position for spring-back
                node.originalX = node.x;
                node.originalY = node.y;

                // Start with minimal velocity
                node.vx = (Math.random() - 0.5) * 0.1;
                node.vy = (Math.random() - 0.5) * 0.1;
            });
        });

        // Center the view immediately
        this.centerView();
    }

    centerView() {
        // Reset scale first
        this.graphContainer.scale.set(1);

        // Center the container in the viewport
        this.graphContainer.x = this.app.screen.width / 2;
        this.graphContainer.y = this.app.screen.height / 2;

        // Stop any ongoing simulation temporarily to prevent jumping
        const wasRunning = this.simulation.running;
        this.simulation.running = false;

        // Restart simulation after a brief moment
        setTimeout(() => {
            this.simulation.running = wasRunning;
        }, 100);
    }

    createVisuals() {
        // Clear existing graphics
        this.nodeContainer.removeChildren();
        this.edgeContainer.removeChildren();
        this.nodeGraphics.clear();
        this.edgeGraphics.clear();

        // Create edge graphics
        this.edges.forEach((edge, index) => {
            const line = new PIXI.Graphics();
            line.lineStyle(1, this.edgeColor, 0.4); // Fainter opacity
            this.edgeContainer.addChild(line);
            this.edgeGraphics.set(edge, line);
        });

        // Create node graphics
        this.nodes.forEach((node, index) => {
            const nodeGraphic = new PIXI.Graphics();
            const color = this.getNodeColor(node);

            // Draw as a small point/circle
            nodeGraphic.beginFill(color);
            nodeGraphic.drawCircle(0, 0, this.nodeRadius);
            nodeGraphic.endFill();

            // Make interactive with larger hit area for easier clicking
            nodeGraphic.eventMode = 'static';
            nodeGraphic.cursor = 'pointer';
            nodeGraphic.hitArea = new PIXI.Circle(0, 0, this.nodeRadius + 5); // Larger hit area
            nodeGraphic.node = node;

            // Node interaction events - simplified
            nodeGraphic.on('pointerdown', (event) => this.onNodeDragStart(event, node));
            nodeGraphic.on('pointerover', () => this.onNodeHover(nodeGraphic, node));
            nodeGraphic.on('pointerout', () => this.onNodeOut(nodeGraphic, node));

            this.nodeContainer.addChild(nodeGraphic);
            this.nodeGraphics.set(node, nodeGraphic);
        });
    }

    getNodeColor(node) {
        // Color nodes based on their group
        const groupColors = [
            0x4A90E2, // Blue
            0xE24A4A, // Red
            0x4AE24A, // Green
            0xE2E24A, // Yellow
            0xE24AE2, // Magenta
            0x4AE2E2, // Cyan
            0xE2A04A, // Orange
            0xA04AE2  // Purple
        ];

        return groupColors[node.group % groupColors.length] || this.nodeColors.default;
    }

    brightenColor(color, factor = 1.3) {
        // Extract RGB components
        const r = (color >> 16) & 0xFF;
        const g = (color >> 8) & 0xFF;
        const b = color & 0xFF;

        // Brighten each component
        const newR = Math.min(255, Math.floor(r * factor));
        const newG = Math.min(255, Math.floor(g * factor));
        const newB = Math.min(255, Math.floor(b * factor));

        // Combine back to hex
        return (newR << 16) | (newG << 8) | newB;
    }

    dimColor(color, factor = 0.5) {
        // Extract RGB components
        const r = (color >> 16) & 0xFF;
        const g = (color >> 8) & 0xFF;
        const b = color & 0xFF;

        // Dim each component
        const newR = Math.floor(r * factor);
        const newG = Math.floor(g * factor);
        const newB = Math.floor(b * factor);

        // Combine back to hex
        return (newR << 16) | (newG << 8) | newB;
    }

    onNodeDragStart(event, node) {
        event.stopPropagation();
        this.draggedNode = node;
        node.fx = node.x;
        node.fy = node.y;
    }

    onNodeHover(nodeGraphic, node) {
        // Update visual appearance
        nodeGraphic.clear();
        nodeGraphic.beginFill(this.nodeColors.hover);
        nodeGraphic.drawCircle(0, 0, this.nodeRadius * 1.5);
        nodeGraphic.endFill();

        // Show tooltip
        this.showTooltip(node);
    }

    onNodeOut(nodeGraphic, node) {
        // Reset visual appearance
        const color = this.getNodeColor(node);
        nodeGraphic.clear();
        nodeGraphic.beginFill(color);
        nodeGraphic.drawCircle(0, 0, this.nodeRadius);
        nodeGraphic.endFill();

        // Hide tooltip
        this.hideTooltip();
    }

    showTooltip(node) {
        if (!this.tooltip || !node.tooltip) return;

        this.tooltip.textContent = node.tooltip;
        this.tooltip.classList.add('visible');

        // Position tooltip near mouse
        document.addEventListener('mousemove', this.updateTooltipPosition);
    }

    hideTooltip() {
        if (!this.tooltip) return;

        this.tooltip.classList.remove('visible');
        document.removeEventListener('mousemove', this.updateTooltipPosition);
    }

    updateTooltipPosition = (event) => {
        if (!this.tooltip) return;

        const offset = 10;
        this.tooltip.style.left = (event.clientX + offset) + 'px';
        this.tooltip.style.top = (event.clientY - this.tooltip.offsetHeight - offset) + 'px';
    }

    startSimulation() {
        this.simulation.running = true;
        this.simulation.frameCount = 0;
        this.simulation.equilibriumReached = false;
        console.log('Simulation started, running:', this.simulation.running);
    }

    stopSimulation() {
        console.log('Stopping simulation');
        this.simulation.running = false;
    }

    update() {
        if (this.simulation.running) {
            this.updatePhysics();
        } else {
            // Debug: log when simulation should be running but isn't
            if (this.nodes.length > 0 && this.simulation.frameCount < 10) {
                console.log('Simulation not running but should be, frameCount:', this.simulation.frameCount);
            }
        }
        this.updateVisuals();
    }

    updatePhysics() {
        const { repulsion, attraction, velocityLimit, damping, stabilityThreshold } = this.simulation;

        this.simulation.frameCount++;

        // Debug: log every 60 frames (1 second) to confirm simulation is running
        if (this.simulation.frameCount % 60 === 0) {
            console.log('Simulation running, frame:', this.simulation.frameCount);
        }

        let totalMovement = 0;
        let totalVelocity = 0;

        // Apply forces to each node
        this.nodes.forEach(node => {
            if (node.fx !== null && node.fy !== null) {
                // Node is fixed (being dragged)
                node.x = node.fx;
                node.y = node.fy;
                node.vx = 0;
                node.vy = 0;
                return;
            }

            let fx = 0, fy = 0;

            // Very gentle repulsion between nodes in same group only
            this.nodes.forEach(other => {
                if (node === other || node.group !== other.group) return;

                const dx = node.x - other.x;
                const dy = node.y - other.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > 0 && distance < 30) {
                    const force = repulsion / Math.max(distance * distance, 1);
                    const normalizedForce = Math.min(force, 0.1); // Very small cap
                    fx += (dx / distance) * normalizedForce;
                    fy += (dy / distance) * normalizedForce;
                }
            });

            // Very gentle attraction along edges
            this.edges.forEach(edge => {
                if (edge.source === node) {
                    const dx = edge.target.x - node.x;
                    const dy = edge.target.y - node.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const idealDistance = 35;

                    if (distance > 0) {
                        const force = (distance - idealDistance) * attraction;
                        fx += (dx / distance) * Math.min(Math.abs(force), 0.05) * Math.sign(force);
                        fy += (dy / distance) * Math.min(Math.abs(force), 0.05) * Math.sign(force);
                    }
                } else if (edge.target === node) {
                    const dx = edge.source.x - node.x;
                    const dy = edge.source.y - node.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const idealDistance = 35;

                    if (distance > 0) {
                        const force = (distance - idealDistance) * attraction;
                        fx += (dx / distance) * Math.min(Math.abs(force), 0.05) * Math.sign(force);
                        fy += (dy / distance) * Math.min(Math.abs(force), 0.05) * Math.sign(force);
                    }
                }
            });

            // Strong anchor force to original position (prevents drift and flattening)
            const anchorForce = 0.01; // Stronger anchor
            fx += (node.originalX - node.x) * anchorForce;
            fy += (node.originalY - node.y) * anchorForce;

            // Spring-back force to original position (when returning from drag)
            if (node.isReturning) {
                const springForce = 0.05;
                const dx = node.originalX - node.x;
                const dy = node.originalY - node.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                fx += dx * springForce;
                fy += dy * springForce;

                if (distance < 3) {
                    node.isReturning = false;
                }
            }

            // Cap the total force very strictly
            const totalForce = Math.sqrt(fx * fx + fy * fy);
            if (totalForce > 0.1) { // Very small force cap
                fx = (fx / totalForce) * 0.1;
                fy = (fy / totalForce) * 0.1;
            }

            // Update velocity with ultra-strong damping
            node.vx = (node.vx + fx) * damping;
            node.vy = (node.vy + fy) * damping;

            // Strict velocity limiting
            const velocity = Math.sqrt(node.vx * node.vx + node.vy * node.vy);
            if (velocity > velocityLimit) {
                node.vx = (node.vx / velocity) * velocityLimit;
                node.vy = (node.vy / velocity) * velocityLimit;
            }

            // Update position
            const oldX = node.x;
            const oldY = node.y;
            node.x += node.vx;
            node.y += node.vy;

            // Track movement and velocity
            totalMovement += Math.abs(node.x - oldX) + Math.abs(node.y - oldY);
            totalVelocity += Math.abs(node.vx) + Math.abs(node.vy);
        });

        // Gradual velocity reduction after initial settling
        if (this.simulation.frameCount > 300) { // After 5 seconds
            this.nodes.forEach(node => {
                node.vx *= 0.95; // Very gradually reduce velocities
                node.vy *= 0.95;
            });
        }

        // Don't stop the simulation automatically - let it run continuously
        if (totalMovement < stabilityThreshold && totalVelocity < stabilityThreshold * 2) {
            this.simulation.equilibriumReached = true;
            // Just reduce velocities slightly, don't stop
            this.nodes.forEach(node => {
                node.vx *= 0.9;
                node.vy *= 0.9;
            });
        }
    }

    updateVisuals() {
        // Update node positions
        this.nodes.forEach(node => {
            const graphic = this.nodeGraphics.get(node);
            if (graphic) {
                graphic.x = node.x;
                graphic.y = node.y;
            }

            // Update label position if it exists
            const label = this.nodeLabels.get(node);
            if (label) {
                this.updateNodeLabelPosition(node, label);
            }
        });

        // Update edge positions
        this.edges.forEach(edge => {
            const line = this.edgeGraphics.get(edge);
            if (line && edge.source && edge.target) {
                line.clear();
                line.lineStyle(1, this.edgeColor, 0.4); // Fainter opacity
                line.moveTo(edge.source.x, edge.source.y);
                line.lineTo(edge.target.x, edge.target.y);
            }
        });
    }

    handleResize() {
        const oldWidth = this.app.screen.width;
        const oldHeight = this.app.screen.height;

        this.app.renderer.resize(this.container.clientWidth, this.container.clientHeight);
        this.app.stage.hitArea = new PIXI.Rectangle(0, 0, this.app.screen.width, this.app.screen.height);

        // Adjust container position to maintain centering
        const newWidth = this.app.screen.width;
        const newHeight = this.app.screen.height;

        if (oldWidth > 0 && oldHeight > 0) {
            // Adjust the container position based on the size change
            this.graphContainer.x += (newWidth - oldWidth) / 2;
            this.graphContainer.y += (newHeight - oldHeight) / 2;
        } else {
            // Initial resize, center the container
            this.centerView();
        }
    }

    destroy() {
        if (this.app) {
            this.app.destroy(true);
        }
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('mousemove', this.updateTooltipPosition);
        this.hideTooltip();
        this.hideAllLabels();
    }
}
