import * as PIXI from 'pixi.js';

export class NetworkGraph {
    constructor(container) {
        this.container = container;
        this.app = null;
        this.viewport = null;
        this.nodes = [];
        this.edges = [];
        this.nodeGraphics = new Map();
        this.edgeGraphics = new Map();
        
        // Physics simulation parameters - fast convergence
        this.simulation = {
            running: false,
            damping: 0.95,
            repulsion: 800,
            attraction: 0.05,
            centerForce: 0.02,
            minDistance: 40,
            iterations: 0,
            maxIterations: 60 // ~1 second at 60fps
        };
        
        // Visual parameters
        this.nodeRadius = 8;
        this.nodeColors = {
            default: 0x4A90E2,
            hover: 0x7BB3F0,
            selected: 0xF5A623
        };
        this.edgeColor = 0x666666;
        this.backgroundColor = 0x1a1a1a;
        
        this.init();
    }

    init() {
        // Create PIXI application
        this.app = new PIXI.Application({
            width: this.container.clientWidth,
            height: this.container.clientHeight,
            backgroundColor: this.backgroundColor,
            antialias: true,
            resolution: window.devicePixelRatio || 1,
            autoDensity: true
        });
        
        this.container.appendChild(this.app.view);
        
        // Create main container for graph elements
        this.graphContainer = new PIXI.Container();
        this.app.stage.addChild(this.graphContainer);

        // Center the container immediately
        this.graphContainer.x = this.app.screen.width / 2;
        this.graphContainer.y = this.app.screen.height / 2;

        // Create separate containers for edges and nodes (edges behind nodes)
        this.edgeContainer = new PIXI.Container();
        this.nodeContainer = new PIXI.Container();
        this.graphContainer.addChild(this.edgeContainer);
        this.graphContainer.addChild(this.nodeContainer);
        
        // Set up interaction
        this.setupInteraction();
        
        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());
        
        // Start animation loop
        this.app.ticker.add(() => this.update());
    }
    
    setupInteraction() {
        // Enable interaction on the stage
        this.app.stage.interactive = true;
        this.app.stage.hitArea = new PIXI.Rectangle(0, 0, this.app.screen.width, this.app.screen.height);
        
        // Pan and zoom variables
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.lastPanPoint = { x: 0, y: 0 };
        
        // Mouse/touch events for panning
        this.app.stage.on('pointerdown', (event) => {
            if (event.target === this.app.stage) {
                this.isDragging = true;
                this.dragStart = { x: event.global.x, y: event.global.y };
                this.lastPanPoint = { x: this.graphContainer.x, y: this.graphContainer.y };
            }
        });
        
        this.app.stage.on('pointermove', (event) => {
            if (this.isDragging) {
                const dx = event.global.x - this.dragStart.x;
                const dy = event.global.y - this.dragStart.y;
                this.graphContainer.x = this.lastPanPoint.x + dx;
                this.graphContainer.y = this.lastPanPoint.y + dy;
            }
        });
        
        this.app.stage.on('pointerup', () => {
            this.isDragging = false;
        });
        
        this.app.stage.on('pointerupoutside', () => {
            this.isDragging = false;
        });
        
        // Zoom with mouse wheel
        this.app.view.addEventListener('wheel', (event) => {
            event.preventDefault();
            this.handleZoom(event.deltaY > 0 ? 0.9 : 1.1, event.clientX, event.clientY);
        });

        // Touch support for pinch-to-zoom
        this.setupTouchControls();
    }

    setupTouchControls() {
        let lastTouchDistance = 0;

        this.app.view.addEventListener('touchstart', (event) => {
            if (event.touches.length === 2) {
                event.preventDefault();
                const touch1 = event.touches[0];
                const touch2 = event.touches[1];

                lastTouchDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
            }
        });

        this.app.view.addEventListener('touchmove', (event) => {
            if (event.touches.length === 2) {
                event.preventDefault();
                const touch1 = event.touches[0];
                const touch2 = event.touches[1];

                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );

                const currentCenter = {
                    x: (touch1.clientX + touch2.clientX) / 2,
                    y: (touch1.clientY + touch2.clientY) / 2
                };

                if (lastTouchDistance > 0) {
                    const scaleFactor = currentDistance / lastTouchDistance;
                    this.handleZoom(scaleFactor, currentCenter.x, currentCenter.y);
                }

                lastTouchDistance = currentDistance;
            }
        });
    }

    handleZoom(scaleFactor, clientX, clientY) {
        const newScale = this.graphContainer.scale.x * scaleFactor;

        // Limit zoom levels
        if (newScale < 0.1 || newScale > 3) return;

        // Get mouse/touch position relative to the container
        const rect = this.app.view.getBoundingClientRect();
        const pointX = clientX - rect.left;
        const pointY = clientY - rect.top;

        // Calculate zoom point
        const worldPos = {
            x: (pointX - this.graphContainer.x) / this.graphContainer.scale.x,
            y: (pointY - this.graphContainer.y) / this.graphContainer.scale.y
        };

        // Apply zoom
        this.graphContainer.scale.set(newScale);

        // Adjust position to zoom towards point
        this.graphContainer.x = pointX - worldPos.x * newScale;
        this.graphContainer.y = pointY - worldPos.y * newScale;
    }
    
    setData(nodes, edges) {
        // Stop current simulation
        this.stopSimulation();

        // Clear existing data
        this.nodes = [];
        this.edges = [];

        // Process new data
        this.nodes = nodes.map((node, index) => ({
            ...node,
            id: node.id || index,
            x: 0,
            y: 0,
            vx: 0,
            vy: 0,
            fx: null, // fixed position x
            fy: null, // fixed position y
            group: node.group || 0
        }));

        this.edges = edges.map(edge => ({
            ...edge,
            source: this.nodes.find(n => n.id === edge.source),
            target: this.nodes.find(n => n.id === edge.target)
        })).filter(edge => edge.source && edge.target);

        // Initialize layout and visuals
        this.initializeLayout();
        this.createVisuals();

        // Start simulation immediately for fast convergence
        setTimeout(() => {
            this.startSimulation();
        }, 50);
    }
    
    initializeLayout() {
        console.log('Initializing layout with', this.nodes.length, 'nodes');
        console.log('Screen dimensions:', this.app.screen.width, 'x', this.app.screen.height);

        // Arrange nodes in a circle initially
        const centerX = 0;
        const centerY = 0;
        const radius = Math.min(this.app.screen.width, this.app.screen.height) * 0.2;

        console.log('Circle radius:', radius);

        this.nodes.forEach((node, index) => {
            const angle = (index / this.nodes.length) * Math.PI * 2;
            node.x = centerX + Math.cos(angle) * radius;
            node.y = centerY + Math.sin(angle) * radius;

            // Start with minimal velocity
            node.vx = (Math.random() - 0.5) * 0.1;
            node.vy = (Math.random() - 0.5) * 0.1;
        });

        console.log('First node position:', this.nodes[0]?.x, this.nodes[0]?.y);

        // Center the view immediately
        this.centerView();
    }

    centerView() {
        // Reset scale first
        this.graphContainer.scale.set(1);

        // Center the container in the viewport
        this.graphContainer.x = this.app.screen.width / 2;
        this.graphContainer.y = this.app.screen.height / 2;

        // Stop any ongoing simulation temporarily to prevent jumping
        const wasRunning = this.simulation.running;
        this.simulation.running = false;

        // Restart simulation after a brief moment
        setTimeout(() => {
            this.simulation.running = wasRunning;
        }, 100);
    }

    createVisuals() {
        // Clear existing graphics
        this.nodeContainer.removeChildren();
        this.edgeContainer.removeChildren();
        this.nodeGraphics.clear();
        this.edgeGraphics.clear();

        // Create edge graphics
        this.edges.forEach((edge, index) => {
            const line = new PIXI.Graphics();
            line.lineStyle(2, this.edgeColor, 0.6);
            this.edgeContainer.addChild(line);
            this.edgeGraphics.set(edge, line);
        });

        // Create node graphics
        this.nodes.forEach((node, index) => {
            const nodeGraphic = new PIXI.Graphics();
            const color = this.getNodeColor(node);

            nodeGraphic.beginFill(color);
            nodeGraphic.drawCircle(0, 0, this.nodeRadius);
            nodeGraphic.endFill();

            // Add border
            nodeGraphic.lineStyle(2, 0xffffff, 0.3);
            nodeGraphic.drawCircle(0, 0, this.nodeRadius);

            // Make interactive
            nodeGraphic.interactive = true;
            nodeGraphic.buttonMode = true;
            nodeGraphic.node = node;

            // Node interaction events
            nodeGraphic.on('pointerdown', (event) => this.onNodeDragStart(event, node));
            nodeGraphic.on('pointermove', (event) => this.onNodeDrag(event, node));
            nodeGraphic.on('pointerup', (event) => this.onNodeDragEnd(event, node));
            nodeGraphic.on('pointerupoutside', (event) => this.onNodeDragEnd(event, node));
            nodeGraphic.on('pointerover', () => this.onNodeHover(nodeGraphic, node));
            nodeGraphic.on('pointerout', () => this.onNodeOut(nodeGraphic, node));

            this.nodeContainer.addChild(nodeGraphic);
            this.nodeGraphics.set(node, nodeGraphic);
        });
    }

    getNodeColor(node) {
        // Color nodes based on their group
        const groupColors = [
            0x4A90E2, // Blue
            0xE24A4A, // Red
            0x4AE24A, // Green
            0xE2E24A, // Yellow
            0xE24AE2, // Magenta
            0x4AE2E2, // Cyan
            0xE2A04A, // Orange
            0xA04AE2  // Purple
        ];

        return groupColors[node.group % groupColors.length] || this.nodeColors.default;
    }

    onNodeDragStart(event, node) {
        event.stopPropagation();
        node.isDragging = true;
        node.fx = node.x;
        node.fy = node.y;
        this.dragData = event.data;
    }

    onNodeDrag(event, node) {
        if (node.isDragging && this.dragData) {
            const newPosition = this.dragData.getLocalPosition(this.graphContainer);
            node.fx = newPosition.x;
            node.fy = newPosition.y;
            node.x = newPosition.x;
            node.y = newPosition.y;
        }
    }

    onNodeDragEnd(event, node) {
        node.isDragging = false;
        node.fx = null;
        node.fy = null;
        this.dragData = null;
    }

    onNodeHover(nodeGraphic, node) {
        nodeGraphic.clear();
        nodeGraphic.beginFill(this.nodeColors.hover);
        nodeGraphic.drawCircle(0, 0, this.nodeRadius * 1.2);
        nodeGraphic.endFill();
        nodeGraphic.lineStyle(2, 0xffffff, 0.5);
        nodeGraphic.drawCircle(0, 0, this.nodeRadius * 1.2);
    }

    onNodeOut(nodeGraphic, node) {
        const color = this.getNodeColor(node);
        nodeGraphic.clear();
        nodeGraphic.beginFill(color);
        nodeGraphic.drawCircle(0, 0, this.nodeRadius);
        nodeGraphic.endFill();
        nodeGraphic.lineStyle(2, 0xffffff, 0.3);
        nodeGraphic.drawCircle(0, 0, this.nodeRadius);
    }

    startSimulation() {
        this.simulation.running = true;
        this.simulation.iterations = 0;
    }

    stopSimulation() {
        this.simulation.running = false;
    }

    update() {
        if (this.simulation.running) {
            this.updatePhysics();
        }
        this.updateVisuals();
    }

    updatePhysics() {
        const { repulsion, attraction, centerForce, minDistance } = this.simulation;

        // Increment iteration counter
        this.simulation.iterations++;

        // Adaptive damping - starts low, increases over time to reach equilibrium quickly
        const progress = Math.min(this.simulation.iterations / this.simulation.maxIterations, 1);
        const adaptiveDamping = 0.7 + (progress * 0.25); // 0.7 to 0.95

        // Apply forces to each node
        this.nodes.forEach(node => {
            if (node.fx !== null && node.fy !== null) {
                // Node is fixed (being dragged)
                node.x = node.fx;
                node.y = node.fy;
                node.vx = 0;
                node.vy = 0;
                return;
            }

            let fx = 0, fy = 0;

            // Repulsion force between nodes
            this.nodes.forEach(other => {
                if (node === other) return;

                const dx = node.x - other.x;
                const dy = node.y - other.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > 0 && distance < 200) {
                    const force = repulsion / (distance * distance);
                    fx += (dx / distance) * force;
                    fy += (dy / distance) * force;
                }
            });

            // Attraction force along edges
            this.edges.forEach(edge => {
                if (edge.source === node) {
                    const dx = edge.target.x - node.x;
                    const dy = edge.target.y - node.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance > minDistance) {
                        fx += dx * attraction;
                        fy += dy * attraction;
                    }
                } else if (edge.target === node) {
                    const dx = edge.source.x - node.x;
                    const dy = edge.source.y - node.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance > minDistance) {
                        fx += dx * attraction;
                        fy += dy * attraction;
                    }
                }
            });

            // Center force (weak attraction to origin)
            fx += -node.x * centerForce;
            fy += -node.y * centerForce;

            // Update velocity and position with adaptive damping
            node.vx = (node.vx + fx) * adaptiveDamping;
            node.vy = (node.vy + fy) * adaptiveDamping;

            node.x += node.vx;
            node.y += node.vy;

            // Keep nodes within reasonable bounds (prevent flying off to infinity)
            const maxDistance = 1000;
            if (Math.abs(node.x) > maxDistance) {
                node.x = Math.sign(node.x) * maxDistance;
                node.vx *= -0.5;
            }
            if (Math.abs(node.y) > maxDistance) {
                node.y = Math.sign(node.y) * maxDistance;
                node.vy *= -0.5;
            }
        });

        // Check if simulation should stop (reached equilibrium or max iterations)
        if (this.simulation.iterations >= this.simulation.maxIterations) {
            const totalVelocity = this.nodes.reduce((sum, node) =>
                sum + Math.abs(node.vx) + Math.abs(node.vy), 0);

            if (totalVelocity < 0.1 || this.simulation.iterations > this.simulation.maxIterations * 2) {
                this.simulation.running = false;
                console.log('Simulation stopped at equilibrium after', this.simulation.iterations, 'iterations');
            }
        }
    }

    updateVisuals() {
        // Update node positions
        this.nodes.forEach(node => {
            const graphic = this.nodeGraphics.get(node);
            if (graphic) {
                graphic.x = node.x;
                graphic.y = node.y;
            }
        });

        // Update edge positions
        this.edges.forEach(edge => {
            const line = this.edgeGraphics.get(edge);
            if (line && edge.source && edge.target) {
                line.clear();
                line.lineStyle(2, this.edgeColor, 0.6);
                line.moveTo(edge.source.x, edge.source.y);
                line.lineTo(edge.target.x, edge.target.y);
            }
        });
    }

    handleResize() {
        const oldWidth = this.app.screen.width;
        const oldHeight = this.app.screen.height;

        this.app.renderer.resize(this.container.clientWidth, this.container.clientHeight);
        this.app.stage.hitArea = new PIXI.Rectangle(0, 0, this.app.screen.width, this.app.screen.height);

        // Adjust container position to maintain centering
        const newWidth = this.app.screen.width;
        const newHeight = this.app.screen.height;

        if (oldWidth > 0 && oldHeight > 0) {
            // Adjust the container position based on the size change
            this.graphContainer.x += (newWidth - oldWidth) / 2;
            this.graphContainer.y += (newHeight - oldHeight) / 2;
        } else {
            // Initial resize, center the container
            this.centerView();
        }
    }

    destroy() {
        if (this.app) {
            this.app.destroy(true);
        }
        window.removeEventListener('resize', this.handleResize);
    }
}
