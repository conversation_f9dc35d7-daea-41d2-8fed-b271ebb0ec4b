import { NetworkGraph } from './NetworkGraph.js';
import { generateTechNetwork } from './dummyData.js';

// Initialize the application
async function init() {
    const container = document.getElementById('graph-container');
    
    if (!container) {
        console.error('Graph container not found');
        return;
    }
    
    // Create network graph instance
    const networkGraph = new NetworkGraph(container);

    // Generate and load dummy data
    const data = generateTechNetwork();
    console.log('Generated network data:', data);
    
    networkGraph.setData(data.nodes, data.edges);

    // Ensure simulation starts after a brief delay
    setTimeout(() => {
        if (!networkGraph.simulation.running) {
            console.log('Simulation not running after setData, force starting...');
            networkGraph.startSimulation();
        } else {
            console.log('Simulation confirmed running after setData');
        }
    }, 100);

    // Add some controls for testing
    addControls(networkGraph);
    
    // Make networkGraph globally accessible for debugging
    window.networkGraph = networkGraph;
}

function addControls(networkGraph) {
    const controlsDiv = document.querySelector('.controls');

    // Add simple generate button
    const controls = document.createElement('div');
    controls.innerHTML = `
        <h4 style="margin: 15px 0 10px 0; color: #fff; font-size: 12px;">Controls:</h4>
        <button id="generate-new" style="margin: 2px; padding: 8px 16px; font-size: 12px; background: #4A90E2; color: #fff; border: 1px solid #357ABD; border-radius: 4px; cursor: pointer;">Generate New Network</button>
        <button id="center-view" style="margin: 2px; padding: 8px 16px; font-size: 12px; background: #333; color: #fff; border: 1px solid #555; border-radius: 4px; cursor: pointer;">Center View</button>
    `;

    controlsDiv.appendChild(controls);

    // Add event listeners
    document.getElementById('generate-new').addEventListener('click', async () => {
        const { generateTechNetwork } = await import('./dummyData.js');
        const data = generateTechNetwork();
        networkGraph.setData(data.nodes, data.edges);
    });

    document.getElementById('center-view').addEventListener('click', () => {
        networkGraph.centerView();
    });

    // Add hover effects to buttons
    const buttons = controlsDiv.querySelectorAll('button');
    buttons.forEach(button => {
        const originalBg = button.style.background;
        button.addEventListener('mouseenter', () => {
            if (button.id === 'generate-new') {
                button.style.background = '#357ABD';
            } else {
                button.style.background = '#555';
            }
        });
        button.addEventListener('mouseleave', () => {
            button.style.background = originalBg;
        });
    });
}

// Handle any errors
window.addEventListener('error', (event) => {
    console.error('Application error:', event.error);
});

// Start the application
init().catch(console.error);
