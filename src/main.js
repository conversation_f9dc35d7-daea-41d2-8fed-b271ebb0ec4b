import { NetworkGraph } from './NetworkGraph.js';
import { generateTechNetwork } from './dummyData.js';

// Initialize the application
async function init() {
    const container = document.getElementById('graph-container');
    
    if (!container) {
        console.error('Graph container not found');
        return;
    }
    
    // Create network graph instance
    const networkGraph = new NetworkGraph(container);

    // Generate and load dummy data
    const data = generateTechNetwork();
    console.log('Generated network data:', data);
    
    networkGraph.setData(data.nodes, data.edges);

    // Ensure simulation starts after a brief delay
    setTimeout(() => {
        if (!networkGraph.simulation.running) {
            console.log('Simulation not running after setData, force starting...');
            networkGraph.startSimulation();
        } else {
            console.log('Simulation confirmed running after setData');
        }
    }, 100);

    // Add some controls for testing
    addControls(networkGraph);
    
    // Make networkGraph globally accessible for debugging
    window.networkGraph = networkGraph;
}

function addControls(networkGraph) {
    const controlsDiv = document.querySelector('.controls');
    
    // Add data generation buttons
    const dataControls = document.createElement('div');
    dataControls.innerHTML = `
        <h4 style="margin: 15px 0 10px 0; color: #fff; font-size: 12px;">Data Sets:</h4>
        <button id="tech-data" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #333; color: #fff; border: 1px solid #555; border-radius: 3px; cursor: pointer;">Tech Network</button>
        <button id="social-data" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #333; color: #fff; border: 1px solid #555; border-radius: 3px; cursor: pointer;">Social Network</button>
        <button id="bio-data" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #333; color: #fff; border: 1px solid #555; border-radius: 3px; cursor: pointer;">Bio Network</button>
    `;
    
    const simulationControls = document.createElement('div');
    simulationControls.innerHTML = `
        <h4 style="margin: 15px 0 10px 0; color: #fff; font-size: 12px;">Simulation:</h4>
        <button id="start-sim" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #333; color: #fff; border: 1px solid #555; border-radius: 3px; cursor: pointer;">Start</button>
        <button id="stop-sim" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #333; color: #fff; border: 1px solid #555; border-radius: 3px; cursor: pointer;">Stop</button>
        <button id="center-view" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #333; color: #fff; border: 1px solid #555; border-radius: 3px; cursor: pointer;">Center</button>
    `;
    
    controlsDiv.appendChild(dataControls);
    controlsDiv.appendChild(simulationControls);
    
    // Add event listeners
    document.getElementById('tech-data').addEventListener('click', async () => {
        const { generateTechNetwork } = await import('./dummyData.js');
        const data = generateTechNetwork();
        networkGraph.setData(data.nodes, data.edges);
    });
    
    document.getElementById('social-data').addEventListener('click', async () => {
        const { generateSocialNetwork } = await import('./dummyData.js');
        const data = generateSocialNetwork();
        networkGraph.setData(data.nodes, data.edges);
    });
    
    document.getElementById('bio-data').addEventListener('click', async () => {
        const { generateBiologicalNetwork } = await import('./dummyData.js');
        const data = generateBiologicalNetwork();
        networkGraph.setData(data.nodes, data.edges);
    });
    
    document.getElementById('start-sim').addEventListener('click', () => {
        networkGraph.startSimulation();
    });
    
    document.getElementById('stop-sim').addEventListener('click', () => {
        networkGraph.stopSimulation();
    });
    
    document.getElementById('center-view').addEventListener('click', () => {
        networkGraph.centerView();
    });
    
    // Add hover effects to buttons
    const buttons = controlsDiv.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.background = '#555';
        });
        button.addEventListener('mouseleave', () => {
            button.style.background = '#333';
        });
    });
}

// Handle any errors
window.addEventListener('error', (event) => {
    console.error('Application error:', event.error);
});

// Start the application
init().catch(console.error);
