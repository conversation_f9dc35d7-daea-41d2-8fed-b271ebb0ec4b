<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Graph</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #app {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #graph-container {
            width: 100%;
            height: 100%;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .controls h3 {
            margin-bottom: 10px;
            color: #fff;
            font-size: 14px;
        }
        
        .controls p {
            font-size: 12px;
            color: #ccc;
            margin: 5px 0;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
            border: 1px solid #444;
        }

        .tooltip.visible {
            opacity: 1;
        }

        .search-container {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
        }

        .search-field {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #555;
            border-radius: 20px;
            padding: 8px 16px;
            color: #fff;
            font-size: 14px;
            width: 300px;
            outline: none;
            transition: border-color 0.2s;
        }

        .search-field:focus {
            border-color: #4A90E2;
        }

        .search-field::placeholder {
            color: #999;
        }

        .node-label {
            position: absolute;
            color: #fff;
            font-size: 11px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 50;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.6);
            padding: 2px 6px;
            border-radius: 3px;
            white-space: nowrap;
        }

        .fps-display {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #ccc;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-family: monospace;
            z-index: 100;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="search-container">
            <input type="text" id="search-field" class="search-field" placeholder="Search nodes..." />
        </div>
        <div id="graph-container"></div>
        <div class="controls">
            <h3>Network Graph</h3>
            <p>• Drag to pan</p>
            <p>• Scroll/pinch to zoom</p>
            <p>• Drag nodes to move them</p>
            <p>• Hover for tooltips</p>
            <p>• Search to highlight nodes</p>
        </div>
        <div id="tooltip" class="tooltip"></div>
        <div id="fps-display" class="fps-display">FPS: --</div>
    </div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
